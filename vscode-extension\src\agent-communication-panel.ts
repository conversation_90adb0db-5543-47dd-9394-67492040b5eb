import * as vscode from 'vscode';
import * as path from 'path';
import { WorkspaceManager } from './workspace-manager';

export interface Agent {
  id: string;
  name: string;
  type: AgentType;
  status: AgentStatus;
  capabilities: string[];
  currentTask?: string;
  progress: number;
  lastActivity: Date;
  avatar?: string;
  description: string;
}

export enum AgentType {
  ANALYST = 'analyst',
  ARCHITECT = 'architect',
  DEVELOPER = 'developer',
  TESTER = 'tester',
  REVIEWER = 'reviewer',
  OPTIMIZER = 'optimizer',
  DOCUMENTER = 'documenter'
}

export enum AgentStatus {
  IDLE = 'idle',
  WORKING = 'working',
  WAITING_FOR_FEEDBACK = 'waiting_for_feedback',
  BLOCKED = 'blocked',
  COMPLETED = 'completed',
  ERROR = 'error'
}

export interface Message {
  id: string;
  agentId: string;
  userId?: string;
  content: string;
  type: MessageType;
  timestamp: Date;
  attachments?: MessageAttachment[];
  metadata?: Record<string, any>;
  parentMessageId?: string;
  reactions?: MessageReaction[];
}

export enum MessageType {
  USER_MESSAGE = 'user_message',
  AGENT_MESSAGE = 'agent_message',
  SYSTEM_MESSAGE = 'system_message',
  FEEDBACK_REQUEST = 'feedback_request',
  TASK_UPDATE = 'task_update',
  ERROR_REPORT = 'error_report',
  SUGGESTION = 'suggestion',
  QUESTION = 'question'
}

export interface MessageAttachment {
  id: string;
  type: 'file' | 'code' | 'image' | 'link' | 'data';
  name: string;
  content: string;
  metadata?: Record<string, any>;
}

export interface MessageReaction {
  emoji: string;
  userId: string;
  timestamp: Date;
}

export interface FeedbackRequest {
  id: string;
  agentId: string;
  title: string;
  description: string;
  options?: FeedbackOption[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  deadline?: Date;
  context: Record<string, any>;
  status: 'pending' | 'responded' | 'expired';
}

export interface FeedbackOption {
  id: string;
  label: string;
  description?: string;
  value: any;
}

export interface AgentGuidance {
  agentId: string;
  instruction: string;
  priority: number;
  context?: Record<string, any>;
  timestamp: Date;
}

export class AgentCommunicationPanel {
  private panel: vscode.WebviewPanel | undefined;
  private context: vscode.ExtensionContext;
  private workspaceManager: WorkspaceManager;
  private agents: Map<string, Agent> = new Map();
  private messages: Map<string, Message[]> = new Map();
  private feedbackRequests: Map<string, FeedbackRequest> = new Map();
  private activeConversations: Set<string> = new Set();

  constructor(context: vscode.ExtensionContext, workspaceManager: WorkspaceManager) {
    this.context = context;
    this.workspaceManager = workspaceManager;
    this.initializeDefaultAgents();
  }

  /**
   * Initialize default agents
   */
  private initializeDefaultAgents(): void {
    const defaultAgents: Agent[] = [
      {
        id: 'analyst-001',
        name: 'Alex Analyst',
        type: AgentType.ANALYST,
        status: AgentStatus.IDLE,
        capabilities: ['requirement_analysis', 'user_story_creation', 'market_research'],
        progress: 0,
        lastActivity: new Date(),
        avatar: '🔍',
        description: 'Specializes in analyzing requirements and creating detailed project specifications'
      },
      {
        id: 'architect-001',
        name: 'Aria Architect',
        type: AgentType.ARCHITECT,
        status: AgentStatus.IDLE,
        capabilities: ['system_design', 'technology_selection', 'architecture_planning'],
        progress: 0,
        lastActivity: new Date(),
        avatar: '🏗️',
        description: 'Designs system architecture and selects appropriate technologies'
      },
      {
        id: 'developer-001',
        name: 'Dev Developer',
        type: AgentType.DEVELOPER,
        status: AgentStatus.IDLE,
        capabilities: ['code_generation', 'implementation', 'debugging'],
        progress: 0,
        lastActivity: new Date(),
        avatar: '💻',
        description: 'Implements features and writes high-quality code'
      },
      {
        id: 'tester-001',
        name: 'Tessa Tester',
        type: AgentType.TESTER,
        status: AgentStatus.IDLE,
        capabilities: ['test_creation', 'quality_assurance', 'bug_detection'],
        progress: 0,
        lastActivity: new Date(),
        avatar: '🧪',
        description: 'Creates comprehensive tests and ensures code quality'
      },
      {
        id: 'reviewer-001',
        name: 'Rex Reviewer',
        type: AgentType.REVIEWER,
        status: AgentStatus.IDLE,
        capabilities: ['code_review', 'best_practices', 'security_audit'],
        progress: 0,
        lastActivity: new Date(),
        avatar: '👁️',
        description: 'Reviews code for quality, security, and best practices'
      }
    ];

    defaultAgents.forEach(agent => {
      this.agents.set(agent.id, agent);
      this.messages.set(agent.id, []);
    });
  }

  /**
   * Show the agent communication panel
   */
  async showPanel(): Promise<void> {
    if (this.panel) {
      this.panel.reveal(vscode.ViewColumn.Two);
      return;
    }

    this.panel = vscode.window.createWebviewPanel(
      'aetherforge.agentCommunication',
      'Agent Communication',
      vscode.ViewColumn.Two,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context.extensionPath, 'webview', 'dist'))
        ]
      }
    );

    this.panel.webview.html = this.getWebviewContent();
    this.setupMessageHandling();

    this.panel.onDidDispose(() => {
      this.panel = undefined;
    });

    // Send initial data
    this.sendToWebview('agents.list', Array.from(this.agents.values()));
  }

  /**
   * Setup message handling between webview and extension
   */
  private setupMessageHandling(): void {
    if (!this.panel) return;

    this.panel.webview.onDidReceiveMessage(
      async (message) => {
        try {
          switch (message.command) {
            case 'agent.sendMessage':
              await this.handleSendMessage(message.data);
              break;

            case 'agent.provideFeedback':
              await this.handleProvideFeedback(message.data);
              break;

            case 'agent.provideGuidance':
              await this.handleProvideGuidance(message.data);
              break;

            case 'agent.getMessages':
              await this.handleGetMessages(message.data.agentId);
              break;

            case 'agent.getStatus':
              await this.handleGetAgentStatus(message.data.agentId);
              break;

            case 'agent.startConversation':
              await this.handleStartConversation(message.data.agentId);
              break;

            case 'agent.endConversation':
              await this.handleEndConversation(message.data.agentId);
              break;

            case 'feedback.respond':
              await this.handleFeedbackResponse(message.data);
              break;

            case 'message.react':
              await this.handleMessageReaction(message.data);
              break;

            case 'agent.interrupt':
              await this.handleAgentInterrupt(message.data.agentId);
              break;

            case 'project.getContext':
              await this.handleGetProjectContext();
              break;
          }
        } catch (error) {
          this.sendToWebview('error', {
            message: error instanceof Error ? error.message : String(error),
            command: message.command
          });
        }
      },
      undefined,
      this.context.subscriptions
    );
  }

  /**
   * Handle sending a message to an agent
   */
  private async handleSendMessage(data: {
    agentId: string;
    content: string;
    type?: MessageType;
    attachments?: MessageAttachment[];
  }): Promise<void> {
    const agent = this.agents.get(data.agentId);
    if (!agent) {
      throw new Error(`Agent ${data.agentId} not found`);
    }

    const message: Message = {
      id: this.generateMessageId(),
      agentId: data.agentId,
      userId: 'user',
      content: data.content,
      type: data.type || MessageType.USER_MESSAGE,
      timestamp: new Date(),
      attachments: data.attachments,
      reactions: []
    };

    // Add message to conversation
    const agentMessages = this.messages.get(data.agentId) || [];
    agentMessages.push(message);
    this.messages.set(data.agentId, agentMessages);

    // Mark conversation as active
    this.activeConversations.add(data.agentId);

    // Update agent status
    agent.lastActivity = new Date();
    if (agent.status === AgentStatus.IDLE) {
      agent.status = AgentStatus.WORKING;
    }

    // Send message to webview
    this.sendToWebview('message.received', message);

    // Simulate agent response (in real implementation, this would communicate with actual agents)
    setTimeout(() => {
      this.simulateAgentResponse(data.agentId, message);
    }, 1000 + Math.random() * 2000);
  }

  /**
   * Send message to orchestrator and handle agent response
   */
  private async simulateAgentResponse(agentId: string, userMessage: Message): Promise<void> {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    try {
      // Try to send to actual orchestrator first
      const orchestratorResponse = await this.sendMessageToOrchestrator(agentId, userMessage);

      if (orchestratorResponse) {
        const agentMessage: Message = {
          id: orchestratorResponse.messageId || this.generateMessageId(),
          agentId: agentId,
          content: orchestratorResponse.content,
          type: orchestratorResponse.type || MessageType.AGENT_MESSAGE,
          timestamp: new Date(orchestratorResponse.timestamp || Date.now()),
          parentMessageId: userMessage.id,
          reactions: [],
          attachments: orchestratorResponse.attachments,
          metadata: orchestratorResponse.metadata
        };

        // Add agent response to conversation
        const agentMessages = this.messages.get(agentId) || [];
        agentMessages.push(agentMessage);
        this.messages.set(agentId, agentMessages);

        // Update agent status from orchestrator response
        if (orchestratorResponse.agentStatus) {
          Object.assign(agent, orchestratorResponse.agentStatus);
        } else {
          agent.lastActivity = new Date();
          agent.progress = Math.min(100, agent.progress + (orchestratorResponse.progressIncrement || 10));
        }

        // Send response to webview
        this.sendToWebview('message.received', agentMessage);
        this.sendToWebview('agent.updated', agent);

        // Handle feedback requests from orchestrator
        if (orchestratorResponse.feedbackRequest) {
          setTimeout(() => {
            this.createFeedbackRequestFromOrchestrator(agentId, orchestratorResponse.feedbackRequest);
          }, 1000);
        }

        return;
      }
    } catch (error) {
      console.warn(`Failed to communicate with orchestrator for agent ${agentId}:`, error);
      // Fall back to simulation
    }

    // Fallback to simulated response for development/offline mode
    await this.generateSimulatedResponse(agentId, userMessage);
  }

  /**
   * Send message to orchestrator backend
   */
  private async sendMessageToOrchestrator(agentId: string, userMessage: Message): Promise<any> {
    // Validate inputs
    if (!agentId || typeof agentId !== 'string') {
      throw new Error('Invalid agent ID provided');
    }

    if (!userMessage || !userMessage.content || typeof userMessage.content !== 'string') {
      throw new Error('Invalid message content provided');
    }

    if (userMessage.content.trim().length === 0) {
      throw new Error('Message content cannot be empty');
    }

    if (userMessage.content.length > 10000) {
      throw new Error('Message content too long (max 10000 characters)');
    }

    try {
      const projectContext = await this.getProjectContext();

      const requestData = {
        agentId,
        message: userMessage.content.trim(),
        messageType: userMessage.type,
        messageId: userMessage.id,
        timestamp: userMessage.timestamp.toISOString(),
        context: {
          conversationHistory: this.messages.get(agentId)?.slice(-5) || [], // Last 5 messages for context
          agentStatus: this.agents.get(agentId),
          projectContext
        }
      };

      // Send to orchestrator via VS Code command
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Orchestrator communication timeout (10s)'));
        }, 10000); // 10 second timeout

        vscode.commands.executeCommand('aetherforge.sendAgentMessage', requestData)
          .then((response) => {
            clearTimeout(timeout);

            // Validate response
            if (!response) {
              reject(new Error('Empty response from orchestrator'));
              return;
            }

            if (typeof response !== 'object') {
              reject(new Error('Invalid response format from orchestrator'));
              return;
            }

            resolve(response);
          })
          .catch((error) => {
            clearTimeout(timeout);

            // Enhance error information
            const enhancedError = new Error(`Orchestrator communication failed: ${error.message || error}`);
            enhancedError.name = 'OrchestratorCommunicationError';
            reject(enhancedError);
          });
      });
    } catch (error) {
      throw new Error(`Failed to prepare orchestrator request: ${error.message || error}`);
    }
  }

  /**
   * Generate simulated response (fallback)
   */
  private async generateSimulatedResponse(agentId: string, userMessage: Message): Promise<void> {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    const responses = this.getAgentResponses(agent.type, userMessage.content);
    const response = responses[Math.floor(Math.random() * responses.length)];

    const agentMessage: Message = {
      id: this.generateMessageId(),
      agentId: agentId,
      content: response.content,
      type: response.type,
      timestamp: new Date(),
      parentMessageId: userMessage.id,
      reactions: [],
      attachments: response.attachments
    };

    // Add agent response to conversation
    const agentMessages = this.messages.get(agentId) || [];
    agentMessages.push(agentMessage);
    this.messages.set(agentId, agentMessages);

    // Update agent status
    agent.lastActivity = new Date();
    agent.progress = Math.min(100, agent.progress + Math.random() * 20);

    // Send response to webview
    this.sendToWebview('message.received', agentMessage);
    this.sendToWebview('agent.updated', agent);

    // Sometimes create feedback requests
    if (Math.random() < 0.3) {
      setTimeout(() => {
        this.createFeedbackRequest(agentId);
      }, 2000);
    }
  }

  /**
   * Get appropriate responses based on agent type and user message
   */
  private getAgentResponses(agentType: AgentType, userMessage: string): Array<{
    content: string;
    type: MessageType;
    attachments?: MessageAttachment[];
  }> {
    const responses: Record<AgentType, Array<{
      content: string;
      type: MessageType;
      attachments?: MessageAttachment[];
    }>> = {
      [AgentType.ANALYST]: [
        {
          content: "I've analyzed your requirements. Let me break down the key user stories and acceptance criteria.",
          type: MessageType.AGENT_MESSAGE,
          attachments: [{
            id: 'analysis-1',
            type: 'data',
            name: 'Requirements Analysis',
            content: JSON.stringify({
              userStories: 5,
              acceptanceCriteria: 12,
              priority: 'high'
            })
          }]
        },
        {
          content: "Based on market research, I recommend focusing on these core features first. Would you like me to prioritize them?",
          type: MessageType.SUGGESTION
        }
      ],
      [AgentType.ARCHITECT]: [
        {
          content: "I've designed a scalable architecture for your project. Here's the system overview and technology stack recommendations.",
          type: MessageType.AGENT_MESSAGE,
          attachments: [{
            id: 'arch-1',
            type: 'code',
            name: 'Architecture Diagram',
            content: '// System Architecture\n// Frontend: React + TypeScript\n// Backend: Node.js + Express\n// Database: PostgreSQL'
          }]
        },
        {
          content: "The current architecture supports horizontal scaling. Should I add microservices patterns for better modularity?",
          type: MessageType.QUESTION
        }
      ],
      [AgentType.DEVELOPER]: [
        {
          content: "I've implemented the core functionality. The code follows best practices and includes proper error handling.",
          type: MessageType.AGENT_MESSAGE,
          attachments: [{
            id: 'code-1',
            type: 'file',
            name: 'main.ts',
            content: 'export class MainService {\n  // Implementation details\n}'
          }]
        },
        {
          content: "I need clarification on the authentication flow. Should I implement OAuth2 or JWT-based authentication?",
          type: MessageType.QUESTION
        }
      ],
      [AgentType.TESTER]: [
        {
          content: "I've created comprehensive test suites covering unit, integration, and e2e tests. Coverage is at 95%.",
          type: MessageType.AGENT_MESSAGE
        },
        {
          content: "Found a potential edge case in the user input validation. Should I create additional test scenarios?",
          type: MessageType.FEEDBACK_REQUEST
        }
      ],
      [AgentType.REVIEWER]: [
        {
          content: "Code review completed. The implementation looks solid with minor suggestions for optimization.",
          type: MessageType.AGENT_MESSAGE
        },
        {
          content: "I noticed some security considerations. Would you like me to implement additional security measures?",
          type: MessageType.SUGGESTION
        }
      ],
      [AgentType.OPTIMIZER]: [
        {
          content: "Performance analysis complete. I've identified several optimization opportunities.",
          type: MessageType.AGENT_MESSAGE
        }
      ],
      [AgentType.DOCUMENTER]: [
        {
          content: "Documentation has been updated with the latest changes. All APIs are properly documented.",
          type: MessageType.AGENT_MESSAGE
        }
      ]
    };

    return responses[agentType] || [{
      content: "I'm working on your request. I'll update you with progress soon.",
      type: MessageType.AGENT_MESSAGE
    }];
  }

  /**
   * Get current project context for orchestrator communication
   */
  private async getProjectContext(): Promise<any> {
    try {
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      return {
        workspacePath: workspaceFolder?.uri.fsPath,
        workspaceName: workspaceFolder?.name,
        activeFile: vscode.window.activeTextEditor?.document.fileName,
        openFiles: vscode.workspace.textDocuments.map(doc => doc.fileName),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.warn('Failed to get project context:', error);
      return {};
    }
  }

  /**
   * Create feedback request from orchestrator response
   */
  private createFeedbackRequestFromOrchestrator(agentId: string, feedbackData: any): void {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    const feedbackRequest: FeedbackRequest = {
      id: feedbackData.id || this.generateMessageId(),
      agentId: agentId,
      title: feedbackData.title || `Feedback needed from ${agent.name}`,
      description: feedbackData.description || this.getFeedbackDescription(agent.type),
      options: feedbackData.options || this.getFeedbackOptions(agent.type),
      priority: feedbackData.priority || 'medium',
      context: feedbackData.context || { agentType: agent.type, currentTask: agent.currentTask },
      status: 'pending'
    };

    this.feedbackRequests.set(feedbackRequest.id, feedbackRequest);
    agent.status = AgentStatus.WAITING_FOR_FEEDBACK;

    this.sendToWebview('feedback.request', feedbackRequest);
    this.sendToWebview('agent.updated', agent);
  }

  /**
   * Create a feedback request from an agent
   */
  private createFeedbackRequest(agentId: string): void {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    const feedbackRequest: FeedbackRequest = {
      id: this.generateMessageId(),
      agentId: agentId,
      title: `Feedback needed from ${agent.name}`,
      description: this.getFeedbackDescription(agent.type),
      options: this.getFeedbackOptions(agent.type),
      priority: 'medium',
      context: { agentType: agent.type, currentTask: agent.currentTask },
      status: 'pending'
    };

    this.feedbackRequests.set(feedbackRequest.id, feedbackRequest);
    agent.status = AgentStatus.WAITING_FOR_FEEDBACK;

    this.sendToWebview('feedback.request', feedbackRequest);
    this.sendToWebview('agent.updated', agent);
  }

  /**
   * Get feedback description based on agent type
   */
  private getFeedbackDescription(agentType: AgentType): string {
    const descriptions: Record<AgentType, string> = {
      [AgentType.ANALYST]: "I need your input on the priority of user stories. Which features should we implement first?",
      [AgentType.ARCHITECT]: "Should I optimize for performance or maintainability in the current architecture design?",
      [AgentType.DEVELOPER]: "I have multiple implementation approaches. Which one aligns better with your vision?",
      [AgentType.TESTER]: "I found edge cases that need additional testing. How should we handle these scenarios?",
      [AgentType.REVIEWER]: "The code quality is good, but I have suggestions for improvements. Should I proceed?",
      [AgentType.OPTIMIZER]: "I can optimize for speed or memory usage. What's your priority?",
      [AgentType.DOCUMENTER]: "Should I focus on API documentation or user guides first?"
    };

    return descriptions[agentType] || "I need your guidance to proceed with the current task.";
  }

  /**
   * Get feedback options based on agent type
   */
  private getFeedbackOptions(agentType: AgentType): FeedbackOption[] {
    const options: Record<AgentType, FeedbackOption[]> = {
      [AgentType.ANALYST]: [
        { id: 'priority-high', label: 'High Priority Features', value: 'high' },
        { id: 'priority-user', label: 'User-Requested Features', value: 'user' },
        { id: 'priority-business', label: 'Business Value Features', value: 'business' }
      ],
      [AgentType.ARCHITECT]: [
        { id: 'optimize-performance', label: 'Optimize for Performance', value: 'performance' },
        { id: 'optimize-maintainability', label: 'Optimize for Maintainability', value: 'maintainability' },
        { id: 'optimize-scalability', label: 'Optimize for Scalability', value: 'scalability' }
      ],
      [AgentType.DEVELOPER]: [
        { id: 'approach-simple', label: 'Simple Implementation', value: 'simple' },
        { id: 'approach-robust', label: 'Robust Implementation', value: 'robust' },
        { id: 'approach-flexible', label: 'Flexible Implementation', value: 'flexible' }
      ],
      [AgentType.TESTER]: [
        { id: 'test-comprehensive', label: 'Comprehensive Testing', value: 'comprehensive' },
        { id: 'test-focused', label: 'Focused Testing', value: 'focused' },
        { id: 'test-automated', label: 'Automated Testing', value: 'automated' }
      ],
      [AgentType.REVIEWER]: [
        { id: 'review-approve', label: 'Approve Changes', value: 'approve' },
        { id: 'review-improve', label: 'Request Improvements', value: 'improve' },
        { id: 'review-refactor', label: 'Suggest Refactoring', value: 'refactor' }
      ],
      [AgentType.OPTIMIZER]: [
        { id: 'optimize-speed', label: 'Optimize for Speed', value: 'speed' },
        { id: 'optimize-memory', label: 'Optimize for Memory', value: 'memory' },
        { id: 'optimize-balanced', label: 'Balanced Optimization', value: 'balanced' }
      ],
      [AgentType.DOCUMENTER]: [
        { id: 'doc-api', label: 'API Documentation', value: 'api' },
        { id: 'doc-user', label: 'User Documentation', value: 'user' },
        { id: 'doc-developer', label: 'Developer Documentation', value: 'developer' }
      ]
    };

    return options[agentType] || [
      { id: 'continue', label: 'Continue', value: 'continue' },
      { id: 'pause', label: 'Pause', value: 'pause' }
    ];
  }

  /**
   * Handle providing feedback to an agent
   */
  private async handleProvideFeedback(data: {
    feedbackId: string;
    response: any;
    comment?: string;
  }): Promise<void> {
    const feedbackRequest = this.feedbackRequests.get(data.feedbackId);
    if (!feedbackRequest) {
      throw new Error(`Feedback request ${data.feedbackId} not found`);
    }

    feedbackRequest.status = 'responded';
    const agent = this.agents.get(feedbackRequest.agentId);

    if (agent) {
      agent.status = AgentStatus.WORKING;
      agent.lastActivity = new Date();

      // Create feedback response message
      const feedbackMessage: Message = {
        id: this.generateMessageId(),
        agentId: feedbackRequest.agentId,
        userId: 'user',
        content: `Feedback provided: ${data.response}${data.comment ? ` - ${data.comment}` : ''}`,
        type: MessageType.USER_MESSAGE,
        timestamp: new Date(),
        metadata: { feedbackId: data.feedbackId, response: data.response },
        reactions: []
      };

      const agentMessages = this.messages.get(feedbackRequest.agentId) || [];
      agentMessages.push(feedbackMessage);
      this.messages.set(feedbackRequest.agentId, agentMessages);

      this.sendToWebview('message.received', feedbackMessage);
      this.sendToWebview('agent.updated', agent);
      this.sendToWebview('feedback.responded', { feedbackId: data.feedbackId, response: data.response });

      // Simulate agent acknowledgment
      setTimeout(() => {
        this.sendAgentAcknowledgment(feedbackRequest.agentId, data.response);
      }, 1000);
    }
  }

  /**
   * Handle providing guidance to an agent
   */
  private async handleProvideGuidance(data: {
    agentId: string;
    instruction: string;
    priority: number;
    context?: Record<string, any>;
  }): Promise<void> {
    const agent = this.agents.get(data.agentId);
    if (!agent) {
      throw new Error(`Agent ${data.agentId} not found`);
    }

    const guidance: AgentGuidance = {
      agentId: data.agentId,
      instruction: data.instruction,
      priority: data.priority,
      context: data.context,
      timestamp: new Date()
    };

    // Create guidance message
    const guidanceMessage: Message = {
      id: this.generateMessageId(),
      agentId: data.agentId,
      userId: 'user',
      content: `Guidance: ${data.instruction}`,
      type: MessageType.USER_MESSAGE,
      timestamp: new Date(),
      metadata: { guidance: true, priority: data.priority },
      reactions: []
    };

    const agentMessages = this.messages.get(data.agentId) || [];
    agentMessages.push(guidanceMessage);
    this.messages.set(data.agentId, agentMessages);

    agent.lastActivity = new Date();
    if (agent.status === AgentStatus.IDLE) {
      agent.status = AgentStatus.WORKING;
    }

    this.sendToWebview('message.received', guidanceMessage);
    this.sendToWebview('agent.updated', agent);
    this.sendToWebview('guidance.provided', guidance);

    // Simulate agent response to guidance
    setTimeout(() => {
      this.sendAgentGuidanceResponse(data.agentId, data.instruction);
    }, 1500);
  }

  /**
   * Send agent acknowledgment of feedback
   */
  private sendAgentAcknowledgment(agentId: string, response: any): void {
    const acknowledgmentMessage: Message = {
      id: this.generateMessageId(),
      agentId: agentId,
      content: `Thank you for the feedback! I'll proceed with "${response}" approach and update you on progress.`,
      type: MessageType.AGENT_MESSAGE,
      timestamp: new Date(),
      reactions: []
    };

    const agentMessages = this.messages.get(agentId) || [];
    agentMessages.push(acknowledgmentMessage);
    this.messages.set(agentId, agentMessages);

    this.sendToWebview('message.received', acknowledgmentMessage);
  }

  /**
   * Send agent response to guidance
   */
  private sendAgentGuidanceResponse(agentId: string, instruction: string): void {
    const responseMessage: Message = {
      id: this.generateMessageId(),
      agentId: agentId,
      content: `Understood! I'll follow your guidance: "${instruction}". Adjusting my approach accordingly.`,
      type: MessageType.AGENT_MESSAGE,
      timestamp: new Date(),
      reactions: []
    };

    const agentMessages = this.messages.get(agentId) || [];
    agentMessages.push(responseMessage);
    this.messages.set(agentId, agentMessages);

    this.sendToWebview('message.received', responseMessage);
  }

  /**
   * Handle getting messages for an agent
   */
  private async handleGetMessages(agentId: string): Promise<void> {
    const messages = this.messages.get(agentId) || [];
    this.sendToWebview('messages.list', { agentId, messages });
  }

  /**
   * Handle getting agent status
   */
  private async handleGetAgentStatus(agentId: string): Promise<void> {
    const agent = this.agents.get(agentId);
    if (agent) {
      this.sendToWebview('agent.status', agent);
    }
  }

  /**
   * Handle starting a conversation with an agent
   */
  private async handleStartConversation(agentId: string): Promise<void> {
    this.activeConversations.add(agentId);
    const agent = this.agents.get(agentId);

    if (agent && agent.status === AgentStatus.IDLE) {
      agent.status = AgentStatus.WORKING;
      agent.lastActivity = new Date();
      this.sendToWebview('agent.updated', agent);
    }

    // Send welcome message from agent
    const welcomeMessage: Message = {
      id: this.generateMessageId(),
      agentId: agentId,
      content: this.getWelcomeMessage(agent?.type || AgentType.DEVELOPER),
      type: MessageType.AGENT_MESSAGE,
      timestamp: new Date(),
      reactions: []
    };

    const agentMessages = this.messages.get(agentId) || [];
    agentMessages.push(welcomeMessage);
    this.messages.set(agentId, agentMessages);

    this.sendToWebview('conversation.started', { agentId });
    this.sendToWebview('message.received', welcomeMessage);
  }

  /**
   * Handle ending a conversation with an agent
   */
  private async handleEndConversation(agentId: string): Promise<void> {
    this.activeConversations.delete(agentId);
    const agent = this.agents.get(agentId);

    if (agent && agent.status === AgentStatus.WORKING) {
      agent.status = AgentStatus.IDLE;
      this.sendToWebview('agent.updated', agent);
    }

    this.sendToWebview('conversation.ended', { agentId });
  }

  /**
   * Handle feedback response
   */
  private async handleFeedbackResponse(data: {
    feedbackId: string;
    optionId: string;
    comment?: string;
  }): Promise<void> {
    const feedbackRequest = this.feedbackRequests.get(data.feedbackId);
    if (!feedbackRequest) {
      throw new Error(`Feedback request ${data.feedbackId} not found`);
    }

    const selectedOption = feedbackRequest.options?.find(opt => opt.id === data.optionId);
    await this.handleProvideFeedback({
      feedbackId: data.feedbackId,
      response: selectedOption?.value || data.optionId,
      comment: data.comment
    });
  }

  /**
   * Handle message reaction
   */
  private async handleMessageReaction(data: {
    messageId: string;
    emoji: string;
    agentId: string;
  }): Promise<void> {
    const agentMessages = this.messages.get(data.agentId) || [];
    const message = agentMessages.find(msg => msg.id === data.messageId);

    if (message) {
      if (!message.reactions) {
        message.reactions = [];
      }

      const existingReaction = message.reactions.find(r => r.userId === 'user' && r.emoji === data.emoji);
      if (existingReaction) {
        // Remove reaction
        message.reactions = message.reactions.filter(r => !(r.userId === 'user' && r.emoji === data.emoji));
      } else {
        // Add reaction
        message.reactions.push({
          emoji: data.emoji,
          userId: 'user',
          timestamp: new Date()
        });
      }

      this.sendToWebview('message.reacted', { messageId: data.messageId, reactions: message.reactions });
    }
  }

  /**
   * Handle agent interrupt
   */
  private async handleAgentInterrupt(agentId: string): Promise<void> {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = AgentStatus.IDLE;
      agent.currentTask = undefined;
      agent.lastActivity = new Date();

      const interruptMessage: Message = {
        id: this.generateMessageId(),
        agentId: agentId,
        content: "Task interrupted by user. Ready for new instructions.",
        type: MessageType.SYSTEM_MESSAGE,
        timestamp: new Date(),
        reactions: []
      };

      const agentMessages = this.messages.get(agentId) || [];
      agentMessages.push(interruptMessage);
      this.messages.set(agentId, agentMessages);

      this.sendToWebview('agent.updated', agent);
      this.sendToWebview('message.received', interruptMessage);
    }
  }

  /**
   * Handle getting project context
   */
  private async handleGetProjectContext(): Promise<void> {
    const workspaceInfo = this.workspaceManager.getWorkspaceInfo();
    const context = {
      workspace: workspaceInfo,
      activeAgents: Array.from(this.agents.values()).filter(agent =>
        this.activeConversations.has(agent.id)
      ),
      totalMessages: Array.from(this.messages.values()).reduce((total, msgs) => total + msgs.length, 0),
      pendingFeedback: Array.from(this.feedbackRequests.values()).filter(req => req.status === 'pending')
    };

    this.sendToWebview('project.context', context);
  }

  /**
   * Get welcome message based on agent type
   */
  private getWelcomeMessage(agentType: AgentType): string {
    const welcomeMessages: Record<AgentType, string> = {
      [AgentType.ANALYST]: "Hi! I'm Alex, your requirements analyst. I'll help you define and prioritize your project requirements. What would you like to build?",
      [AgentType.ARCHITECT]: "Hello! I'm Aria, your system architect. I'll design the technical architecture for your project. Tell me about your requirements and constraints.",
      [AgentType.DEVELOPER]: "Hey there! I'm Dev, your developer. I'll implement the features and write clean, maintainable code. What should we build first?",
      [AgentType.TESTER]: "Hi! I'm Tessa, your quality assurance specialist. I'll ensure your code is thoroughly tested and bug-free. Ready to create some tests?",
      [AgentType.REVIEWER]: "Hello! I'm Rex, your code reviewer. I'll review your code for quality, security, and best practices. Let's make your code shine!",
      [AgentType.OPTIMIZER]: "Hi! I'm your optimization specialist. I'll help improve performance and efficiency. What needs optimization?",
      [AgentType.DOCUMENTER]: "Hello! I'm your documentation specialist. I'll help create clear, comprehensive documentation. What needs documenting?"
    };

    return welcomeMessages[agentType] || "Hello! I'm ready to help with your project. How can I assist you today?";
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Send message to webview
   */
  private sendToWebview(command: string, data: any): void {
    if (this.panel) {
      this.panel.webview.postMessage({ command, data });
    }
  }

  /**
   * Get webview HTML content
   */
  private getWebviewContent(): string {
    const scriptUri = this.panel?.webview.asWebviewUri(
      vscode.Uri.file(path.join(this.context.extensionPath, 'webview', 'dist', 'agentCommunication.js'))
    );

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Communication</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            background: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            margin: 0;
            padding: 0;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">Loading Agent Communication Panel...</div>
    </div>
    <script src="${scriptUri}"></script>
</body>
</html>`;
  }

  /**
   * Update agent status
   */
  updateAgentStatus(agentId: string, status: AgentStatus, currentTask?: string, progress?: number): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = status;
      agent.lastActivity = new Date();
      if (currentTask !== undefined) {
        agent.currentTask = currentTask;
      }
      if (progress !== undefined) {
        agent.progress = progress;
      }

      this.sendToWebview('agent.updated', agent);
    }
  }

  /**
   * Send message from agent to user
   */
  sendAgentMessage(agentId: string, content: string, type: MessageType = MessageType.AGENT_MESSAGE, attachments?: MessageAttachment[]): void {
    const message: Message = {
      id: this.generateMessageId(),
      agentId: agentId,
      content: content,
      type: type,
      timestamp: new Date(),
      attachments: attachments,
      reactions: []
    };

    const agentMessages = this.messages.get(agentId) || [];
    agentMessages.push(message);
    this.messages.set(agentId, agentMessages);

    this.sendToWebview('message.received', message);

    // Update agent activity
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.lastActivity = new Date();
      this.sendToWebview('agent.updated', agent);
    }
  }

  /**
   * Request feedback from user
   */
  requestFeedback(agentId: string, title: string, description: string, options?: FeedbackOption[], priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'): string {
    const feedbackRequest: FeedbackRequest = {
      id: this.generateMessageId(),
      agentId: agentId,
      title: title,
      description: description,
      options: options,
      priority: priority,
      context: {},
      status: 'pending'
    };

    this.feedbackRequests.set(feedbackRequest.id, feedbackRequest);

    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = AgentStatus.WAITING_FOR_FEEDBACK;
      this.sendToWebview('agent.updated', agent);
    }

    this.sendToWebview('feedback.request', feedbackRequest);
    return feedbackRequest.id;
  }

  /**
   * Get all agents
   */
  getAgents(): Agent[] {
    return Array.from(this.agents.values());
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: string): Agent | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Dispose panel
   */
  dispose(): void {
    if (this.panel) {
      this.panel.dispose();
      this.panel = undefined;
    }
  }
}
