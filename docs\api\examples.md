# Aetherforge API Examples

This document provides comprehensive examples of using the Aetherforge API for various use cases and scenarios.

## 🚀 Quick Start Examples

### Basic Project Creation

```bash
# Create a simple web application
curl -X POST http://localhost:8000/projects \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A simple todo list application with user authentication",
    "project_name": "TodoApp",
    "project_type": "fullstack"
  }'
```

### Advanced Project Creation

```bash
# Create a complex e-commerce application
curl -X POST http://localhost:8000/projects \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "An e-commerce platform with product catalog, shopping cart, payment processing, and admin dashboard",
    "project_name": "EcommerceStore",
    "project_type": "fullstack",
    "features": ["authentication", "payments", "admin_panel", "search", "analytics"],
    "tech_preferences": {
      "frontend": "react",
      "backend": "nodejs",
      "database": "postgresql",
      "payment": "stripe"
    },
    "complexity": "complex",
    "priority": "high",
    "deadline": "2024-02-15T00:00:00Z"
  }'
```

## 📊 Project Management Examples

### Monitor Project Progress

```bash
# Get detailed project status
curl -X GET http://localhost:8000/projects/proj_abc123/status \
  -H "Authorization: Bearer your_api_key"
```

**Response:**
```json
{
  "project_id": "proj_abc123",
  "status": "in_progress",
  "progress": {
    "overall": 65,
    "phases": {
      "requirements_analysis": {"status": "completed", "progress": 100},
      "architecture_design": {"status": "completed", "progress": 100},
      "development": {"status": "in_progress", "progress": 60},
      "quality_assurance": {"status": "pending", "progress": 0}
    }
  },
  "current_agent": {
    "role": "developer",
    "task": "Implementing payment integration",
    "estimated_completion": "2024-01-15T16:00:00Z"
  }
}
```

### List Projects with Filtering

```bash
# Get active projects only
curl -X GET "http://localhost:8000/projects?status=active&limit=10" \
  -H "Authorization: Bearer your_api_key"

# Search projects by name
curl -X GET "http://localhost:8000/projects?search=ecommerce&sort=created_at&order=desc" \
  -H "Authorization: Bearer your_api_key"
```

## 🔧 Advanced Use Cases

### Custom Workflow Example

```bash
# Create project with custom workflow
curl -X POST http://localhost:8000/projects \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A machine learning model training pipeline",
    "project_name": "MLPipeline",
    "project_type": "data_platform",
    "workflow": "ml-training-workflow",
    "tech_preferences": {
      "framework": "tensorflow",
      "language": "python",
      "deployment": "kubernetes"
    },
    "custom_config": {
      "model_type": "neural_network",
      "dataset_size": "large",
      "training_time": "extended"
    }
  }'
```

### Batch Project Creation

```bash
# Create multiple projects in sequence
for project in "BlogApp" "PortfolioSite" "ChatApp"; do
  curl -X POST http://localhost:8000/projects \
    -H "Authorization: Bearer your_api_key" \
    -H "Content-Type: application/json" \
    -d "{
      \"prompt\": \"A $project with modern design and responsive layout\",
      \"project_name\": \"$project\",
      \"project_type\": \"frontend\"
    }"
  sleep 5  # Wait between requests to avoid rate limiting
done
```

## 🐍 Python SDK Examples

### Basic Usage

```python
from aetherforge import AetherforgeClient

# Initialize client
client = AetherforgeClient(api_key="your_api_key")

# Create a project
project = client.projects.create(
    prompt="A social media dashboard with analytics",
    project_name="SocialDashboard",
    project_type="fullstack",
    features=["authentication", "analytics", "real_time"]
)

print(f"Project created: {project.id}")
print(f"Estimated completion: {project.estimated_completion}")
```

### Advanced Project Management

```python
import asyncio
from aetherforge import AetherforgeClient

async def monitor_project(client, project_id):
    """Monitor project progress with real-time updates."""
    
    # Get initial status
    status = await client.projects.get_status(project_id)
    print(f"Initial progress: {status.progress.overall}%")
    
    # Stream real-time updates
    async for update in client.projects.stream_updates(project_id):
        print(f"Update: {update.message}")
        print(f"Progress: {update.progress}%")
        
        if update.status == "completed":
            print("Project completed!")
            break
        elif update.status == "failed":
            print(f"Project failed: {update.error}")
            break

# Usage
async def main():
    client = AetherforgeClient(api_key="your_api_key")
    
    # Create project
    project = await client.projects.create_async(
        prompt="A real-time chat application",
        project_name="ChatApp",
        project_type="fullstack"
    )
    
    # Monitor progress
    await monitor_project(client, project.id)

asyncio.run(main())
```

### Batch Operations

```python
from aetherforge import AetherforgeClient
import concurrent.futures

def create_project(client, config):
    """Create a single project."""
    return client.projects.create(**config)

# Project configurations
project_configs = [
    {
        "prompt": "A blog platform with CMS features",
        "project_name": "BlogCMS",
        "project_type": "fullstack"
    },
    {
        "prompt": "A task management application",
        "project_name": "TaskManager",
        "project_type": "fullstack"
    },
    {
        "prompt": "A portfolio website with animations",
        "project_name": "Portfolio",
        "project_type": "frontend"
    }
]

# Create projects concurrently
client = AetherforgeClient(api_key="your_api_key")

with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
    futures = [
        executor.submit(create_project, client, config)
        for config in project_configs
    ]
    
    for future in concurrent.futures.as_completed(futures):
        try:
            project = future.result()
            print(f"Created project: {project.name} ({project.id})")
        except Exception as e:
            print(f"Failed to create project: {e}")
```

## 🌐 JavaScript/Node.js Examples

### Basic Project Creation

```javascript
const Aetherforge = require('aetherforge-js');

const client = new Aetherforge.Client({
  apiKey: 'your_api_key',
  baseUrl: 'http://localhost:8000'
});

async function createProject() {
  try {
    const project = await client.projects.create({
      prompt: 'A weather application with location-based forecasts',
      projectName: 'WeatherApp',
      projectType: 'mobile',
      techPreferences: {
        framework: 'react-native',
        backend: 'nodejs'
      }
    });
    
    console.log(`Project created: ${project.id}`);
    return project;
  } catch (error) {
    console.error('Failed to create project:', error.message);
  }
}

createProject();
```

### Real-time Progress Monitoring

```javascript
const Aetherforge = require('aetherforge-js');

const client = new Aetherforge.Client({
  apiKey: 'your_api_key',
  baseUrl: 'http://localhost:8000'
});

async function monitorProject(projectId) {
  // Set up WebSocket connection for real-time updates
  const ws = client.projects.streamUpdates(projectId);
  
  ws.on('update', (data) => {
    console.log(`Progress: ${data.progress}%`);
    console.log(`Current phase: ${data.current_phase}`);
    console.log(`Message: ${data.message}`);
  });
  
  ws.on('completed', (data) => {
    console.log('Project completed successfully!');
    console.log(`Download URL: ${data.download_url}`);
    ws.close();
  });
  
  ws.on('error', (error) => {
    console.error('Project failed:', error.message);
    ws.close();
  });
}

// Usage
async function main() {
  const project = await client.projects.create({
    prompt: 'A fitness tracking mobile app',
    projectName: 'FitnessTracker',
    projectType: 'mobile'
  });
  
  await monitorProject(project.id);
}

main();
```

## 🔄 WebSocket Examples

### Real-time Project Updates

```javascript
// Connect to WebSocket
const ws = new WebSocket('ws://localhost:8000/ws');

// Subscribe to project updates
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'subscribe',
    channel: 'project_updates',
    project_id: 'proj_abc123'
  }));
};

// Handle incoming messages
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch (data.type) {
    case 'progress_update':
      console.log(`Progress: ${data.progress}%`);
      break;
      
    case 'phase_completed':
      console.log(`Phase completed: ${data.phase}`);
      break;
      
    case 'agent_update':
      console.log(`Agent ${data.agent_role}: ${data.message}`);
      break;
      
    case 'project_completed':
      console.log('Project completed!');
      console.log(`Files created: ${data.file_count}`);
      break;
      
    case 'error':
      console.error(`Error: ${data.message}`);
      break;
  }
};
```

### Pheromone System Monitoring

```javascript
// Monitor pheromone signals
const ws = new WebSocket('ws://localhost:8000/ws');

ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'subscribe',
    channel: 'pheromones',
    filters: {
      signal_types: ['agent_started', 'phase_completed', 'error_occurred'],
      intensity_min: 0.5
    }
  }));
};

ws.onmessage = (event) => {
  const pheromone = JSON.parse(event.data);
  
  console.log(`Pheromone: ${pheromone.signal}`);
  console.log(`Intensity: ${pheromone.intensity}`);
  console.log(`Agent: ${pheromone.agent_id}`);
  console.log(`Project: ${pheromone.project_id}`);
  console.log(`Payload:`, pheromone.payload);
};
```

## 🛠️ Integration Examples

### CI/CD Pipeline Integration

```yaml
# .github/workflows/auto-generate.yml
name: Auto-generate Projects

on:
  issues:
    types: [labeled]

jobs:
  generate-project:
    if: contains(github.event.label.name, 'auto-generate')
    runs-on: ubuntu-latest
    
    steps:
    - name: Extract project details
      id: extract
      run: |
        # Extract project details from issue body
        echo "project_name=$(echo '${{ github.event.issue.title }}' | sed 's/[^a-zA-Z0-9]//g')" >> $GITHUB_OUTPUT
        
    - name: Create project via TaoForge API
      run: |
        curl -X POST ${{ secrets.AETHERFORGE_API_URL }}/projects \
          -H "Authorization: Bearer ${{ secrets.AETHERFORGE_API_KEY }}" \
          -H "Content-Type: application/json" \
          -d '{
            "prompt": "${{ github.event.issue.body }}",
            "project_name": "${{ steps.extract.outputs.project_name }}",
            "project_type": "fullstack"
          }'
```

### Slack Bot Integration

```python
from slack_bolt import App
from aetherforge import AetherforgeClient

app = App(token="your_slack_token")
aetherforge = AetherforgeClient(api_key="your_aetherforge_api_key")

@app.command("/create-project")
def create_project_command(ack, respond, command):
    ack()
    
    try:
        # Parse command text
        prompt = command['text']
        project_name = f"slack-project-{command['user_id']}"
        
        # Create project
        project = aetherforge.projects.create(
            prompt=prompt,
            project_name=project_name,
            project_type="fullstack"
        )
        
        respond(f"Project created! ID: {project.id}\nMonitor progress: {project.status_url}")
        
    except Exception as e:
        respond(f"Failed to create project: {str(e)}")

if __name__ == "__main__":
    app.start(port=int(os.environ.get("PORT", 3000)))
```

## 📈 Analytics and Monitoring

### Project Analytics

```python
from aetherforge import AetherforgeClient
import pandas as pd
import matplotlib.pyplot as plt

client = AetherforgeClient(api_key="your_api_key")

# Get project statistics
projects = client.projects.list(limit=100)
df = pd.DataFrame([p.to_dict() for p in projects])

# Analyze project success rates
success_rate = df['status'].value_counts(normalize=True)
print(f"Success rate: {success_rate['completed']:.2%}")

# Plot project creation over time
df['created_at'] = pd.to_datetime(df['created_at'])
daily_projects = df.groupby(df['created_at'].dt.date).size()

plt.figure(figsize=(12, 6))
daily_projects.plot(kind='line')
plt.title('Projects Created Over Time')
plt.xlabel('Date')
plt.ylabel('Number of Projects')
plt.show()
```

---

These examples demonstrate the flexibility and power of the Aetherforge API. For more advanced use cases and detailed documentation, visit our [API reference](README.md).
