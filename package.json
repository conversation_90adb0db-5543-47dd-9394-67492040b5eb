{
  "name": "aetherforge",
  "displayName": "Aetherforge",
  "description": "Autonomous AI Software Creation System",
  "version": "1.0.0",
  "publisher": "aetherforge",
  "repository": {
    "type": "git",
    "url": "https://github.com/aetherforge/aetherforge.git"
  },
  "engines": {
    "vscode": "^1.74.0"
  },
  "categories": ["Other"],
  "activationEvents": [],
  "main": "./dist/extension.js",
  "contributes": {
    "commands": [
      {
        "command": "aetherforge.start",
        "title": "Start Aetherforge",
        "category": "Aetherforge"
      }
    ],
    "configuration": {
      "title": "Aetherforge",
      "properties": {
        "aetherforge.orchestratorUrl": {
          "type": "string",
          "default": "http://localhost:8000",
          "description": "URL of the Aetherforge orchestrator service"
        }
      }
    }
  },
  "scripts": {
    "compile": "tsc -p ./",
    "watch": "tsc -watch -p ./",
    "vscode:prepublish": "npm run esbuild-base -- --minify",
    "esbuild-base": "esbuild ./src/extension.ts --bundle --outfile=dist/extension.js --external:vscode --format=cjs --platform=node"
  },
  "devDependencies": {
    "@types/vscode": "^1.74.0",
    "@types/node": "16.x",
    "typescript": "^4.9.4",
    "esbuild": "^0.19.0"
  },
  "dependencies": {
    "axios": "^1.6.0"
  }
}
EOF