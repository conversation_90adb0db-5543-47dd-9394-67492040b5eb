import { useState, useEffect, useRef, useCallback } from 'react';
import { vscode, useVSCodeMessage } from '@/utils/vscode';
import { 
  ProjectStructure, 
  QualityMetrics, 
  WorkflowExecution, 
  Pheromone,
  ProjectFile,
  CodeIssue
} from '@/types';

interface RealTimeSyncState {
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  lastUpdate: string | null;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface DataCache {
  projectStructure: CacheEntry<ProjectStructure> | null;
  qualityMetrics: CacheEntry<QualityMetrics> | null;
  workflowExecutions: CacheEntry<WorkflowExecution[]> | null;
  pheromones: CacheEntry<Pheromone[]> | null;
}

export const useRealTimeSync = (projectId: string) => {
  const [syncState, setSyncState] = useState<RealTimeSyncState>({
    isConnected: false,
    isLoading: false,
    error: null,
    lastUpdate: null,
    connectionStatus: 'disconnected'
  });

  const [cache, setCache] = useState<DataCache>({
    projectStructure: null,
    qualityMetrics: null,
    workflowExecutions: null,
    pheromones: null
  });

  const [realTimeEnabled, setRealTimeEnabled] = useState(true);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const heartbeatIntervalRef = useRef<NodeJS.Timeout>();

  // Cache utilities
  const setCacheEntry = useCallback(<T>(
    key: keyof DataCache,
    data: T,
    ttl: number = 5 * 60 * 1000 // 5 minutes default TTL
  ) => {
    setCache(prev => ({
      ...prev,
      [key]: {
        data,
        timestamp: Date.now(),
        ttl
      }
    }));
  }, []);

  const getCacheEntry = useCallback(<T>(key: keyof DataCache): T | null => {
    const entry = cache[key] as CacheEntry<T> | null;
    if (!entry) return null;
    
    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      // Cache expired
      setCache(prev => ({ ...prev, [key]: null }));
      return null;
    }
    
    return entry.data;
  }, [cache]);

  const invalidateCache = useCallback((key?: keyof DataCache) => {
    if (key) {
      setCache(prev => ({ ...prev, [key]: null }));
    } else {
      setCache({
        projectStructure: null,
        qualityMetrics: null,
        workflowExecutions: null,
        pheromones: null
      });
    }
  }, []);

  // Connection management
  const connect = useCallback(async () => {
    setSyncState(prev => ({ ...prev, connectionStatus: 'connecting', isLoading: true }));
    
    try {
      await vscode.sendRequest('connectRealTimeSync', { projectId });
      setSyncState(prev => ({
        ...prev,
        isConnected: true,
        connectionStatus: 'connected',
        isLoading: false,
        error: null,
        lastUpdate: new Date().toISOString()
      }));
      
      // Start heartbeat
      heartbeatIntervalRef.current = setInterval(() => {
        vscode.postMessage('heartbeat', { projectId });
      }, 30000); // 30 seconds
      
    } catch (error) {
      setSyncState(prev => ({
        ...prev,
        isConnected: false,
        connectionStatus: 'error',
        isLoading: false,
        error: error instanceof Error ? error.message : 'Connection failed'
      }));
      
      // Schedule reconnection
      scheduleReconnect();
    }
  }, [projectId]);

  const disconnect = useCallback(() => {
    vscode.postMessage('disconnectRealTimeSync', { projectId });
    
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    setSyncState(prev => ({
      ...prev,
      isConnected: false,
      connectionStatus: 'disconnected',
      error: null
    }));
  }, [projectId]);

  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    reconnectTimeoutRef.current = setTimeout(() => {
      if (realTimeEnabled) {
        connect();
      }
    }, 5000); // Retry after 5 seconds
  }, [connect, realTimeEnabled]);

  // Data fetching with caching
  const fetchProjectStructure = useCallback(async (forceRefresh = false): Promise<ProjectStructure | null> => {
    if (!forceRefresh) {
      const cached = getCacheEntry<ProjectStructure>('projectStructure');
      if (cached) return cached;
    }
    
    try {
      const data = await vscode.sendRequest('getProjectStructure', { projectId });
      setCacheEntry('projectStructure', data);
      return data;
    } catch (error) {
      console.error('Failed to fetch project structure:', error);
      return null;
    }
  }, [projectId, getCacheEntry, setCacheEntry]);

  const fetchQualityMetrics = useCallback(async (forceRefresh = false): Promise<QualityMetrics | null> => {
    if (!forceRefresh) {
      const cached = getCacheEntry<QualityMetrics>('qualityMetrics');
      if (cached) return cached;
    }
    
    try {
      const data = await vscode.sendRequest('getQualityMetrics', { projectId });
      setCacheEntry('qualityMetrics', data);
      return data;
    } catch (error) {
      console.error('Failed to fetch quality metrics:', error);
      return null;
    }
  }, [projectId, getCacheEntry, setCacheEntry]);

  const fetchWorkflowExecutions = useCallback(async (forceRefresh = false): Promise<WorkflowExecution[]> => {
    if (!forceRefresh) {
      const cached = getCacheEntry<WorkflowExecution[]>('workflowExecutions');
      if (cached) return cached;
    }
    
    try {
      const data = await vscode.sendRequest('getWorkflowExecutions', { projectId });
      setCacheEntry('workflowExecutions', data);
      return data;
    } catch (error) {
      console.error('Failed to fetch workflow executions:', error);
      return [];
    }
  }, [projectId, getCacheEntry, setCacheEntry]);

  const fetchPheromones = useCallback(async (forceRefresh = false): Promise<Pheromone[]> => {
    if (!forceRefresh) {
      const cached = getCacheEntry<Pheromone[]>('pheromones');
      if (cached) return cached;
    }
    
    try {
      const data = await vscode.sendRequest('getPheromones', { projectId });
      setCacheEntry('pheromones', data);
      return data;
    } catch (error) {
      console.error('Failed to fetch pheromones:', error);
      return [];
    }
  }, [projectId, getCacheEntry, setCacheEntry]);

  // Real-time message handlers
  useVSCodeMessage('realTimeUpdate', (data) => {
    if (!realTimeEnabled || data.projectId !== projectId) return;
    
    setSyncState(prev => ({
      ...prev,
      lastUpdate: new Date().toISOString()
    }));
    
    switch (data.type) {
      case 'projectStructure':
        setCacheEntry('projectStructure', data.data);
        break;
      case 'qualityMetrics':
        setCacheEntry('qualityMetrics', data.data);
        break;
      case 'workflowExecution':
        // Update specific workflow execution
        const currentExecutions = getCacheEntry<WorkflowExecution[]>('workflowExecutions');
        if (currentExecutions) {
          const updatedExecutions = currentExecutions.map(exec =>
            exec.id === data.data.id ? { ...exec, ...data.data } : exec
          );
          setCacheEntry('workflowExecutions', updatedExecutions);
        }
        break;
      case 'pheromone':
        // Add new pheromone
        const currentPheromones = getCacheEntry<Pheromone[]>('pheromones') || [];
        setCacheEntry('pheromones', [data.data, ...currentPheromones.slice(0, 99)]);
        break;
      case 'fileChange':
        // Invalidate structure cache when files change
        invalidateCache('projectStructure');
        break;
      case 'qualityChange':
        // Invalidate quality cache when quality changes
        invalidateCache('qualityMetrics');
        break;
    }
  });

  useVSCodeMessage('connectionStatus', (data) => {
    if (data.projectId !== projectId) return;
    
    setSyncState(prev => ({
      ...prev,
      isConnected: data.connected,
      connectionStatus: data.connected ? 'connected' : 'disconnected',
      error: data.error || null
    }));
    
    if (!data.connected && realTimeEnabled) {
      scheduleReconnect();
    }
  });

  // Initialize connection
  useEffect(() => {
    if (realTimeEnabled && projectId) {
      connect();
    }
    
    return () => {
      disconnect();
    };
  }, [projectId, realTimeEnabled, connect, disconnect]);

  // Toggle real-time updates
  const toggleRealTime = useCallback(() => {
    setRealTimeEnabled(prev => {
      const newValue = !prev;
      if (newValue) {
        connect();
      } else {
        disconnect();
      }
      return newValue;
    });
  }, [connect, disconnect]);

  // Force refresh all data
  const refreshAll = useCallback(async () => {
    invalidateCache();
    setSyncState(prev => ({ ...prev, isLoading: true }));
    
    try {
      await Promise.all([
        fetchProjectStructure(true),
        fetchQualityMetrics(true),
        fetchWorkflowExecutions(true),
        fetchPheromones(true)
      ]);
    } finally {
      setSyncState(prev => ({ ...prev, isLoading: false }));
    }
  }, [fetchProjectStructure, fetchQualityMetrics, fetchWorkflowExecutions, fetchPheromones, invalidateCache]);

  return {
    // State
    ...syncState,
    realTimeEnabled,
    
    // Data fetchers
    fetchProjectStructure,
    fetchQualityMetrics,
    fetchWorkflowExecutions,
    fetchPheromones,
    
    // Cache utilities
    getCachedProjectStructure: () => getCacheEntry<ProjectStructure>('projectStructure'),
    getCachedQualityMetrics: () => getCacheEntry<QualityMetrics>('qualityMetrics'),
    getCachedWorkflowExecutions: () => getCacheEntry<WorkflowExecution[]>('workflowExecutions'),
    getCachedPheromones: () => getCacheEntry<Pheromone[]>('pheromones'),
    
    // Actions
    connect,
    disconnect,
    toggleRealTime,
    refreshAll,
    invalidateCache
  };
};
