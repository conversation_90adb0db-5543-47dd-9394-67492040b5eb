import * as vscode from 'vscode';
import { startAetherforge } from './aetherforge';

export function activate(context: vscode.ExtensionContext) {
  console.log('Aetherforge is now active!');

  let disposable = vscode.commands.registerCommand('aetherforge.start', async () => {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
      vscode.window.showErrorMessage('Please open a workspace folder to use Aetherforge');
      return;
    }

    try {
      await startAetherforge(context);
      vscode.window.showInformationMessage('Aetherforge started successfully!');
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to start Aetherforge: ${error}`);
    }
  });

  context.subscriptions.push(disposable);
}

export function deactivate() {
  // Clean up resources when extension is deactivated
}
