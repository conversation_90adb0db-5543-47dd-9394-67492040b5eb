"""
Enhanced Base Agent for Aetherforge
Provides comprehensive base functionality for all agents with integrated APIManager,
resilience features, error handling, and configuration management.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class AgentType(Enum):
    """Types of agents in the system"""
    ANALYST = "analyst"
    ARCHITECT = "architect"
    DEVELOPER = "developer"
    QA = "qa"
    SPECIALIST = "specialist"
    COORDINATOR = "coordinator"

class AgentStatus(Enum):
    """Agent execution status"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    EXECUTING = "executing"
    WAITING = "waiting"
    COMPLETED = "completed"
    ERROR = "error"
    DEGRADED = "degraded"

@dataclass
class AgentConfig:
    """Configuration for agent behavior"""
    # API Configuration
    preferred_provider: str = "openai"
    fallback_providers: List[str] = field(default_factory=lambda: ["anthropic", "openrouter"])
    model: str = "gpt-4"
    max_tokens: int = 3000
    temperature: float = 0.7
    
    # Resilience Configuration
    enable_resilience: bool = True
    max_retries: int = 3
    retry_delay: float = 1.0
    enable_fallback: bool = True
    enable_caching: bool = True
    
    # Performance Configuration
    timeout_seconds: int = 300
    enable_streaming: bool = False
    batch_size: int = 1
    
    # Quality Configuration
    enable_validation: bool = True
    enable_logging: bool = True
    log_level: str = "INFO"

@dataclass
class AgentMetrics:
    """Metrics for agent performance tracking"""
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    average_execution_time: float = 0.0
    total_tokens_used: int = 0
    total_cost: float = 0.0
    api_calls_made: int = 0
    cache_hits: int = 0
    fallbacks_used: int = 0
    last_execution_time: Optional[str] = None
    error_history: List[Dict[str, Any]] = field(default_factory=list)

class BaseAgentEnhanced(ABC):
    """Enhanced base class for all Aetherforge agents with comprehensive API integration"""
    
    def __init__(self, agent_id: str, name: str, agent_type: AgentType, 
                 config: Optional[AgentConfig] = None):
        self.agent_id = agent_id
        self.name = name
        self.agent_type = agent_type
        self.config = config or AgentConfig()
        
        # Status and metrics
        self.status = AgentStatus.IDLE
        self.metrics = AgentMetrics()
        
        # API Manager integration
        self.api_manager = None
        self._initialize_api_manager()
        
        # Context and state
        self.context: Dict[str, Any] = {}
        self.execution_history: List[Dict[str, Any]] = []
        
        # Logging setup
        self._setup_logging()
        
        logger.info(f"Initialized {self.agent_type.value} agent: {self.name}")
    
    def _initialize_api_manager(self):
        """Initialize API manager with agent configuration"""
        try:
            from api_manager import APIManager, APIProvider
            
            self.api_manager = APIManager()
            
            # Set preferred provider if available
            try:
                preferred = APIProvider(self.config.preferred_provider.lower())
                if preferred in self.api_manager.providers:
                    self.api_manager.fallback_order = [preferred] + [
                        p for p in self.api_manager.fallback_order if p != preferred
                    ]
            except (ValueError, AttributeError):
                logger.warning(f"Invalid preferred provider: {self.config.preferred_provider}")
                
        except ImportError:
            logger.warning("APIManager not available, using basic implementation")
            self.api_manager = None
    
    def _setup_logging(self):
        """Setup agent-specific logging"""
        self.logger = logging.getLogger(f"agent.{self.agent_type.value}.{self.agent_id}")
        self.logger.setLevel(getattr(logging, self.config.log_level.upper(), logging.INFO))
    
    async def execute_task(self, task: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a task with comprehensive error handling and resilience
        
        Args:
            task: Task specification with type, parameters, and requirements
            context: Additional context for task execution
            
        Returns:
            Dictionary with execution results and metadata
        """
        execution_id = f"{self.agent_id}_{int(time.time())}"
        start_time = time.time()
        
        self.status = AgentStatus.EXECUTING
        self.metrics.total_executions += 1
        
        # Merge context
        execution_context = {**self.context, **(context or {})}
        
        try:
            self.logger.info(f"Starting task execution: {task.get('type', 'unknown')}")
            
            # Validate input
            if self.config.enable_validation:
                validation_result = await self.validate_input(task, execution_context)
                if not validation_result.get("valid", False):
                    raise ValueError(f"Input validation failed: {validation_result.get('message')}")
            
            # Execute the actual task
            result = await self._execute_task_implementation(task, execution_context)
            
            # Update metrics for success
            execution_time = time.time() - start_time
            self._update_success_metrics(execution_time, result)
            
            # Add execution metadata
            result["metadata"] = {
                **result.get("metadata", {}),
                "execution_id": execution_id,
                "agent_id": self.agent_id,
                "agent_type": self.agent_type.value,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            self.status = AgentStatus.COMPLETED
            self.logger.info(f"Task completed successfully in {execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            # Handle execution error
            execution_time = time.time() - start_time
            error_result = await self._handle_execution_error(e, task, execution_context, execution_time)
            
            self.status = AgentStatus.ERROR
            self.logger.error(f"Task execution failed: {e}")
            
            return error_result
    
    async def _execute_task_implementation(self, task: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Implementation-specific task execution (to be overridden by subclasses)
        This method should contain the actual agent logic
        """
        return await self.execute_agent_logic(task, context)
    
    @abstractmethod
    async def execute_agent_logic(self, task: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Agent-specific logic implementation (must be implemented by subclasses)
        
        Args:
            task: Task specification
            context: Execution context
            
        Returns:
            Dictionary with agent-specific results
        """
        pass
    
    async def validate_input(self, task: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate input data for the agent (can be overridden by subclasses)
        
        Args:
            task: Task specification to validate
            context: Execution context
            
        Returns:
            Dictionary with validation results
        """
        # Basic validation
        if not isinstance(task, dict):
            return {"valid": False, "message": "Task must be a dictionary"}
        
        if "type" not in task:
            return {"valid": False, "message": "Task must have a 'type' field"}
        
        return {"valid": True, "message": "Input validation passed"}
    
    async def generate_text(self, messages: List[Dict[str, str]], 
                          model: Optional[str] = None,
                          max_tokens: Optional[int] = None,
                          temperature: Optional[float] = None,
                          **kwargs) -> Dict[str, Any]:
        """
        Generate text using the integrated API manager with resilience
        
        Args:
            messages: List of message dictionaries
            model: Model to use (defaults to agent config)
            max_tokens: Maximum tokens (defaults to agent config)
            temperature: Temperature (defaults to agent config)
            **kwargs: Additional parameters
            
        Returns:
            Dictionary with generated text and metadata
        """
        if not self.api_manager:
            return {
                "success": False,
                "error": "API manager not available",
                "content": "Unable to generate text - API manager not configured"
            }
        
        # Use agent configuration defaults
        model = model or self.config.model
        max_tokens = max_tokens or self.config.max_tokens
        temperature = temperature if temperature is not None else self.config.temperature
        
        try:
            # Track API call
            self.metrics.api_calls_made += 1
            
            # Generate text with resilience
            result = await self.api_manager.generate_text(
                messages=messages,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=self.config.enable_streaming,
                **kwargs
            )
            
            # Update metrics
            if result.get("success"):
                tokens_used = result.get("metadata", {}).get("tokens_used", 0)
                self.metrics.total_tokens_used += tokens_used
                
                # Estimate cost (rough calculation)
                estimated_cost = tokens_used * 0.0001  # $0.0001 per token estimate
                self.metrics.total_cost += estimated_cost
                
                if result.get("metadata", {}).get("from_cache"):
                    self.metrics.cache_hits += 1
                
                if result.get("metadata", {}).get("fallback_used"):
                    self.metrics.fallbacks_used += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"Text generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "Text generation failed due to technical difficulties"
            }
    
    async def _handle_execution_error(self, error: Exception, task: Dict[str, Any], 
                                    context: Dict[str, Any], execution_time: float) -> Dict[str, Any]:
        """Handle execution errors with appropriate recovery strategies"""
        self.metrics.failed_executions += 1
        
        # Log error details
        error_details = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "task_type": task.get("type"),
            "timestamp": datetime.now().isoformat(),
            "execution_time": execution_time
        }
        
        self.metrics.error_history.append(error_details)
        
        # Keep only last 100 errors
        if len(self.metrics.error_history) > 100:
            self.metrics.error_history = self.metrics.error_history[-100:]
        
        # Determine if error is recoverable
        recoverable = self._is_recoverable_error(error)
        
        return {
            "success": False,
            "error": str(error),
            "error_type": type(error).__name__,
            "recoverable": recoverable,
            "metadata": {
                "agent_id": self.agent_id,
                "agent_type": self.agent_type.value,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "error_details": error_details
            }
        }
    
    def _is_recoverable_error(self, error: Exception) -> bool:
        """Determine if an error is recoverable"""
        error_str = str(error).lower()
        
        # Non-recoverable errors
        non_recoverable = ["invalid", "unauthorized", "forbidden", "not found", "validation"]
        if any(term in error_str for term in non_recoverable):
            return False
        
        # Recoverable errors
        recoverable = ["timeout", "connection", "rate limit", "service unavailable", "temporary"]
        if any(term in error_str for term in recoverable):
            return True
        
        return True  # Default to recoverable
    
    def _update_success_metrics(self, execution_time: float, result: Dict[str, Any]):
        """Update metrics for successful execution"""
        self.metrics.successful_executions += 1
        self.metrics.last_execution_time = datetime.now().isoformat()
        
        # Update average execution time
        total_executions = self.metrics.successful_executions + self.metrics.failed_executions
        self.metrics.average_execution_time = (
            (self.metrics.average_execution_time * (total_executions - 1) + execution_time) / total_executions
        )
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status and metrics"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "type": self.agent_type.value,
            "status": self.status.value,
            "config": {
                "preferred_provider": self.config.preferred_provider,
                "model": self.config.model,
                "max_tokens": self.config.max_tokens,
                "enable_resilience": self.config.enable_resilience
            },
            "metrics": {
                "total_executions": self.metrics.total_executions,
                "successful_executions": self.metrics.successful_executions,
                "failed_executions": self.metrics.failed_executions,
                "success_rate": (self.metrics.successful_executions / self.metrics.total_executions 
                               if self.metrics.total_executions > 0 else 0),
                "average_execution_time": self.metrics.average_execution_time,
                "total_tokens_used": self.metrics.total_tokens_used,
                "total_cost": self.metrics.total_cost,
                "api_calls_made": self.metrics.api_calls_made,
                "cache_hits": self.metrics.cache_hits,
                "fallbacks_used": self.metrics.fallbacks_used,
                "last_execution_time": self.metrics.last_execution_time
            },
            "api_manager_available": self.api_manager is not None,
            "last_errors": self.metrics.error_history[-5:] if self.metrics.error_history else []
        }
    
    async def update_config(self, new_config: Dict[str, Any]) -> Dict[str, Any]:
        """Update agent configuration"""
        try:
            # Update configuration fields
            for key, value in new_config.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                    self.logger.info(f"Updated config {key} = {value}")
            
            # Reinitialize API manager if provider changed
            if "preferred_provider" in new_config:
                self._initialize_api_manager()
            
            return {"success": True, "message": "Configuration updated successfully"}
            
        except Exception as e:
            self.logger.error(f"Failed to update configuration: {e}")
            return {"success": False, "error": str(e)}
    
    async def reset_metrics(self):
        """Reset agent metrics"""
        self.metrics = AgentMetrics()
        self.logger.info("Agent metrics reset")
    
    async def cleanup(self):
        """Cleanup agent resources"""
        self.status = AgentStatus.IDLE
        self.logger.info("Agent cleanup completed")
