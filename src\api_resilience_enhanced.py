"""
Enhanced API Resilience System for Aetherforge
Provides comprehensive resilience mechanisms including exponential backoff retry logic,
provider fallback chains, model fallback, cache fallback, and degraded service mode.
"""

import asyncio
import time
import random
import json
import hashlib
import logging
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class FallbackStrategy(Enum):
    """Fallback strategy types"""
    PROVIDER_FALLBACK = "provider_fallback"
    MODEL_FALLBACK = "model_fallback"
    CACHE_FALLBACK = "cache_fallback"
    DEGRADED_MODE = "degraded_mode"

class RetryStrategy(Enum):
    """Retry strategy types"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_DELAY = "fixed_delay"
    IMMEDIATE = "immediate"

@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    jitter_range: float = 0.1
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    retry_on_status_codes: List[int] = field(default_factory=lambda: [429, 500, 502, 503, 504])
    retry_on_exceptions: List[str] = field(default_factory=lambda: [
        "ConnectionError", "TimeoutError", "RateLimitError", "ServiceUnavailableError"
    ])

@dataclass
class FallbackConfig:
    """Configuration for fallback behavior"""
    enable_provider_fallback: bool = True
    enable_model_fallback: bool = True
    enable_cache_fallback: bool = True
    enable_degraded_mode: bool = True
    
    # Provider fallback chain
    provider_fallback_order: List[str] = field(default_factory=lambda: [
        "openai", "anthropic", "openrouter", "azure", "local"
    ])
    
    # Model fallback chains per provider
    model_fallback_chains: Dict[str, List[str]] = field(default_factory=lambda: {
        "openai": ["gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"],
        "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
        "azure": ["gpt-4", "gpt-35-turbo"],
        "openrouter": ["openai/gpt-4-turbo-preview", "openai/gpt-4", "openai/gpt-3.5-turbo"],
        "local": ["llama2", "mistral", "codellama"]
    })
    
    # Cache settings
    cache_ttl_seconds: int = 3600  # 1 hour
    cache_max_size: int = 1000
    
    # Degraded mode settings
    degraded_mode_message: str = "I apologize, but I'm currently experiencing technical difficulties. Please try again later."
    degraded_mode_timeout: int = 300  # 5 minutes

@dataclass
class ResilienceMetrics:
    """Metrics for resilience operations"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    retries_performed: int = 0
    fallbacks_used: int = 0
    cache_hits: int = 0
    degraded_mode_activations: int = 0
    average_response_time: float = 0.0
    provider_success_rates: Dict[str, float] = field(default_factory=dict)
    model_success_rates: Dict[str, float] = field(default_factory=dict)

class APIResilienceManager:
    """Comprehensive API resilience management system"""
    
    def __init__(self, retry_config: Optional[RetryConfig] = None, 
                 fallback_config: Optional[FallbackConfig] = None):
        self.retry_config = retry_config or RetryConfig()
        self.fallback_config = fallback_config or FallbackConfig()
        
        # Cache storage
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        
        # Metrics tracking
        self.metrics = ResilienceMetrics()
        
        # Degraded mode state
        self.degraded_mode_active = False
        self.degraded_mode_start_time: Optional[datetime] = None
        
        # Provider health tracking
        self.provider_health: Dict[str, Dict[str, Any]] = {}
        
        # Circuit breaker state
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}
        
        logger.info("API Resilience Manager initialized")
    
    async def execute_with_resilience(self, 
                                    operation: Callable,
                                    operation_args: tuple = (),
                                    operation_kwargs: Dict[str, Any] = None,
                                    cache_key: Optional[str] = None,
                                    provider: Optional[str] = None,
                                    model: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute an operation with full resilience mechanisms
        
        Args:
            operation: The async function to execute
            operation_args: Arguments for the operation
            operation_kwargs: Keyword arguments for the operation
            cache_key: Key for caching (if None, caching is disabled)
            provider: Provider name for fallback
            model: Model name for fallback
            
        Returns:
            Dictionary with result and metadata
        """
        start_time = time.time()
        operation_kwargs = operation_kwargs or {}
        
        self.metrics.total_requests += 1
        
        # Check degraded mode
        if self._is_degraded_mode_active():
            return self._get_degraded_mode_response()
        
        # Check cache first
        if cache_key and self.fallback_config.enable_cache_fallback:
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                self.metrics.cache_hits += 1
                cached_result["metadata"]["from_cache"] = True
                return cached_result
        
        # Try with retry logic
        last_error = None
        
        for attempt in range(self.retry_config.max_retries + 1):
            try:
                # Check circuit breaker
                if provider and self._is_circuit_breaker_open(provider):
                    raise Exception(f"Circuit breaker open for provider {provider}")
                
                # Execute the operation
                result = await operation(*operation_args, **operation_kwargs)
                
                # Success - update metrics and cache
                response_time = time.time() - start_time
                self._update_success_metrics(provider, model, response_time)
                
                if cache_key and result.get("success"):
                    self._store_in_cache(cache_key, result)
                
                # Reset circuit breaker on success
                if provider:
                    self._reset_circuit_breaker(provider)
                
                return result
                
            except Exception as e:
                last_error = e
                self.metrics.retries_performed += 1
                
                # Update failure metrics
                self._update_failure_metrics(provider, model)
                
                # Check if we should retry
                if attempt < self.retry_config.max_retries and self._should_retry(e):
                    delay = self._calculate_retry_delay(attempt)
                    logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay:.2f}s: {e}")
                    await asyncio.sleep(delay)
                    continue
                else:
                    # No more retries, try fallback
                    break
        
        # Primary operation failed, try fallbacks
        fallback_result = await self._try_fallbacks(
            operation, operation_args, operation_kwargs, 
            cache_key, provider, model, last_error
        )
        
        if fallback_result:
            return fallback_result
        
        # All fallbacks failed
        self.metrics.failed_requests += 1
        
        # Activate degraded mode if too many failures
        if self._should_activate_degraded_mode():
            self._activate_degraded_mode()
            return self._get_degraded_mode_response()
        
        # Return error response
        return {
            "success": False,
            "error": f"All resilience mechanisms failed. Last error: {last_error}",
            "metadata": {
                "resilience_used": True,
                "retries_attempted": self.retry_config.max_retries,
                "fallbacks_attempted": True,
                "last_error": str(last_error)
            }
        }
    
    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt"""
        if self.retry_config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.retry_config.base_delay * (self.retry_config.exponential_base ** attempt)
        elif self.retry_config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.retry_config.base_delay * (attempt + 1)
        elif self.retry_config.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.retry_config.base_delay
        else:  # IMMEDIATE
            delay = 0
        
        # Apply maximum delay limit
        delay = min(delay, self.retry_config.max_delay)
        
        # Add jitter if enabled
        if self.retry_config.jitter:
            jitter = delay * self.retry_config.jitter_range * (random.random() * 2 - 1)
            delay = max(0, delay + jitter)
        
        return delay
    
    def _should_retry(self, error: Exception) -> bool:
        """Determine if an error should trigger a retry"""
        error_str = str(error).lower()
        error_type = type(error).__name__
        
        # Check if error type is in retry list
        if error_type in self.retry_config.retry_on_exceptions:
            return True
        
        # Check for specific error patterns
        retry_patterns = ["timeout", "connection", "rate limit", "service unavailable", "502", "503", "504"]
        if any(pattern in error_str for pattern in retry_patterns):
            return True
        
        # Don't retry on authentication or quota errors
        no_retry_patterns = ["unauthorized", "forbidden", "invalid", "quota exceeded", "401", "403"]
        if any(pattern in error_str for pattern in no_retry_patterns):
            return False
        
        return True
    
    async def _try_fallbacks(self, operation: Callable, operation_args: tuple,
                           operation_kwargs: Dict[str, Any], cache_key: Optional[str],
                           provider: Optional[str], model: Optional[str], 
                           original_error: Exception) -> Optional[Dict[str, Any]]:
        """Try various fallback mechanisms"""
        
        # 1. Model fallback (try different model with same provider)
        if (model and provider and self.fallback_config.enable_model_fallback and 
            provider in self.fallback_config.model_fallback_chains):
            
            model_chain = self.fallback_config.model_fallback_chains[provider]
            current_model_index = model_chain.index(model) if model in model_chain else -1
            
            for fallback_model in model_chain[current_model_index + 1:]:
                try:
                    logger.info(f"Trying model fallback: {fallback_model}")
                    
                    # Update kwargs with fallback model
                    fallback_kwargs = operation_kwargs.copy()
                    fallback_kwargs["model"] = fallback_model
                    
                    result = await operation(*operation_args, **fallback_kwargs)
                    
                    if result.get("success"):
                        self.metrics.fallbacks_used += 1
                        result["metadata"]["fallback_used"] = "model"
                        result["metadata"]["fallback_model"] = fallback_model
                        
                        if cache_key:
                            self._store_in_cache(cache_key, result)
                        
                        return result
                        
                except Exception as e:
                    logger.warning(f"Model fallback {fallback_model} failed: {e}")
                    continue
        
        # 2. Provider fallback
        if provider and self.fallback_config.enable_provider_fallback:
            current_provider_index = -1
            if provider in self.fallback_config.provider_fallback_order:
                current_provider_index = self.fallback_config.provider_fallback_order.index(provider)
            
            for fallback_provider in self.fallback_config.provider_fallback_order[current_provider_index + 1:]:
                try:
                    logger.info(f"Trying provider fallback: {fallback_provider}")
                    
                    # Update kwargs with fallback provider
                    fallback_kwargs = operation_kwargs.copy()
                    fallback_kwargs["provider"] = fallback_provider
                    
                    # Use default model for fallback provider
                    if fallback_provider in self.fallback_config.model_fallback_chains:
                        fallback_kwargs["model"] = self.fallback_config.model_fallback_chains[fallback_provider][0]
                    
                    result = await operation(*operation_args, **fallback_kwargs)
                    
                    if result.get("success"):
                        self.metrics.fallbacks_used += 1
                        result["metadata"]["fallback_used"] = "provider"
                        result["metadata"]["fallback_provider"] = fallback_provider
                        
                        if cache_key:
                            self._store_in_cache(cache_key, result)
                        
                        return result
                        
                except Exception as e:
                    logger.warning(f"Provider fallback {fallback_provider} failed: {e}")
                    continue
        
        # 3. Cache fallback (try to find any cached response)
        if cache_key and self.fallback_config.enable_cache_fallback:
            stale_cached_result = self._get_from_cache(cache_key, allow_stale=True)
            if stale_cached_result:
                logger.info("Using stale cache as fallback")
                self.metrics.fallbacks_used += 1
                stale_cached_result["metadata"]["fallback_used"] = "stale_cache"
                return stale_cached_result
        
        return None

    def _get_from_cache(self, cache_key: str, allow_stale: bool = False) -> Optional[Dict[str, Any]]:
        """Get result from cache"""
        if cache_key not in self.cache:
            return None

        cached_time = self.cache_timestamps.get(cache_key)
        if not cached_time:
            return None

        # Check if cache is still valid
        age = (datetime.now() - cached_time).total_seconds()
        if not allow_stale and age > self.fallback_config.cache_ttl_seconds:
            # Remove expired cache entry
            del self.cache[cache_key]
            del self.cache_timestamps[cache_key]
            return None

        cached_result = self.cache[cache_key].copy()
        cached_result["metadata"]["cache_age_seconds"] = age
        cached_result["metadata"]["cache_stale"] = age > self.fallback_config.cache_ttl_seconds

        return cached_result

    def _store_in_cache(self, cache_key: str, result: Dict[str, Any]):
        """Store result in cache"""
        # Limit cache size
        if len(self.cache) >= self.fallback_config.cache_max_size:
            # Remove oldest entry
            oldest_key = min(self.cache_timestamps.keys(),
                           key=lambda k: self.cache_timestamps[k])
            del self.cache[oldest_key]
            del self.cache_timestamps[oldest_key]

        # Store result (without metadata to save space)
        cache_result = result.copy()
        if "metadata" in cache_result:
            del cache_result["metadata"]

        self.cache[cache_key] = cache_result
        self.cache_timestamps[cache_key] = datetime.now()

    def _is_degraded_mode_active(self) -> bool:
        """Check if degraded mode is currently active"""
        if not self.degraded_mode_active:
            return False

        if not self.degraded_mode_start_time:
            return False

        # Check if degraded mode timeout has passed
        elapsed = (datetime.now() - self.degraded_mode_start_time).total_seconds()
        if elapsed > self.fallback_config.degraded_mode_timeout:
            self._deactivate_degraded_mode()
            return False

        return True

    def _activate_degraded_mode(self):
        """Activate degraded mode"""
        self.degraded_mode_active = True
        self.degraded_mode_start_time = datetime.now()
        self.metrics.degraded_mode_activations += 1
        logger.warning("Degraded mode activated due to repeated failures")

    def _deactivate_degraded_mode(self):
        """Deactivate degraded mode"""
        self.degraded_mode_active = False
        self.degraded_mode_start_time = None
        logger.info("Degraded mode deactivated")

    def _get_degraded_mode_response(self) -> Dict[str, Any]:
        """Get response for degraded mode"""
        return {
            "success": False,
            "content": self.fallback_config.degraded_mode_message,
            "metadata": {
                "degraded_mode": True,
                "degraded_mode_start": self.degraded_mode_start_time.isoformat() if self.degraded_mode_start_time else None
            }
        }

    def _should_activate_degraded_mode(self) -> bool:
        """Determine if degraded mode should be activated"""
        # Activate if failure rate is too high
        if self.metrics.total_requests < 10:
            return False  # Need minimum requests to determine

        failure_rate = self.metrics.failed_requests / self.metrics.total_requests
        return failure_rate > 0.8  # 80% failure rate

    def _update_success_metrics(self, provider: Optional[str], model: Optional[str], response_time: float):
        """Update metrics for successful operation"""
        self.metrics.successful_requests += 1

        # Update average response time
        total_responses = self.metrics.successful_requests + self.metrics.failed_requests
        self.metrics.average_response_time = (
            (self.metrics.average_response_time * (total_responses - 1) + response_time) / total_responses
        )

        # Update provider success rate
        if provider:
            if provider not in self.provider_health:
                self.provider_health[provider] = {"successes": 0, "failures": 0}
            self.provider_health[provider]["successes"] += 1

            total = self.provider_health[provider]["successes"] + self.provider_health[provider]["failures"]
            self.metrics.provider_success_rates[provider] = self.provider_health[provider]["successes"] / total

        # Update model success rate
        if model:
            model_key = f"{provider}:{model}" if provider else model
            if model_key not in self.metrics.model_success_rates:
                self.metrics.model_success_rates[model_key] = {"successes": 0, "failures": 0}

            # This is a simplified tracking - in production you'd want more sophisticated metrics
            success_count = self.metrics.model_success_rates[model_key].get("successes", 0) + 1
            failure_count = self.metrics.model_success_rates[model_key].get("failures", 0)
            total = success_count + failure_count
            self.metrics.model_success_rates[model_key] = success_count / total if total > 0 else 1.0

    def _update_failure_metrics(self, provider: Optional[str], model: Optional[str]):
        """Update metrics for failed operation"""
        # Update provider failure tracking
        if provider:
            if provider not in self.provider_health:
                self.provider_health[provider] = {"successes": 0, "failures": 0}
            self.provider_health[provider]["failures"] += 1

            total = self.provider_health[provider]["successes"] + self.provider_health[provider]["failures"]
            self.metrics.provider_success_rates[provider] = self.provider_health[provider]["successes"] / total

            # Update circuit breaker
            self._update_circuit_breaker(provider, success=False)

    def _is_circuit_breaker_open(self, provider: str) -> bool:
        """Check if circuit breaker is open for a provider"""
        if provider not in self.circuit_breakers:
            return False

        breaker = self.circuit_breakers[provider]

        if breaker["state"] == "open":
            # Check if timeout has passed
            if time.time() - breaker["last_failure"] > breaker["timeout"]:
                breaker["state"] = "half_open"
                logger.info(f"Circuit breaker for {provider} moved to half-open state")
            else:
                return True

        return False

    def _update_circuit_breaker(self, provider: str, success: bool):
        """Update circuit breaker state"""
        if provider not in self.circuit_breakers:
            self.circuit_breakers[provider] = {
                "state": "closed",
                "failure_count": 0,
                "last_failure": 0,
                "timeout": 60,  # 1 minute timeout
                "failure_threshold": 5
            }

        breaker = self.circuit_breakers[provider]

        if success:
            breaker["failure_count"] = 0
            if breaker["state"] == "half_open":
                breaker["state"] = "closed"
                logger.info(f"Circuit breaker for {provider} closed")
        else:
            breaker["failure_count"] += 1
            breaker["last_failure"] = time.time()

            if breaker["failure_count"] >= breaker["failure_threshold"]:
                breaker["state"] = "open"
                logger.warning(f"Circuit breaker for {provider} opened due to repeated failures")

    def _reset_circuit_breaker(self, provider: str):
        """Reset circuit breaker for a provider"""
        if provider in self.circuit_breakers:
            self.circuit_breakers[provider]["state"] = "closed"
            self.circuit_breakers[provider]["failure_count"] = 0

    def get_resilience_status(self) -> Dict[str, Any]:
        """Get current resilience status and metrics"""
        return {
            "degraded_mode_active": self.degraded_mode_active,
            "degraded_mode_start": self.degraded_mode_start_time.isoformat() if self.degraded_mode_start_time else None,
            "cache_size": len(self.cache),
            "circuit_breakers": {
                provider: breaker["state"]
                for provider, breaker in self.circuit_breakers.items()
            },
            "metrics": {
                "total_requests": self.metrics.total_requests,
                "successful_requests": self.metrics.successful_requests,
                "failed_requests": self.metrics.failed_requests,
                "success_rate": (self.metrics.successful_requests / self.metrics.total_requests
                               if self.metrics.total_requests > 0 else 0),
                "retries_performed": self.metrics.retries_performed,
                "fallbacks_used": self.metrics.fallbacks_used,
                "cache_hits": self.metrics.cache_hits,
                "degraded_mode_activations": self.metrics.degraded_mode_activations,
                "average_response_time": self.metrics.average_response_time,
                "provider_success_rates": self.metrics.provider_success_rates,
                "model_success_rates": self.metrics.model_success_rates
            }
        }

    def clear_cache(self):
        """Clear all cached responses"""
        self.cache.clear()
        self.cache_timestamps.clear()
        logger.info("Cache cleared")

    def reset_metrics(self):
        """Reset all metrics"""
        self.metrics = ResilienceMetrics()
        logger.info("Metrics reset")
