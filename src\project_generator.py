"""
Comprehensive Project Generation Pipeline for Aetherforge
Handles complete end-to-end project creation with real code generation,
file structure creation, packaging, and final output delivery.
"""

import asyncio
import json
import logging
import shutil
import zipfile
import tarfile
import tempfile
import subprocess
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from datetime import datetime
import os
import uuid
from dataclasses import dataclass, field
from enum import Enum

from .component_adapters import ComponentManager
from .agent_executors import create_agent_executor
from .pheromone_bus import get_pheromone_bus
from .file_generators import FileGenerators
from .project_types import (
    ProjectType, PackageFormat, OutputFormat,
    ProjectTemplate, GenerationConfig
)

logger = logging.getLogger(__name__)

class ProjectGenerationPipeline:
    """Complete end-to-end project generation pipeline"""

    def __init__(self, config: GenerationConfig = None):
        self.component_manager = None
        self.pheromone_bus = get_pheromone_bus()
        self.config = config or GenerationConfig(ProjectType.WEB_APPLICATION)
        self.templates = self._load_project_templates()
        self.file_generators = self._initialize_file_generators()
        self.packaging_handlers = self._initialize_packaging_handlers()

    def _load_project_templates(self) -> Dict[ProjectType, ProjectTemplate]:
        """Load project templates for different project types"""
        return {
            ProjectType.WEB_APPLICATION: ProjectTemplate(
                name="Web Application",
                project_type=ProjectType.WEB_APPLICATION,
                directories=[
                    "src", "src/components", "src/pages", "src/utils", "src/styles",
                    "public", "public/images", "public/icons",
                    "tests", "tests/unit", "tests/integration", "tests/e2e",
                    "docs", "docs/api", "docs/user-guide",
                    "config", "scripts", "server", "server/api", "server/middleware",
                    "database", "database/migrations", "database/seeds"
                ],
                dependencies={
                    "frontend": ["react", "typescript", "vite", "tailwindcss"],
                    "backend": ["express", "cors", "helmet", "dotenv"],
                    "database": ["postgresql", "prisma"],
                    "testing": ["jest", "cypress", "testing-library"],
                    "dev": ["eslint", "prettier", "husky", "lint-staged"]
                },
                build_commands=["npm install", "npm run build"],
                test_commands=["npm test", "npm run test:e2e"],
                deployment_config={"platform": "vercel", "docker": True}
            ),
            ProjectType.API_SERVICE: ProjectTemplate(
                name="API Service",
                project_type=ProjectType.API_SERVICE,
                directories=[
                    "src", "src/controllers", "src/services", "src/models", "src/middleware",
                    "src/routes", "src/utils", "src/validators", "src/types",
                    "tests", "tests/unit", "tests/integration",
                    "docs", "docs/api", "config", "scripts",
                    "database", "database/migrations", "database/seeds"
                ],
                dependencies={
                    "runtime": ["express", "cors", "helmet", "compression"],
                    "database": ["postgresql", "prisma", "redis"],
                    "validation": ["joi", "express-validator"],
                    "auth": ["jsonwebtoken", "bcrypt", "passport"],
                    "testing": ["jest", "supertest", "faker"],
                    "dev": ["nodemon", "eslint", "prettier"]
                },
                build_commands=["npm install", "npm run build"],
                test_commands=["npm test", "npm run test:integration"],
                deployment_config={"platform": "aws", "docker": True}
            ),
            ProjectType.MOBILE_APPLICATION: ProjectTemplate(
                name="Mobile Application",
                project_type=ProjectType.MOBILE_APPLICATION,
                directories=[
                    "src", "src/screens", "src/components", "src/navigation",
                    "src/services", "src/utils", "src/hooks", "src/types",
                    "assets", "assets/images", "assets/fonts", "assets/icons",
                    "tests", "tests/unit", "tests/integration",
                    "docs", "config", "scripts", "android", "ios"
                ],
                dependencies={
                    "runtime": ["react-native", "react-navigation", "react-native-vector-icons"],
                    "state": ["redux", "react-redux", "redux-toolkit"],
                    "ui": ["react-native-elements", "react-native-paper"],
                    "testing": ["jest", "detox", "react-native-testing-library"],
                    "dev": ["metro", "flipper", "reactotron"]
                },
                build_commands=["npm install", "npx react-native run-android"],
                test_commands=["npm test", "npm run test:e2e"],
                deployment_config={"platform": "app-store", "code-push": True}
            ),
            ProjectType.DATA_PLATFORM: ProjectTemplate(
                name="Data Platform",
                project_type=ProjectType.DATA_PLATFORM,
                directories=[
                    "src", "src/pipelines", "src/processors", "src/connectors",
                    "src/models", "src/utils", "src/schemas", "src/transformers",
                    "data", "data/raw", "data/processed", "data/models",
                    "notebooks", "tests", "tests/unit", "tests/integration",
                    "docs", "config", "scripts", "infrastructure"
                ],
                dependencies={
                    "runtime": ["pandas", "numpy", "scikit-learn", "apache-airflow"],
                    "database": ["postgresql", "mongodb", "redis", "elasticsearch"],
                    "ml": ["tensorflow", "pytorch", "mlflow", "wandb"],
                    "testing": ["pytest", "great-expectations", "dbt"],
                    "dev": ["jupyter", "black", "flake8", "mypy"]
                },
                build_commands=["pip install -r requirements.txt", "dbt compile"],
                test_commands=["pytest", "great_expectations checkpoint run"],
                deployment_config={"platform": "kubernetes", "docker": True}
            )
        }

    def _initialize_file_generators(self) -> Dict[str, Any]:
        """Initialize file generators for different file types"""
        return {
            "package_json": FileGenerators.generate_package_json,
            "dockerfile": FileGenerators.generate_dockerfile,
            "docker_compose": FileGenerators.generate_docker_compose,
            "gitignore": FileGenerators.generate_gitignore,
            "readme": FileGenerators.generate_readme,
            "license": FileGenerators.generate_license,
            "env_example": FileGenerators.generate_env_example,
            "eslint_config": FileGenerators.generate_eslint_config,
            "prettier_config": FileGenerators.generate_prettier_config,
            "tsconfig": FileGenerators.generate_tsconfig,
            "jest_config": FileGenerators.generate_jest_config
        }

    def _initialize_packaging_handlers(self) -> Dict[PackageFormat, Any]:
        """Initialize packaging handlers for different formats"""
        return {
            PackageFormat.ZIP: self._package_as_zip,
            PackageFormat.TAR_GZ: self._package_as_tar_gz,
            PackageFormat.TAR_BZ2: self._package_as_tar_bz2,
            PackageFormat.DIRECTORY: self._package_as_directory
        }
    
    async def generate_project(self, prompt: str, project_name: str, project_type: str,
                             project_path: str, workflow: str = None,
                             generation_config: GenerationConfig = None) -> Dict[str, Any]:
        """Generate a complete project using the full end-to-end pipeline"""

        project_id = project_name.replace(" ", "_").lower()

        # Update configuration if provided
        if generation_config:
            self.config = generation_config

        # Parse project type
        try:
            parsed_project_type = ProjectType(project_type.lower())
            self.config.project_type = parsed_project_type
        except ValueError:
            parsed_project_type = ProjectType.WEB_APPLICATION
            logger.warning(f"Unknown project type '{project_type}', defaulting to web_application")

        try:
            # Initialize component manager
            async with ComponentManager() as cm:
                self.component_manager = cm

                # Start pheromone bus cleanup task
                await self.pheromone_bus.start_cleanup_task()

                # Phase 1: Project Initialization & Structure Creation
                await self._drop_pheromone("project_generation_started", {
                    "project_id": project_id,
                    "prompt": prompt,
                    "project_type": project_type,
                    "workflow": workflow,
                    "config": self.config.__dict__
                }, project_id)

                init_result = await self._initialize_project_structure(
                    project_path, project_id, prompt, parsed_project_type
                )

                # Phase 2: Requirements Analysis
                analysis_result = await self._execute_requirements_analysis(
                    prompt, project_path, project_id, project_type
                )

                # Phase 3: Architecture Design
                architecture_result = await self._execute_architecture_design(
                    prompt, project_path, project_id, project_type, analysis_result
                )

                # Phase 4: Code Generation & File Creation
                development_result = await self._execute_comprehensive_development(
                    prompt, project_path, project_id, project_type, architecture_result
                )

                # Phase 5: Configuration & Infrastructure Files
                infrastructure_result = await self._generate_infrastructure_files(
                    project_path, project_id, parsed_project_type, development_result
                )

                # Phase 6: Quality Assurance & Testing
                qa_result = await self._execute_quality_assurance(
                    prompt, project_path, project_id, project_type, development_result
                )

                # Phase 7: Documentation Generation
                documentation_result = await self._generate_comprehensive_documentation(
                    project_path, project_id, {
                        "analysis": analysis_result,
                        "architecture": architecture_result,
                        "development": development_result,
                        "infrastructure": infrastructure_result
                    }
                )

                # Phase 8: Project Packaging & Final Output
                packaging_result = await self._package_final_project(
                    project_path, project_id, {
                        "analysis": analysis_result,
                        "architecture": architecture_result,
                        "development": development_result,
                        "infrastructure": infrastructure_result,
                        "qa": qa_result,
                        "documentation": documentation_result
                    }
                )

                await self._drop_pheromone("project_generation_completed", {
                    "project_id": project_id,
                    "project_path": project_path,
                    "package_path": packaging_result.get("package_path"),
                    "total_files": packaging_result.get("total_files", 0),
                    "package_size": packaging_result.get("package_size", 0),
                    "completion_time": datetime.now().isoformat()
                }, project_id)

                return {
                    "success": True,
                    "project_id": project_id,
                    "project_path": project_path,
                    "package_path": packaging_result.get("package_path"),
                    "phases_completed": 8,
                    "total_files": packaging_result.get("total_files", 0),
                    "package_size": packaging_result.get("package_size", 0),
                    "results": {
                        "initialization": init_result,
                        "analysis": analysis_result,
                        "architecture": architecture_result,
                        "development": development_result,
                        "infrastructure": infrastructure_result,
                        "qa": qa_result,
                        "documentation": documentation_result,
                        "packaging": packaging_result
                    }
                }

        except Exception as e:
            await self._drop_pheromone("project_generation_failed", {
                "project_id": project_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }, project_id)

            logger.error(f"Project generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "project_id": project_id
            }
    
    async def _initialize_project_structure(self, project_path: str, project_id: str,
                                           prompt: str, project_type: ProjectType) -> Dict[str, Any]:
        """Initialize comprehensive project structure based on project type"""

        await self._drop_pheromone("phase_started", {"phase": "initialization"}, project_id)

        project_dir = Path(project_path)
        project_dir.mkdir(parents=True, exist_ok=True)

        # Get template for project type
        template = self.templates.get(project_type, self.templates[ProjectType.WEB_APPLICATION])

        # Create directory structure from template
        directories_created = []
        for dir_name in template.directories:
            dir_path = project_dir / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            directories_created.append(dir_name)

        # Create project metadata
        metadata = {
            "project_id": project_id,
            "name": project_dir.name,
            "description": prompt,
            "project_type": project_type.value,
            "template": template.name,
            "created_at": datetime.now().isoformat(),
            "aetherforge_version": "1.0.0",
            "status": "generating",
            "config": self.config.__dict__,
            "phases": {
                "initialization": "completed",
                "analysis": "pending",
                "architecture": "pending",
                "development": "pending",
                "infrastructure": "pending",
                "qa": "pending",
                "documentation": "pending",
                "packaging": "pending"
            },
            "dependencies": template.dependencies,
            "build_commands": template.build_commands,
            "test_commands": template.test_commands,
            "deployment_config": template.deployment_config
        }

        metadata_file = project_dir / ".aetherforge.json"
        metadata_file.write_text(json.dumps(metadata, indent=2), encoding='utf-8')

        # Generate initial configuration files
        files_created = [".aetherforge.json"]

        # Generate .gitignore
        gitignore_content = await FileGenerators.generate_gitignore(project_type)
        gitignore_file = project_dir / ".gitignore"
        gitignore_file.write_text(gitignore_content, encoding='utf-8')
        files_created.append(".gitignore")

        # Generate README.md
        readme_content = await FileGenerators.generate_readme(project_dir.name, prompt, project_id, project_type, template)
        readme_file = project_dir / "README.md"
        readme_file.write_text(readme_content, encoding='utf-8')
        files_created.append("README.md")

        # Generate LICENSE
        if self.config.include_docs:
            license_content = await FileGenerators.generate_license()
            license_file = project_dir / "LICENSE"
            license_file.write_text(license_content, encoding='utf-8')
            files_created.append("LICENSE")

        # Generate environment example
        env_example_content = await FileGenerators.generate_env_example(project_type)
        env_file = project_dir / ".env.example"
        env_file.write_text(env_example_content, encoding='utf-8')
        files_created.append(".env.example")

        # Generate package configuration based on project type
        if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE, ProjectType.MOBILE_APPLICATION]:
            package_json_content = await FileGenerators.generate_package_json(project_dir.name, template)
            package_file = project_dir / "package.json"
            package_file.write_text(package_json_content, encoding='utf-8')
            files_created.append("package.json")

        elif project_type == ProjectType.DATA_PLATFORM:
            requirements_content = await self._generate_requirements_txt(template)
            requirements_file = project_dir / "requirements.txt"
            requirements_file.write_text(requirements_content, encoding='utf-8')
            files_created.append("requirements.txt")

            pyproject_content = await self._generate_pyproject_toml(project_dir.name, template)
            pyproject_file = project_dir / "pyproject.toml"
            pyproject_file.write_text(pyproject_content, encoding='utf-8')
            files_created.append("pyproject.toml")

        await self._drop_pheromone("phase_completed", {
            "phase": "initialization",
            "directories_created": len(directories_created),
            "files_created": files_created,
            "project_type": project_type.value,
            "template": template.name
        }, project_id)

        return {
            "success": True,
            "directories_created": directories_created,
            "files_created": files_created,
            "metadata": metadata,
            "template": template.name,
            "project_type": project_type.value
        }
    
    async def _execute_requirements_analysis(self, prompt: str, project_path: str, 
                                           project_id: str, project_type: str) -> Dict[str, Any]:
        """Execute requirements analysis phase"""
        
        await self._drop_pheromone("phase_started", {"phase": "requirements_analysis"}, project_id)
        
        try:
            # Create analyst executor
            analyst = create_agent_executor("analyst")
            
            # Execute analysis
            context = {
                "prompt": prompt,
                "project_path": project_path,
                "project_id": project_id,
                "project_type": project_type,
                "phase": "requirements_analysis"
            }
            
            result = await analyst.execute(context)
            
            # Update project metadata
            await self._update_project_metadata(project_path, "analysis", "completed")
            
            await self._drop_pheromone("phase_completed", {
                "phase": "requirements_analysis",
                "success": result.get("success", False),
                "outputs": result.get("outputs", [])
            }, project_id)
            
            return result
            
        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "requirements_analysis",
                "error": str(e)
            }, project_id)
            raise e
    
    async def _execute_architecture_design(self, prompt: str, project_path: str, 
                                         project_id: str, project_type: str, 
                                         analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute architecture design phase"""
        
        await self._drop_pheromone("phase_started", {"phase": "architecture_design"}, project_id)
        
        try:
            # Create architect executor
            architect = create_agent_executor("architect")
            
            # Execute architecture design
            context = {
                "prompt": prompt,
                "project_path": project_path,
                "project_id": project_id,
                "project_type": project_type,
                "phase": "architecture_design",
                "analysis_result": analysis_result
            }
            
            result = await architect.execute(context)
            
            # Update project metadata
            await self._update_project_metadata(project_path, "architecture", "completed")
            
            await self._drop_pheromone("phase_completed", {
                "phase": "architecture_design",
                "success": result.get("success", False),
                "outputs": result.get("outputs", [])
            }, project_id)
            
            return result
            
        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "architecture_design",
                "error": str(e)
            }, project_id)
            raise e
    
    async def _execute_comprehensive_development(self, prompt: str, project_path: str,
                                               project_id: str, project_type: str,
                                               architecture_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute comprehensive development phase with real code generation"""

        await self._drop_pheromone("phase_started", {"phase": "comprehensive_development"}, project_id)

        try:
            project_dir = Path(project_path)
            parsed_project_type = ProjectType(project_type.lower()) if project_type else ProjectType.WEB_APPLICATION
            template = self.templates.get(parsed_project_type, self.templates[ProjectType.WEB_APPLICATION])

            # Create developer executor
            developer = create_agent_executor("developer")

            # Execute development with enhanced context
            context = {
                "prompt": prompt,
                "project_path": project_path,
                "project_id": project_id,
                "project_type": project_type,
                "phase": "comprehensive_development",
                "architecture_result": architecture_result,
                "template": template.__dict__,
                "config": self.config.__dict__
            }

            result = await developer.execute(context)

            # Generate additional source code files based on project type
            additional_files = await self._generate_source_code_files(
                project_dir, parsed_project_type, template, architecture_result
            )

            # Combine results
            total_files_created = result.get("files_created", 0) + len(additional_files)
            all_outputs = result.get("outputs", []) + additional_files

            # Update project metadata
            await self._update_project_metadata(project_path, "development", "completed")

            await self._drop_pheromone("phase_completed", {
                "phase": "comprehensive_development",
                "success": result.get("success", False),
                "outputs": all_outputs,
                "files_created": total_files_created,
                "additional_files": additional_files
            }, project_id)

            return {
                **result,
                "files_created": total_files_created,
                "outputs": all_outputs,
                "additional_files": additional_files
            }

        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "comprehensive_development",
                "error": str(e)
            }, project_id)
            raise e

    async def _generate_source_code_files(self, project_dir: Path, project_type: ProjectType,
                                        template: ProjectTemplate, architecture_result: Dict[str, Any]) -> List[str]:
        """Generate additional source code files based on project type"""

        files_created = []

        try:
            if project_type == ProjectType.WEB_APPLICATION:
                # Generate React components
                components_dir = project_dir / "src" / "components"

                # App component
                app_component = components_dir / "App.tsx"
                app_content = await self._generate_react_app_component()
                app_component.write_text(app_content, encoding='utf-8')
                files_created.append("src/components/App.tsx")

                # Header component
                header_component = components_dir / "Header.tsx"
                header_content = await self._generate_react_header_component()
                header_component.write_text(header_content, encoding='utf-8')
                files_created.append("src/components/Header.tsx")

                # Main page
                pages_dir = project_dir / "src" / "pages"
                home_page = pages_dir / "Home.tsx"
                home_content = await self._generate_react_home_page()
                home_page.write_text(home_content, encoding='utf-8')
                files_created.append("src/pages/Home.tsx")

                # Styles
                styles_dir = project_dir / "src" / "styles"
                global_styles = styles_dir / "globals.css"
                styles_content = await self._generate_global_styles()
                global_styles.write_text(styles_content, encoding='utf-8')
                files_created.append("src/styles/globals.css")

                # Utils
                utils_dir = project_dir / "src" / "utils"
                api_utils = utils_dir / "api.ts"
                api_content = await self._generate_api_utils()
                api_utils.write_text(api_content, encoding='utf-8')
                files_created.append("src/utils/api.ts")

            elif project_type == ProjectType.API_SERVICE:
                # Generate Express.js API structure
                src_dir = project_dir / "src"

                # Main app file
                app_file = src_dir / "app.ts"
                app_content = await self._generate_express_app()
                app_file.write_text(app_content, encoding='utf-8')
                files_created.append("src/app.ts")

                # Server file
                server_file = src_dir / "server.ts"
                server_content = await self._generate_express_server()
                server_file.write_text(server_content, encoding='utf-8')
                files_created.append("src/server.ts")

                # Controllers
                controllers_dir = src_dir / "controllers"
                user_controller = controllers_dir / "userController.ts"
                controller_content = await self._generate_user_controller()
                user_controller.write_text(controller_content, encoding='utf-8')
                files_created.append("src/controllers/userController.ts")

                # Routes
                routes_dir = src_dir / "routes"
                user_routes = routes_dir / "userRoutes.ts"
                routes_content = await self._generate_user_routes()
                user_routes.write_text(routes_content, encoding='utf-8')
                files_created.append("src/routes/userRoutes.ts")

                # Middleware
                middleware_dir = src_dir / "middleware"
                auth_middleware = middleware_dir / "auth.ts"
                auth_content = await self._generate_auth_middleware()
                auth_middleware.write_text(auth_content, encoding='utf-8')
                files_created.append("src/middleware/auth.ts")

            elif project_type == ProjectType.DATA_PLATFORM:
                # Generate Python data platform structure
                src_dir = project_dir / "src"

                # Main module
                main_file = src_dir / "__init__.py"
                main_file.write_text("# Data Platform Main Module\n", encoding='utf-8')
                files_created.append("src/__init__.py")

                # Data pipeline
                pipelines_dir = src_dir / "pipelines"
                pipeline_file = pipelines_dir / "data_pipeline.py"
                pipeline_content = await self._generate_data_pipeline()
                pipeline_file.write_text(pipeline_content, encoding='utf-8')
                files_created.append("src/pipelines/data_pipeline.py")

                # Data processors
                processors_dir = src_dir / "processors"
                processor_file = processors_dir / "data_processor.py"
                processor_content = await self._generate_data_processor()
                processor_file.write_text(processor_content, encoding='utf-8')
                files_created.append("src/processors/data_processor.py")

                # Models
                models_dir = src_dir / "models"
                model_file = models_dir / "data_model.py"
                model_content = await self._generate_data_model()
                model_file.write_text(model_content, encoding='utf-8')
                files_created.append("src/models/data_model.py")

            logger.info(f"Generated {len(files_created)} additional source code files")

        except Exception as e:
            logger.error(f"Error generating source code files: {e}")

        return files_created

    async def _generate_infrastructure_files(self, project_path: str, project_id: str,
                                           project_type: ProjectType, development_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate infrastructure and configuration files"""

        await self._drop_pheromone("phase_started", {"phase": "infrastructure"}, project_id)

        try:
            project_dir = Path(project_path)
            template = self.templates.get(project_type, self.templates[ProjectType.WEB_APPLICATION])
            files_created = []

            # Generate Docker files if enabled
            if self.config.include_docker:
                dockerfile_content = await FileGenerators.generate_dockerfile(project_type)
                dockerfile = project_dir / "Dockerfile"
                dockerfile.write_text(dockerfile_content, encoding='utf-8')
                files_created.append("Dockerfile")

                docker_compose_content = await FileGenerators.generate_docker_compose(project_type, template)
                docker_compose_file = project_dir / "docker-compose.yml"
                docker_compose_file.write_text(docker_compose_content, encoding='utf-8')
                files_created.append("docker-compose.yml")

                dockerignore_content = await self._generate_dockerignore()
                dockerignore_file = project_dir / ".dockerignore"
                dockerignore_file.write_text(dockerignore_content, encoding='utf-8')
                files_created.append(".dockerignore")

            # Generate CI/CD configuration if enabled
            if self.config.include_ci_cd:
                github_actions_dir = project_dir / ".github" / "workflows"
                github_actions_dir.mkdir(parents=True, exist_ok=True)

                ci_content = await self._generate_ci_cd_config(project_type)
                ci_file = github_actions_dir / "ci.yml"
                ci_file.write_text(ci_content, encoding='utf-8')
                files_created.append(".github/workflows/ci.yml")

            # Generate configuration files based on project type
            if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE]:
                # TypeScript configuration
                tsconfig_content = await FileGenerators.generate_tsconfig(project_type)
                tsconfig_file = project_dir / "tsconfig.json"
                tsconfig_file.write_text(tsconfig_content, encoding='utf-8')
                files_created.append("tsconfig.json")

                # ESLint configuration
                eslint_content = await FileGenerators.generate_eslint_config(project_type)
                eslint_file = project_dir / ".eslintrc.json"
                eslint_file.write_text(eslint_content, encoding='utf-8')
                files_created.append(".eslintrc.json")

                # Prettier configuration
                prettier_content = await FileGenerators.generate_prettier_config()
                prettier_file = project_dir / ".prettierrc"
                prettier_file.write_text(prettier_content, encoding='utf-8')
                files_created.append(".prettierrc")

                # Jest configuration if testing enabled
                if self.config.include_tests:
                    jest_content = await FileGenerators.generate_jest_config(project_type)
                    jest_file = project_dir / "jest.config.js"
                    jest_file.write_text(jest_content, encoding='utf-8')
                    files_created.append("jest.config.js")

                # Build tool configuration
                if project_type == ProjectType.WEB_APPLICATION:
                    vite_content = await self._generate_vite_config()
                    vite_file = project_dir / "vite.config.ts"
                    vite_file.write_text(vite_content, encoding='utf-8')
                    files_created.append("vite.config.ts")

            # Generate deployment configuration if enabled
            if self.config.include_deployment:
                deployment_content = await self._generate_deployment_yaml(project_type, template)
                deployment_file = project_dir / "deployment.yml"
                deployment_file.write_text(deployment_content, encoding='utf-8')
                files_created.append("deployment.yml")

                # Generate Makefile for common tasks
                makefile_content = await self._generate_makefile(project_type, template)
                makefile = project_dir / "Makefile"
                makefile.write_text(makefile_content, encoding='utf-8')
                files_created.append("Makefile")

            # Update project metadata
            await self._update_project_metadata(project_path, "infrastructure", "completed")

            await self._drop_pheromone("phase_completed", {
                "phase": "infrastructure",
                "files_created": files_created,
                "docker_enabled": self.config.include_docker,
                "ci_cd_enabled": self.config.include_ci_cd,
                "deployment_enabled": self.config.include_deployment
            }, project_id)

            return {
                "success": True,
                "files_created": files_created,
                "docker_enabled": self.config.include_docker,
                "ci_cd_enabled": self.config.include_ci_cd,
                "deployment_enabled": self.config.include_deployment
            }

        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "infrastructure",
                "error": str(e)
            }, project_id)
            raise e

    async def _generate_comprehensive_documentation(self, project_path: str, project_id: str,
                                                  phase_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive project documentation"""

        await self._drop_pheromone("phase_started", {"phase": "documentation"}, project_id)

        try:
            project_dir = Path(project_path)
            docs_dir = project_dir / "docs"
            files_created = []

            if self.config.include_docs:
                # API documentation
                api_docs_dir = docs_dir / "api"
                api_docs_dir.mkdir(parents=True, exist_ok=True)

                api_readme = api_docs_dir / "README.md"
                api_content = await self._generate_api_documentation(phase_results)
                api_readme.write_text(api_content, encoding='utf-8')
                files_created.append("docs/api/README.md")

                # User guide
                user_guide_dir = docs_dir / "user-guide"
                user_guide_dir.mkdir(parents=True, exist_ok=True)

                user_guide = user_guide_dir / "getting-started.md"
                guide_content = await self._generate_user_guide(phase_results)
                user_guide.write_text(guide_content, encoding='utf-8')
                files_created.append("docs/user-guide/getting-started.md")

                # Architecture documentation
                architecture_doc = docs_dir / "architecture.md"
                arch_content = await self._generate_architecture_documentation(phase_results)
                architecture_doc.write_text(arch_content, encoding='utf-8')
                files_created.append("docs/architecture.md")

                # Deployment guide
                deployment_doc = docs_dir / "deployment.md"
                deploy_content = await self._generate_deployment_documentation(phase_results)
                deployment_doc.write_text(deploy_content, encoding='utf-8')
                files_created.append("docs/deployment.md")

                # Contributing guide
                contributing_doc = project_dir / "CONTRIBUTING.md"
                contrib_content = await self._generate_contributing_guide()
                contributing_doc.write_text(contrib_content, encoding='utf-8')
                files_created.append("CONTRIBUTING.md")

                # Changelog
                changelog_doc = project_dir / "CHANGELOG.md"
                changelog_content = await self._generate_changelog()
                changelog_doc.write_text(changelog_content, encoding='utf-8')
                files_created.append("CHANGELOG.md")

            # Update project metadata
            await self._update_project_metadata(project_path, "documentation", "completed")

            await self._drop_pheromone("phase_completed", {
                "phase": "documentation",
                "files_created": files_created,
                "docs_enabled": self.config.include_docs
            }, project_id)

            return {
                "success": True,
                "files_created": files_created,
                "docs_enabled": self.config.include_docs
            }

        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "documentation",
                "error": str(e)
            }, project_id)
            raise e

    async def _package_final_project(self, project_path: str, project_id: str,
                                   phase_results: Dict[str, Any]) -> Dict[str, Any]:
        """Package the final project with all generated files"""

        await self._drop_pheromone("phase_started", {"phase": "packaging"}, project_id)

        try:
            project_dir = Path(project_path)

            # Count total files created
            all_files = list(project_dir.rglob("*"))
            total_files = len([f for f in all_files if f.is_file()])

            # Calculate project size
            total_size = sum(f.stat().st_size for f in all_files if f.is_file())

            # Create final project summary
            summary = {
                "project_id": project_id,
                "completion_time": datetime.now().isoformat(),
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "phases_completed": len(phase_results),
                "config": self.config.__dict__,
                "phase_results": {
                    phase: {
                        "success": result.get("success", False),
                        "files_created": len(result.get("files_created", [])),
                        "summary": result.get("summary", "")
                    }
                    for phase, result in phase_results.items()
                },
                "file_breakdown": await self._analyze_file_breakdown(project_dir)
            }

            # Save project summary
            summary_file = project_dir / "docs" / "project_summary.json"
            summary_file.parent.mkdir(parents=True, exist_ok=True)
            summary_file.write_text(json.dumps(summary, indent=2), encoding='utf-8')

            # Update final README with completion status
            await self._update_final_readme(project_dir, summary)

            # Package project based on configuration
            package_path = None
            if self.config.package_format != PackageFormat.DIRECTORY:
                package_path = await self._create_project_package(project_dir, project_id)

            # Update project metadata to completed
            await self._update_project_metadata(project_path, "packaging", "completed")
            await self._update_project_status(project_path, "completed")

            await self._drop_pheromone("phase_completed", {
                "phase": "packaging",
                "total_files": total_files,
                "total_size_mb": summary["total_size_mb"],
                "package_path": package_path,
                "project_completed": True
            }, project_id)

            return {
                "success": True,
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": summary["total_size_mb"],
                "package_path": package_path,
                "summary": summary,
                "files_created": ["docs/project_summary.json"]
            }

        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "packaging",
                "error": str(e)
            }, project_id)
            raise e

    async def _analyze_file_breakdown(self, project_dir: Path) -> Dict[str, int]:
        """Analyze file breakdown by type and directory"""

        breakdown = {
            "by_extension": {},
            "by_directory": {},
            "total_lines": 0
        }

        for file_path in project_dir.rglob("*"):
            if file_path.is_file():
                # Count by extension
                ext = file_path.suffix.lower() or "no_extension"
                breakdown["by_extension"][ext] = breakdown["by_extension"].get(ext, 0) + 1

                # Count by directory
                rel_dir = str(file_path.parent.relative_to(project_dir))
                breakdown["by_directory"][rel_dir] = breakdown["by_directory"].get(rel_dir, 0) + 1

                # Count lines for text files
                if ext in ['.js', '.ts', '.tsx', '.jsx', '.py', '.md', '.json', '.yml', '.yaml', '.css', '.html']:
                    try:
                        content = file_path.read_text(encoding='utf-8')
                        breakdown["total_lines"] += len(content.splitlines())
                    except (UnicodeDecodeError, FileNotFoundError, PermissionError) as e:
                        logger.debug(f"Could not read file {file_path} for line counting: {e}")
                    except Exception as e:
                        logger.warning(f"Unexpected error reading file {file_path}: {e}")

        return breakdown

    async def _create_project_package(self, project_dir: Path, project_id: str) -> str:
        """Create a packaged version of the project"""

        package_handler = self.packaging_handlers.get(self.config.package_format)
        if not package_handler:
            raise ValueError(f"Unsupported package format: {self.config.package_format}")

        return await package_handler(project_dir, project_id)

    async def _package_as_zip(self, project_dir: Path, project_id: str) -> str:
        """Package project as ZIP file"""

        package_path = project_dir.parent / f"{project_id}.zip"

        with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in project_dir.rglob("*"):
                if file_path.is_file():
                    arcname = file_path.relative_to(project_dir.parent)
                    zipf.write(file_path, arcname)

        return str(package_path)

    async def _package_as_tar_gz(self, project_dir: Path, project_id: str) -> str:
        """Package project as TAR.GZ file"""

        package_path = project_dir.parent / f"{project_id}.tar.gz"

        with tarfile.open(package_path, 'w:gz') as tar:
            tar.add(project_dir, arcname=project_dir.name)

        return str(package_path)

    async def _package_as_tar_bz2(self, project_dir: Path, project_id: str) -> str:
        """Package project as TAR.BZ2 file"""

        package_path = project_dir.parent / f"{project_id}.tar.bz2"

        with tarfile.open(package_path, 'w:bz2') as tar:
            tar.add(project_dir, arcname=project_dir.name)

        return str(package_path)

    async def _package_as_directory(self, project_dir: Path, project_id: str) -> str:
        """Keep project as directory (no packaging)"""
        return str(project_dir)

    async def _update_final_readme(self, project_dir: Path, summary: Dict[str, Any]):
        """Update the final README with completion status"""

        readme_file = project_dir / "README.md"
        if readme_file.exists():
            try:
                current_content = readme_file.read_text(encoding='utf-8')

                # Replace status section
                status_section = f"""
## ✅ Project Generation Complete

**Status**: ✅ **Completed Successfully**
**Total Files**: {summary['total_files']}
**Project Size**: {summary['total_size_mb']} MB
**Completion Time**: {summary['completion_time']}
**Phases Completed**: {summary['phases_completed']}/8

### Generated Components

"""

                # Add file breakdown
                for phase, result in summary['phase_results'].items():
                    if result['success']:
                        status_section += f"- ✅ **{phase.title()}**: {result['files_created']} files\n"
                    else:
                        status_section += f"- ❌ **{phase.title()}**: Failed\n"

                status_section += f"""
### Quick Start

1. Install dependencies:
   ```bash
   npm install  # or pip install -r requirements.txt
   ```

2. Start development server:
   ```bash
   npm run dev  # or python main.py
   ```

3. Run tests:
   ```bash
   npm test  # or pytest
   ```

For detailed documentation, see the `docs/` directory.
"""

                # Replace the status section in README
                if "## Development Status" in current_content:
                    parts = current_content.split("## Development Status")
                    new_content = parts[0] + status_section
                else:
                    new_content = current_content + "\n" + status_section

                readme_file.write_text(new_content, encoding='utf-8')

            except Exception as e:
                logger.warning(f"Failed to update final README: {e}")

    # File generation methods that are not in FileGenerators yet
    async def _generate_dockerignore(self) -> str:
        """Generate .dockerignore file"""
        return """node_modules
npm-debug.log*
.git
.gitignore
README.md
.env
.nyc_output
coverage
.coverage
.pytest_cache
"""

    async def _generate_ci_cd_config(self, project_type: ProjectType) -> str:
        """Generate CI/CD configuration"""
        if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE]:
            return """name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - run: npm ci
    - run: npm run build --if-present
    - run: npm test
    - run: npm run lint

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
"""
        else:
            return """name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Lint with flake8
      run: |
        flake8 src tests

    - name: Test with pytest
      run: |
        pytest --cov=src tests/
"""

    async def _generate_vite_config(self) -> str:
        """Generate Vite configuration"""
        return """import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})
"""

    async def _generate_requirements_txt(self, template: ProjectTemplate) -> str:
        """Generate requirements.txt for Python projects"""
        requirements = []

        for dep_type, deps in template.dependencies.items():
            if dep_type in ["runtime", "ml", "database"]:
                requirements.extend(deps)

        # Add common Python dependencies
        if not requirements:
            requirements = [
                "fastapi>=0.104.0",
                "uvicorn[standard]>=0.24.0",
                "pydantic>=2.5.0",
                "sqlalchemy>=2.0.0",
                "alembic>=1.13.0",
                "python-multipart>=0.0.6",
                "python-jose[cryptography]>=3.3.0",
                "passlib[bcrypt]>=1.7.4",
                "python-dotenv>=1.0.0"
            ]

        return "\n".join(requirements) + "\n"

    async def _generate_pyproject_toml(self, project_name: str, template: ProjectTemplate) -> str:
        """Generate pyproject.toml for Python projects"""
        return f"""[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "{project_name.lower().replace(' ', '-')}"
version = "1.0.0"
description = "Generated {template.name} project"
authors = [
    {{name = "Aetherforge AI", email = "<EMAIL>"}}
]
license = {{text = "MIT"}}
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

[project.urls]
Homepage = "https://github.com/aetherforge/{project_name.lower().replace(' ', '-')}"
Repository = "https://github.com/aetherforge/{project_name.lower().replace(' ', '-')}.git"
Issues = "https://github.com/aetherforge/{project_name.lower().replace(' ', '-')}/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
"""

    async def _generate_deployment_yaml(self, project_type: ProjectType, template: ProjectTemplate) -> str:
        """Generate deployment configuration"""
        return f"""# Kubernetes Deployment Configuration
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {template.name.lower().replace(' ', '-')}-app
  labels:
    app: {template.name.lower().replace(' ', '-')}
spec:
  replicas: 3
  selector:
    matchLabels:
      app: {template.name.lower().replace(' ', '-')}
  template:
    metadata:
      labels:
        app: {template.name.lower().replace(' ', '-')}
    spec:
      containers:
      - name: app
        image: {template.name.lower().replace(' ', '-')}:latest
        ports:
        - containerPort: {"3000" if project_type == ProjectType.WEB_APPLICATION else "8000"}
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: {template.name.lower().replace(' ', '-')}-service
spec:
  selector:
    app: {template.name.lower().replace(' ', '-')}
  ports:
    - protocol: TCP
      port: 80
      targetPort: {"3000" if project_type == ProjectType.WEB_APPLICATION else "8000"}
  type: LoadBalancer
"""

    async def _generate_makefile(self, project_type: ProjectType, template: ProjectTemplate) -> str:
        """Generate Makefile for common tasks"""
        if project_type in [ProjectType.WEB_APPLICATION, ProjectType.API_SERVICE]:
            return """# Makefile for Node.js project

.PHONY: install dev build test lint clean docker-build docker-run

install:
	npm install

dev:
	npm run dev

build:
	npm run build

test:
	npm test

lint:
	npm run lint

lint-fix:
	npm run lint:fix

clean:
	rm -rf node_modules dist build

docker-build:
	docker build -t $(shell basename $(CURDIR)) .

docker-run:
	docker run -p 3000:3000 $(shell basename $(CURDIR))

docker-compose-up:
	docker-compose up --build

docker-compose-down:
	docker-compose down

help:
	@echo "Available commands:"
	@echo "  install       - Install dependencies"
	@echo "  dev          - Start development server"
	@echo "  build        - Build for production"
	@echo "  test         - Run tests"
	@echo "  lint         - Lint code"
	@echo "  clean        - Clean build artifacts"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
"""
        else:
            return """# Makefile for Python project

.PHONY: install dev test lint clean docker-build docker-run

install:
	pip install -r requirements.txt
	pip install -r requirements-dev.txt

dev:
	python src/main.py

test:
	pytest

test-cov:
	pytest --cov=src tests/

lint:
	flake8 src tests
	black --check src tests
	isort --check-only src tests

format:
	black src tests
	isort src tests

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov

docker-build:
	docker build -t $(shell basename $(CURDIR)) .

docker-run:
	docker run -p 8000:8000 $(shell basename $(CURDIR))

help:
	@echo "Available commands:"
	@echo "  install      - Install dependencies"
	@echo "  dev         - Start development server"
	@echo "  test        - Run tests"
	@echo "  test-cov    - Run tests with coverage"
	@echo "  lint        - Lint code"
	@echo "  format      - Format code"
	@echo "  clean       - Clean build artifacts"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
"""

    # Documentation generation methods
    async def _generate_api_documentation(self, phase_results: Dict[str, Any]) -> str:
        """Generate API documentation"""
        return """# API Documentation

## Overview

This API provides endpoints for managing the application's core functionality.

## Base URL

```
http://localhost:8000/api
```

## Authentication

All API endpoints require authentication using JWT tokens.

```bash
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### Health Check

```http
GET /health
```

Returns the health status of the API.

### Users

#### Get All Users
```http
GET /api/users
```

#### Get User by ID
```http
GET /api/users/{id}
```

#### Create User
```http
POST /api/users
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

#### Update User
```http
PUT /api/users/{id}
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

#### Delete User
```http
DELETE /api/users/{id}
```

## Error Responses

All endpoints return standard HTTP status codes and JSON error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Rate Limiting

API requests are limited to 100 requests per 15-minute window per IP address.
"""

    async def _generate_user_guide(self, phase_results: Dict[str, Any]) -> str:
        """Generate user guide"""
        return """# User Guide

## Getting Started

Welcome to your new application! This guide will help you get up and running quickly.

### Prerequisites

Before you begin, ensure you have the following installed:

- Node.js 18+ (for web applications)
- Python 3.9+ (for data platforms)
- Docker (optional, for containerized deployment)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd <project-directory>
   ```

2. **Install dependencies**
   ```bash
   npm install  # For Node.js projects
   # or
   pip install -r requirements.txt  # For Python projects
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the development server**
   ```bash
   npm run dev  # For Node.js projects
   # or
   python src/main.py  # For Python projects
   ```

### Configuration

The application can be configured using environment variables. See `.env.example` for all available options.

### Usage

#### Web Interface

Navigate to `http://localhost:3000` to access the web interface.

#### API Usage

The API is available at `http://localhost:8000/api`. See the [API Documentation](../api/README.md) for detailed endpoint information.

### Troubleshooting

#### Common Issues

1. **Port already in use**
   - Change the PORT environment variable in your .env file

2. **Database connection errors**
   - Ensure your database is running and connection details are correct

3. **Permission errors**
   - Check file permissions and ensure you have write access to the project directory

### Support

For additional help, please:
- Check the documentation in the `docs/` directory
- Review the project's README.md
- Create an issue in the project repository
"""

    async def _generate_architecture_documentation(self, phase_results: Dict[str, Any]) -> str:
        """Generate architecture documentation"""
        return """# Architecture Documentation

## Overview

This document describes the high-level architecture of the application.

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (React/Vue)   │◄──►│   (Node.js)     │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Cache Layer   │
                       │   (Redis)       │
                       └─────────────────┘
```

## Components

### Frontend Layer
- **Technology**: React with TypeScript
- **Responsibilities**: User interface, client-side routing, state management
- **Key Features**: Responsive design, real-time updates, form validation

### Backend API Layer
- **Technology**: Node.js with Express
- **Responsibilities**: Business logic, data validation, authentication
- **Key Features**: RESTful API, JWT authentication, rate limiting

### Database Layer
- **Technology**: PostgreSQL
- **Responsibilities**: Data persistence, relationships, transactions
- **Key Features**: ACID compliance, indexing, backup strategies

### Cache Layer
- **Technology**: Redis
- **Responsibilities**: Session storage, caching, real-time features
- **Key Features**: In-memory storage, pub/sub, TTL support

## Design Patterns

### Backend Patterns
- **Repository Pattern**: Data access abstraction
- **Service Layer**: Business logic encapsulation
- **Middleware Pattern**: Request/response processing
- **Factory Pattern**: Object creation

### Frontend Patterns
- **Component Pattern**: Reusable UI components
- **Container/Presenter**: Separation of concerns
- **Observer Pattern**: State management
- **Higher-Order Components**: Code reuse

## Security Considerations

- JWT-based authentication
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration
- Rate limiting
- Environment variable security

## Performance Optimizations

- Database indexing
- Query optimization
- Caching strategies
- Code splitting
- Image optimization
- CDN integration
- Compression

## Scalability

- Horizontal scaling capabilities
- Load balancing strategies
- Database sharding considerations
- Microservices migration path
- Container orchestration
"""

    async def _generate_deployment_documentation(self, phase_results: Dict[str, Any]) -> str:
        """Generate deployment documentation"""
        return """# Deployment Guide

## Overview

This guide covers deployment options for your application.

## Deployment Options

### 1. Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### 2. Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
```

### 3. Docker Deployment

```bash
# Build Docker image
docker build -t myapp .

# Run container
docker run -p 3000:3000 myapp

# Or use Docker Compose
docker-compose up --build
```

### 4. Cloud Deployment

#### Vercel (Recommended for frontend)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

#### Heroku

```bash
# Install Heroku CLI
# Create Heroku app
heroku create myapp

# Deploy
git push heroku main
```

#### AWS

1. **EC2 Deployment**
   - Launch EC2 instance
   - Install Node.js/Python
   - Clone repository
   - Install dependencies
   - Start application

2. **ECS Deployment**
   - Build Docker image
   - Push to ECR
   - Create ECS service
   - Configure load balancer

#### Kubernetes

```yaml
# Apply Kubernetes manifests
kubectl apply -f deployment.yml
```

## Environment Configuration

### Production Environment Variables

```bash
NODE_ENV=production
DATABASE_URL=********************************/db
REDIS_URL=redis://host:6379
JWT_SECRET=your-production-secret
```

### SSL/TLS Configuration

- Use Let's Encrypt for free SSL certificates
- Configure reverse proxy (Nginx/Apache)
- Enable HTTPS redirects

## Monitoring and Logging

### Application Monitoring
- Health check endpoints
- Performance metrics
- Error tracking
- Uptime monitoring

### Log Management
- Structured logging
- Log aggregation
- Log rotation
- Error alerting

## Backup and Recovery

### Database Backups
- Automated daily backups
- Point-in-time recovery
- Cross-region replication

### Application Backups
- Code repository backups
- Configuration backups
- Asset backups

## Security Checklist

- [ ] Environment variables secured
- [ ] Database credentials rotated
- [ ] SSL/TLS enabled
- [ ] Firewall configured
- [ ] Security headers set
- [ ] Dependencies updated
- [ ] Vulnerability scanning enabled

## Performance Optimization

- Enable gzip compression
- Configure CDN
- Optimize database queries
- Implement caching
- Monitor resource usage
"""

    async def _generate_contributing_guide(self) -> str:
        """Generate contributing guide"""
        return """# Contributing Guide

Thank you for your interest in contributing to this project!

## Getting Started

1. **Fork the repository**
2. **Clone your fork**
   ```bash
   git clone https://github.com/yourusername/project-name.git
   ```
3. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```

## Development Setup

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

## Code Standards

### Style Guide
- Use TypeScript for type safety
- Follow ESLint configuration
- Use Prettier for formatting
- Write meaningful commit messages

### Testing
- Write unit tests for new features
- Maintain test coverage above 80%
- Run tests before submitting PR

```bash
npm test
npm run test:coverage
```

### Documentation
- Update README.md if needed
- Add JSDoc comments for functions
- Update API documentation

## Pull Request Process

1. **Ensure tests pass**
   ```bash
   npm test
   npm run lint
   ```

2. **Update documentation**
3. **Create pull request**
4. **Request review**
5. **Address feedback**

## Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting
- `refactor`: Code restructuring
- `test`: Adding tests
- `chore`: Maintenance

## Code of Conduct

- Be respectful and inclusive
- Provide constructive feedback
- Help others learn and grow
- Follow project guidelines

## Questions?

Feel free to open an issue for questions or discussions.
"""

    async def _generate_changelog(self) -> str:
        """Generate changelog"""
        return """# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial project structure
- Basic functionality
- Documentation

### Changed
- N/A

### Deprecated
- N/A

### Removed
- N/A

### Fixed
- N/A

### Security
- N/A

## [1.0.0] - 2024-01-01

### Added
- Initial release
- Core features implemented
- Basic documentation
- Test suite
- CI/CD pipeline

[Unreleased]: https://github.com/username/project/compare/v1.0.0...HEAD
[1.0.0]: https://github.com/username/project/releases/tag/v1.0.0
"""

    # Source code generation methods
    async def _generate_react_app_component(self) -> str:
        """Generate React App component"""
        return """import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './Header';
import Home from '../pages/Home';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
"""

    async def _generate_react_header_component(self) -> str:
        """Generate React Header component"""
        return """import React from 'react';
import { Link } from 'react-router-dom';

const Header: React.FC = () => {
  return (
    <header className="header">
      <nav className="nav">
        <Link to="/" className="nav-brand">
          My App
        </Link>
        <ul className="nav-links">
          <li>
            <Link to="/" className="nav-link">
              Home
            </Link>
          </li>
        </ul>
      </nav>
    </header>
  );
};

export default Header;
"""

    async def _generate_react_home_page(self) -> str:
        """Generate React Home page"""
        return """import React, { useState, useEffect } from 'react';

const Home: React.FC = () => {
  const [message, setMessage] = useState<string>('');

  useEffect(() => {
    setMessage('Welcome to your new application!');
  }, []);

  return (
    <div className="home">
      <div className="hero">
        <h1>Welcome to Your App</h1>
        <p>{message}</p>
        <button
          className="cta-button"
          onClick={() => setMessage('Hello from React!')}
        >
          Get Started
        </button>
      </div>

      <section className="features">
        <h2>Features</h2>
        <div className="feature-grid">
          <div className="feature-card">
            <h3>Modern Stack</h3>
            <p>Built with React, TypeScript, and modern tooling</p>
          </div>
          <div className="feature-card">
            <h3>Responsive Design</h3>
            <p>Works great on desktop, tablet, and mobile</p>
          </div>
          <div className="feature-card">
            <h3>Developer Friendly</h3>
            <p>Hot reload, TypeScript, and excellent DX</p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
"""

    async def _generate_global_styles(self) -> str:
        """Generate global CSS styles"""
        return """/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.header {
  background-color: #282c34;
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #61dafb;
}

/* Home Page Styles */
.home {
  flex: 1;
  padding: 2rem;
}

.hero {
  text-align: center;
  padding: 4rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  margin-bottom: 4rem;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.cta-button {
  background-color: #61dafb;
  color: #282c34;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cta-button:hover {
  background-color: #21a9c7;
}

/* Features Section */
.features {
  max-width: 1200px;
  margin: 0 auto;
}

.features h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
}

.feature-card h3 {
  margin-bottom: 1rem;
  color: #282c34;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav {
    flex-direction: column;
    gap: 1rem;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }
}
"""

    async def _generate_api_utils(self) -> str:
        """Generate API utility functions"""
        return """// API Utility Functions
const API_BASE_URL = process.env.VITE_API_URL || 'http://localhost:8000/api';

interface ApiResponse<T> {
  data: T;
  message?: string;
  error?: string;
}

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const config = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      throw new ApiError(response.status, `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(0, 'Network error occurred');
  }
}

export const api = {
  get: <T>(endpoint: string) => apiRequest<T>(endpoint),

  post: <T>(endpoint: string, data: any) =>
    apiRequest<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  put: <T>(endpoint: string, data: any) =>
    apiRequest<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  delete: <T>(endpoint: string) =>
    apiRequest<T>(endpoint, {
      method: 'DELETE',
    }),
};

export { ApiError };
export type { ApiResponse };
"""

    async def _generate_express_app(self) -> str:
        """Generate Express.js app file"""
        return """import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { errorHandler } from './middleware/errorHandler';
import { logger } from './utils/logger';
import userRoutes from './routes/userRoutes';

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(compression());

// Logging middleware
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API routes
app.use('/api/users', userRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Error handling middleware
app.use(errorHandler);

export default app;
"""

    async def _generate_express_server(self) -> str:
        """Generate Express.js server file"""
        return """import app from './app';
import { logger } from './utils/logger';

const PORT = process.env.PORT || 8000;

const server = app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default server;
"""

    async def _generate_user_controller(self) -> str:
        """Generate user controller"""
        return """import { Request, Response, NextFunction } from 'express';
import { UserService } from '../services/userService';
import { logger } from '../utils/logger';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  getAllUsers = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const users = await this.userService.getAllUsers();
      res.status(200).json({
        success: true,
        data: users,
        count: users.length
      });
    } catch (error) {
      logger.error('Error getting all users:', error);
      next(error);
    }
  };

  getUserById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const user = await this.userService.getUserById(id);

      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      res.status(200).json({
        success: true,
        data: user
      });
    } catch (error) {
      logger.error('Error getting user by ID:', error);
      next(error);
    }
  };

  createUser = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userData = req.body;
      const user = await this.userService.createUser(userData);

      res.status(201).json({
        success: true,
        data: user,
        message: 'User created successfully'
      });
    } catch (error) {
      logger.error('Error creating user:', error);
      next(error);
    }
  };

  updateUser = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const userData = req.body;
      const user = await this.userService.updateUser(id, userData);

      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      res.status(200).json({
        success: true,
        data: user,
        message: 'User updated successfully'
      });
    } catch (error) {
      logger.error('Error updating user:', error);
      next(error);
    }
  };

  deleteUser = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const deleted = await this.userService.deleteUser(id);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      res.status(200).json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting user:', error);
      next(error);
    }
  };
}
"""

    async def _generate_user_routes(self) -> str:
        """Generate user routes"""
        return """import { Router } from 'express';
import { UserController } from '../controllers/userController';
import { authMiddleware } from '../middleware/auth';
import { validateUser } from '../middleware/validation';

const router = Router();
const userController = new UserController();

// Apply authentication middleware to all routes
router.use(authMiddleware);

// GET /api/users - Get all users
router.get('/', userController.getAllUsers);

// GET /api/users/:id - Get user by ID
router.get('/:id', userController.getUserById);

// POST /api/users - Create new user
router.post('/', validateUser, userController.createUser);

// PUT /api/users/:id - Update user
router.put('/:id', validateUser, userController.updateUser);

// DELETE /api/users/:id - Delete user
router.delete('/:id', userController.deleteUser);

export default router;
"""

    async def _generate_auth_middleware(self) -> str:
        """Generate authentication middleware"""
        return """import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

interface AuthRequest extends Request {
  user?: any;
}

export const authMiddleware = (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({
        success: false,
        error: 'No authorization header provided'
      });
    }

    const token = authHeader.split(' ')[1]; // Bearer <token>

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'No token provided'
      });
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      logger.error('JWT_SECRET not configured');
      return res.status(500).json({
        success: false,
        error: 'Server configuration error'
      });
    }

    const decoded = jwt.verify(token, jwtSecret);
    req.user = decoded;

    next();
  } catch (error) {
    logger.error('Auth middleware error:', error);

    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }

    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({
        success: false,
        error: 'Token expired'
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
};
"""

    async def _generate_data_pipeline(self) -> str:
        """Generate data pipeline for Python projects"""
        return """\"\"\"
Data Pipeline Module
Handles data ingestion, processing, and output
\"\"\"

import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)

class DataPipeline:
    \"\"\"Main data pipeline class\"\"\"

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.processors = []
        self.connectors = {}

    async def run(self, data_source: str, output_destination: str) -> Dict[str, Any]:
        \"\"\"Run the complete data pipeline\"\"\"

        try:
            logger.info(f"Starting pipeline: {data_source} -> {output_destination}")

            # Step 1: Data Ingestion
            raw_data = await self.ingest_data(data_source)
            logger.info(f"Ingested {len(raw_data)} records")

            # Step 2: Data Processing
            processed_data = await self.process_data(raw_data)
            logger.info(f"Processed {len(processed_data)} records")

            # Step 3: Data Validation
            validated_data = await self.validate_data(processed_data)
            logger.info(f"Validated {len(validated_data)} records")

            # Step 4: Data Output
            result = await self.output_data(validated_data, output_destination)

            return {
                "success": True,
                "records_processed": len(validated_data),
                "output_location": output_destination,
                "execution_time": datetime.now().isoformat(),
                "result": result
            }

        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": datetime.now().isoformat()
            }

    async def ingest_data(self, source: str) -> pd.DataFrame:
        \"\"\"Ingest data from various sources\"\"\"

        if source.endswith('.csv'):
            return pd.read_csv(source)
        elif source.endswith('.json'):
            return pd.read_json(source)
        elif source.endswith('.parquet'):
            return pd.read_parquet(source)
        else:
            # Database or API source
            connector = self.connectors.get(source)
            if connector:
                return await connector.fetch_data()
            else:
                raise ValueError(f"Unsupported data source: {source}")

    async def process_data(self, data: pd.DataFrame) -> pd.DataFrame:
        \"\"\"Process data through configured processors\"\"\"

        processed_data = data.copy()

        for processor in self.processors:
            processed_data = await processor.process(processed_data)

        return processed_data

    async def validate_data(self, data: pd.DataFrame) -> pd.DataFrame:
        \"\"\"Validate data quality and integrity\"\"\"

        # Remove duplicates
        data = data.drop_duplicates()

        # Remove null values in critical columns
        critical_columns = self.config.get('critical_columns', [])
        for col in critical_columns:
            if col in data.columns:
                data = data.dropna(subset=[col])

        # Data type validation
        expected_types = self.config.get('expected_types', {})
        for col, dtype in expected_types.items():
            if col in data.columns:
                try:
                    data[col] = data[col].astype(dtype)
                except Exception as e:
                    logger.warning(f"Failed to convert {col} to {dtype}: {e}")

        return data

    async def output_data(self, data: pd.DataFrame, destination: str) -> Dict[str, Any]:
        \"\"\"Output processed data to destination\"\"\"

        if destination.endswith('.csv'):
            data.to_csv(destination, index=False)
            return {"format": "csv", "path": destination}
        elif destination.endswith('.json'):
            data.to_json(destination, orient='records')
            return {"format": "json", "path": destination}
        elif destination.endswith('.parquet'):
            data.to_parquet(destination, index=False)
            return {"format": "parquet", "path": destination}
        else:
            # Database or API destination
            connector = self.connectors.get(destination)
            if connector:
                return await connector.save_data(data)
            else:
                raise ValueError(f"Unsupported destination: {destination}")

# Example usage
async def main():
    config = {
        "critical_columns": ["id", "timestamp"],
        "expected_types": {
            "id": "int64",
            "value": "float64"
        }
    }

    pipeline = DataPipeline(config)
    result = await pipeline.run("data/input.csv", "data/output.parquet")
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
"""

    async def _generate_data_processor(self) -> str:
        """Generate data processor for Python projects"""
        return """\"\"\"
Data Processor Module
Contains various data processing and transformation functions
\"\"\"

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
import logging

logger = logging.getLogger(__name__)

class BaseProcessor(ABC):
    \"\"\"Base class for all data processors\"\"\"

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}

    @abstractmethod
    async def process(self, data: pd.DataFrame) -> pd.DataFrame:
        \"\"\"Process the input data

        Args:
            data: Input DataFrame to process

        Returns:
            Processed DataFrame

        Raises:
            NotImplementedError: Must be implemented by subclasses
            ValueError: If data is invalid or cannot be processed
        \"\"\"
        raise NotImplementedError(f"{self.__class__.__name__} must implement the process method")

class CleaningProcessor(BaseProcessor):
    \"\"\"Data cleaning processor\"\"\"

    async def process(self, data: pd.DataFrame) -> pd.DataFrame:
        \"\"\"Clean the data\"\"\"

        logger.info("Starting data cleaning")

        # Remove duplicates
        initial_count = len(data)
        data = data.drop_duplicates()
        logger.info(f"Removed {initial_count - len(data)} duplicate rows")

        # Handle missing values
        missing_strategy = self.config.get('missing_strategy', 'drop')

        if missing_strategy == 'drop':
            data = data.dropna()
        elif missing_strategy == 'fill_mean':
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            data[numeric_columns] = data[numeric_columns].fillna(data[numeric_columns].mean())
        elif missing_strategy == 'fill_median':
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            data[numeric_columns] = data[numeric_columns].fillna(data[numeric_columns].median())
        elif missing_strategy == 'fill_mode':
            for col in data.columns:
                data[col] = data[col].fillna(data[col].mode().iloc[0] if not data[col].mode().empty else 0)

        # Remove outliers using IQR method
        if self.config.get('remove_outliers', False):
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                Q1 = data[col].quantile(0.25)
                Q3 = data[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                data = data[(data[col] >= lower_bound) & (data[col] <= upper_bound)]

        logger.info(f"Data cleaning completed. Final shape: {data.shape}")
        return data

class TransformationProcessor(BaseProcessor):
    \"\"\"Data transformation processor\"\"\"

    async def process(self, data: pd.DataFrame) -> pd.DataFrame:
        \"\"\"Transform the data\"\"\"

        logger.info("Starting data transformation")

        # Apply transformations based on config
        transformations = self.config.get('transformations', [])

        for transform in transformations:
            transform_type = transform.get('type')
            column = transform.get('column')

            if transform_type == 'log':
                data[f'{column}_log'] = np.log1p(data[column])
            elif transform_type == 'sqrt':
                data[f'{column}_sqrt'] = np.sqrt(data[column])
            elif transform_type == 'normalize':
                data[f'{column}_normalized'] = (data[column] - data[column].min()) / (data[column].max() - data[column].min())
            elif transform_type == 'standardize':
                data[f'{column}_standardized'] = (data[column] - data[column].mean()) / data[column].std()
            elif transform_type == 'categorical_encode':
                data[f'{column}_encoded'] = pd.Categorical(data[column]).codes

        # Feature engineering
        if self.config.get('create_features', False):
            # Example: Create date features if datetime column exists
            datetime_columns = data.select_dtypes(include=['datetime64']).columns
            for col in datetime_columns:
                data[f'{col}_year'] = data[col].dt.year
                data[f'{col}_month'] = data[col].dt.month
                data[f'{col}_day'] = data[col].dt.day
                data[f'{col}_weekday'] = data[col].dt.weekday

        logger.info(f"Data transformation completed. Final shape: {data.shape}")
        return data

class AggregationProcessor(BaseProcessor):
    \"\"\"Data aggregation processor\"\"\"

    async def process(self, data: pd.DataFrame) -> pd.DataFrame:
        \"\"\"Aggregate the data\"\"\"

        logger.info("Starting data aggregation")

        group_by = self.config.get('group_by', [])
        aggregations = self.config.get('aggregations', {})

        if group_by and aggregations:
            data = data.groupby(group_by).agg(aggregations).reset_index()

            # Flatten column names if multi-level
            if isinstance(data.columns, pd.MultiIndex):
                data.columns = ['_'.join(col).strip() for col in data.columns.values]

        logger.info(f"Data aggregation completed. Final shape: {data.shape}")
        return data

# Processor factory
def create_processor(processor_type: str, config: Dict[str, Any] = None) -> BaseProcessor:
    \"\"\"Create a processor instance\"\"\"

    processors = {
        'cleaning': CleaningProcessor,
        'transformation': TransformationProcessor,
        'aggregation': AggregationProcessor
    }

    processor_class = processors.get(processor_type)
    if not processor_class:
        raise ValueError(f"Unknown processor type: {processor_type}")

    return processor_class(config)
"""

    async def _generate_data_model(self) -> str:
        """Generate data model for Python projects"""
        return """\"\"\"
Data Models Module
Defines data structures and schemas for the data platform
\"\"\"

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum
import pandas as pd
from pydantic import BaseModel, validator

class DataQuality(Enum):
    \"\"\"Data quality levels\"\"\"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    UNKNOWN = "unknown"

class ProcessingStatus(Enum):
    \"\"\"Processing status\"\"\"
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class DataSource:
    \"\"\"Data source configuration\"\"\"
    name: str
    type: str  # csv, json, database, api
    location: str
    schema: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

@dataclass
class DataSet:
    \"\"\"Dataset representation\"\"\"
    id: str
    name: str
    description: str
    source: DataSource
    data: Optional[pd.DataFrame] = None
    quality: DataQuality = DataQuality.UNKNOWN
    status: ProcessingStatus = ProcessingStatus.PENDING
    row_count: int = 0
    column_count: int = 0
    size_bytes: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def update_stats(self):
        \"\"\"Update dataset statistics\"\"\"
        if self.data is not None:
            self.row_count = len(self.data)
            self.column_count = len(self.data.columns)
            self.size_bytes = self.data.memory_usage(deep=True).sum()
            self.updated_at = datetime.now()

class PipelineConfig(BaseModel):
    \"\"\"Pipeline configuration model\"\"\"

    name: str
    description: str
    source_config: Dict[str, Any]
    processing_steps: List[Dict[str, Any]]
    output_config: Dict[str, Any]
    schedule: Optional[str] = None
    retry_count: int = 3
    timeout_seconds: int = 3600

    @validator('processing_steps')
    def validate_processing_steps(cls, v):
        \"\"\"Validate processing steps\"\"\"
        required_fields = ['type', 'config']
        for step in v:
            for field in required_fields:
                if field not in step:
                    raise ValueError(f"Processing step missing required field: {field}")
        return v

class DataQualityReport(BaseModel):
    \"\"\"Data quality assessment report\"\"\"

    dataset_id: str
    overall_quality: DataQuality
    completeness_score: float  # 0-1
    accuracy_score: float      # 0-1
    consistency_score: float   # 0-1
    timeliness_score: float    # 0-1
    issues: List[str] = []
    recommendations: List[str] = []
    generated_at: datetime = field(default_factory=datetime.now)

    @validator('completeness_score', 'accuracy_score', 'consistency_score', 'timeliness_score')
    def validate_scores(cls, v):
        \"\"\"Validate scores are between 0 and 1\"\"\"
        if not 0 <= v <= 1:
            raise ValueError("Scores must be between 0 and 1")
        return v

@dataclass
class ProcessingResult:
    \"\"\"Result of data processing operation\"\"\"
    success: bool
    dataset_id: str
    records_processed: int
    processing_time_seconds: float
    output_location: str
    quality_report: Optional[DataQualityReport] = None
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

class MetricDefinition(BaseModel):
    \"\"\"Definition of a data metric\"\"\"

    name: str
    description: str
    metric_type: str  # count, sum, avg, min, max, custom
    column: Optional[str] = None
    aggregation_level: str  # row, column, dataset, global
    calculation_method: str
    thresholds: Dict[str, float] = {}

class DataLineage(BaseModel):
    \"\"\"Data lineage tracking\"\"\"

    dataset_id: str
    source_datasets: List[str] = []
    transformations: List[Dict[str, Any]] = []
    output_datasets: List[str] = []
    processing_pipeline: str
    created_at: datetime = field(default_factory=datetime.now)

# Example usage and factory functions
def create_dataset(name: str, description: str, source: DataSource, data: pd.DataFrame = None) -> DataSet:
    \"\"\"Create a new dataset\"\"\"
    dataset = DataSet(
        id=f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        name=name,
        description=description,
        source=source,
        data=data
    )

    if data is not None:
        dataset.update_stats()

    return dataset

def assess_data_quality(dataset: DataSet) -> DataQualityReport:
    \"\"\"Assess data quality for a dataset\"\"\"
    if dataset.data is None:
        return DataQualityReport(
            dataset_id=dataset.id,
            overall_quality=DataQuality.UNKNOWN,
            completeness_score=0.0,
            accuracy_score=0.0,
            consistency_score=0.0,
            timeliness_score=0.0,
            issues=["No data available for quality assessment"]
        )

    data = dataset.data

    # Calculate completeness (non-null ratio)
    completeness = 1 - (data.isnull().sum().sum() / (len(data) * len(data.columns)))

    # Accuracy check - detect obvious data errors
    accuracy_issues = 0
    total_values = len(data) * len(data.columns)

    for column in data.columns:
        if data[column].dtype in ['int64', 'float64']:
            # Check for negative values where they shouldn't be (e.g., age, price)
            if 'age' in column.lower() or 'price' in column.lower() or 'count' in column.lower():
                accuracy_issues += (data[column] < 0).sum()
            # Check for unrealistic outliers (values beyond 3 standard deviations)
            if len(data[column].dropna()) > 0:
                std_dev = data[column].std()
                mean_val = data[column].mean()
                outliers = ((data[column] - mean_val).abs() > 3 * std_dev).sum()
                accuracy_issues += outliers

    accuracy = max(0, 1 - (accuracy_issues / total_values)) if total_values > 0 else 1.0

    # Consistency check - data types and format consistency
    consistency_score = 1.0
    for column in data.columns:
        if data[column].dtype == 'object':
            # Check string format consistency (e.g., email, phone, dates)
            if 'email' in column.lower():
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                valid_emails = data[column].dropna().str.match(email_pattern).sum()
                total_emails = len(data[column].dropna())
                if total_emails > 0:
                    consistency_score *= (valid_emails / total_emails)
            elif 'phone' in column.lower():
                # Basic phone number pattern check
                phone_pattern = r'^\+?[\d\s\-\(\)]{10,}$'
                valid_phones = data[column].dropna().str.match(phone_pattern).sum()
                total_phones = len(data[column].dropna())
                if total_phones > 0:
                    consistency_score *= (valid_phones / total_phones)

    consistency = max(0, consistency_score)

    # Timeliness - check if data has timestamp columns and how recent they are
    timeliness = 1.0
    timestamp_columns = [col for col in data.columns if 'date' in col.lower() or 'time' in col.lower() or 'created' in col.lower()]

    if timestamp_columns:
        try:
            from datetime import datetime, timedelta
            current_time = datetime.now()

            for col in timestamp_columns:
                # Try to parse timestamps
                try:
                    timestamps = pd.to_datetime(data[col], errors='coerce').dropna()
                    if len(timestamps) > 0:
                        latest_timestamp = timestamps.max()
                        days_old = (current_time - latest_timestamp).days
                        # Penalize data older than 30 days
                        if days_old > 30:
                            timeliness *= max(0, 1 - (days_old - 30) / 365)
                except:
                    continue
        except ImportError:
            timeliness = 0.8  # Default if datetime not available

    # Overall quality
    overall_score = (completeness + accuracy + consistency + timeliness) / 4

    if overall_score >= 0.8:
        overall_quality = DataQuality.HIGH
    elif overall_score >= 0.6:
        overall_quality = DataQuality.MEDIUM
    else:
        overall_quality = DataQuality.LOW

    return DataQualityReport(
        dataset_id=dataset.id,
        overall_quality=overall_quality,
        completeness_score=completeness,
        accuracy_score=accuracy,
        consistency_score=consistency,
        timeliness_score=timeliness
    )
"""

    async def _execute_quality_assurance(self, prompt: str, project_path: str,
                                        project_id: str, project_type: str,
                                        development_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute quality assurance phase"""

        await self._drop_pheromone("phase_started", {"phase": "quality_assurance"}, project_id)

        try:
            # Create QA executor
            qa = create_agent_executor("qa")

            # Execute QA
            context = {
                "prompt": prompt,
                "project_path": project_path,
                "project_id": project_id,
                "project_type": project_type,
                "phase": "quality_assurance",
                "development_result": development_result
            }

            result = await qa.execute(context)

            # Update project metadata
            await self._update_project_metadata(project_path, "qa", "completed")

            await self._drop_pheromone("phase_completed", {
                "phase": "quality_assurance",
                "success": result.get("success", False),
                "outputs": result.get("outputs", [])
            }, project_id)

            return result

        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "quality_assurance",
                "error": str(e)
            }, project_id)
            raise e

    async def _finalize_project(self, project_path: str, project_id: str,
                              phase_results: Dict[str, Any]) -> Dict[str, Any]:
        """Finalize the project with summary and final documentation"""

        await self._drop_pheromone("phase_started", {"phase": "finalization"}, project_id)

        try:
            project_dir = Path(project_path)

            # Count total files created
            total_files = len(list(project_dir.rglob("*")))

            # Create final project summary
            summary = {
                "project_id": project_id,
                "completion_time": datetime.now().isoformat(),
                "total_files": total_files,
                "phases_completed": len(phase_results),
                "phase_results": {
                    phase: {
                        "success": result.get("success", False),
                        "outputs": len(result.get("outputs", [])),
                        "summary": result.get("summary", "")
                    }
                    for phase, result in phase_results.items()
                }
            }

            # Save project summary
            summary_file = project_dir / "docs" / "project_summary.json"
            summary_file.write_text(json.dumps(summary, indent=2), encoding='utf-8')

            # Update project metadata to completed
            await self._update_project_metadata(project_path, "finalization", "completed")
            await self._update_project_status(project_path, "completed")

            await self._drop_pheromone("phase_completed", {
                "phase": "finalization",
                "total_files": total_files,
                "project_completed": True
            }, project_id)

            return {
                "success": True,
                "total_files": total_files,
                "summary": summary,
                "files_created": ["docs/project_summary.json"]
            }

        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "finalization",
                "error": str(e)
            }, project_id)
            raise e

    async def _update_project_metadata(self, project_path: str, phase: str, status: str):
        """Update project metadata with phase completion"""

        metadata_file = Path(project_path) / ".aetherforge.json"

        if metadata_file.exists():
            try:
                metadata = json.loads(metadata_file.read_text(encoding='utf-8'))
                metadata["phases"][phase] = status
                metadata["last_updated"] = datetime.now().isoformat()
                metadata_file.write_text(json.dumps(metadata, indent=2), encoding='utf-8')
            except Exception as e:
                logger.warning(f"Failed to update metadata: {e}")

    async def _update_project_status(self, project_path: str, status: str):
        """Update overall project status"""

        metadata_file = Path(project_path) / ".aetherforge.json"

        if metadata_file.exists():
            try:
                metadata = json.loads(metadata_file.read_text(encoding='utf-8'))
                metadata["status"] = status
                metadata["completed_at"] = datetime.now().isoformat()
                metadata_file.write_text(json.dumps(metadata, indent=2), encoding='utf-8')
            except Exception as e:
                logger.warning(f"Failed to update project status: {e}")

    async def _drop_pheromone(self, pheromone_type: str, data: Dict[str, Any], project_id: str):
        """Drop a pheromone using the enhanced bus"""
        try:
            await self.pheromone_bus.drop_pheromone(pheromone_type, data, project_id)
        except Exception as e:
            logger.warning(f"Failed to drop pheromone: {e}")


# Factory function for easy access
async def generate_project(prompt: str, project_name: str, project_type: str,
                          project_path: str, workflow: str = None) -> Dict[str, Any]:
    """Generate a complete project using the pipeline"""

    pipeline = ProjectGenerationPipeline()
    return await pipeline.generate_project(prompt, project_name, project_type, project_path, workflow)
