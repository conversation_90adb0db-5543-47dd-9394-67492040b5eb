version: '3.8'

services:
  # Aetherforge Orchestrator - Main coordination service
  orchestrator:
    build:
      context: .
      dockerfile: Dockerfile.orchestrator
    ports:
      - "8000:8000"
    environment:
      - ARCHON_URL=http://archon:8100
      - MCP_URL=http://mcp-crawl4ai:8051
      - PHEROMIND_URL=http://pheromind:8502
      - BMAD_URL=http://bmad:8503
      - PROJECTS_DIR=/app/projects
      - PHEROMONE_FILE=/app/pheromones.json
      - PYTHONUNBUFFERED=1
    volumes:
      - ./projects:/app/projects
      - ./pheromones.json:/app/pheromones.json
    depends_on:
      - redis
      - postgres
    networks:
      - aetherforge-network
    restart: unless-stopped

  # Archon - Agent team generation and coordination
  archon:
    build:
      context: ./components/Archon
      dockerfile: Dockerfile
    ports:
      - "8100:8100"  # API Service
      - "8501:8501"  # Streamlit UI
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ORCHESTRATOR_URL=http://orchestrator:8000
      - PYTHONUNBUFFERED=1
    volumes:
      - ./projects:/app/projects
      - ./components/Archon/data:/app/data
    networks:
      - aetherforge-network
    restart: unless-stopped

  # MCP-Crawl4AI-RAG - Web crawling and RAG capabilities
  mcp-crawl4ai:
    build:
      context: ./components/mcp-crawl4ai-rag
      dockerfile: Dockerfile
    ports:
      - "8051:8051"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CRAWL_DEPTH=3
      - MAX_PAGES=100
      - PYTHONUNBUFFERED=1
    volumes:
      - ./projects:/app/projects
      - ./components/mcp-crawl4ai-rag/data:/app/data
    networks:
      - aetherforge-network
    restart: unless-stopped

  # Pheromind - Pheromone-based coordination and visualization
  pheromind:
    build:
      context: ./components/Pheromind
      dockerfile: Dockerfile
    ports:
      - "8502:8502"  # Backend API
      - "3000:3000"  # Frontend (if available)
    environment:
      - PHEROMONE_BUS_URL=http://orchestrator:8000
      - NODE_ENV=production
      - PYTHONUNBUFFERED=1
    volumes:
      - ./projects:/app/projects
      - ./components/Pheromind/data:/app/data
    networks:
      - aetherforge-network
    restart: unless-stopped

  # BMAD-METHOD - Development methodology and workflow
  bmad:
    build:
      context: ./components/BMAD-METHOD
      dockerfile: Dockerfile
    ports:
      - "8503:8503"
    environment:
      - ORCHESTRATOR_URL=http://orchestrator:8000
      - WORKFLOW_DIR=/app/workflows
      - PYTHONUNBUFFERED=1
    volumes:
      - ./projects:/app/projects
      - ./components/BMAD-METHOD/workflows:/app/workflows
    networks:
      - aetherforge-network
    restart: unless-stopped

  # Redis - Caching and session storage
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - aetherforge-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL - Primary database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=aetherforge
      - POSTGRES_USER=aetherforge
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-aetherforge123}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - aetherforge-network
    restart: unless-stopped

volumes:
  postgres-data:
  redis-data:

networks:
  aetherforge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
