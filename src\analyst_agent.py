"""
Comprehensive Analyst Agent for Aetherforge
Processes natural language prompts, conducts research using MCP-RAG, 
and generates detailed project specifications with user stories.
"""

import asyncio
import aiohttp
import json
import logging
import os
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass

# Enhanced base agent integration
try:
    from base_agent_enhanced import BaseAgentEnhanced, AgentType, AgentConfig, AgentStatus
except ImportError:
    # Fallback for when enhanced base agent is not available
    BaseAgentEnhanced = None
    AgentType = None
    AgentConfig = None
    AgentStatus = None

logger = logging.getLogger(__name__)

@dataclass
class ResearchContext:
    """Context for research operations"""
    query: str
    sources: List[str] = None
    depth: str = "comprehensive"
    match_count: int = 10

@dataclass
class ProjectSpecification:
    """Complete project specification structure"""
    project_name: str
    description: str
    requirements: Dict[str, Any]
    user_stories: List[Dict[str, Any]]
    technical_stack: Dict[str, Any]
    architecture: Dict[str, Any]
    constraints: List[str]
    success_metrics: List[str]
    risks: List[str]

class MCPRAGClient:
    """Client for MCP-RAG research service"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or os.getenv("MCP_URL", "http://localhost:8051")
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60))
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_available_sources(self) -> List[str]:
        """Get available sources for research"""
        try:
            async with self.session.get(f"{self.base_url}/get_available_sources") as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("sources", [])
                return []
        except Exception as e:
            logger.warning(f"Failed to get available sources: {e}")
            return []
    
    async def perform_rag_query(self, query: str, source: str = None, match_count: int = 5) -> Dict[str, Any]:
        """Perform RAG query for research"""
        try:
            payload = {
                "query": query,
                "source": source,
                "match_count": match_count
            }
            
            async with self.session.post(f"{self.base_url}/perform_rag_query", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                return {"results": [], "error": f"HTTP {response.status}"}
        except Exception as e:
            logger.warning(f"RAG query failed: {e}")
            return {"results": [], "error": str(e)}
    
    async def search_code_examples(self, query: str, source_id: str = None, match_count: int = 5) -> Dict[str, Any]:
        """Search for code examples"""
        try:
            payload = {
                "query": query,
                "source_id": source_id,
                "match_count": match_count
            }
            
            async with self.session.post(f"{self.base_url}/search_code_examples", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                return {"results": [], "error": f"HTTP {response.status}"}
        except Exception as e:
            logger.warning(f"Code search failed: {e}")
            return {"results": [], "error": str(e)}
    
    async def crawl_and_research(self, urls: List[str]) -> Dict[str, Any]:
        """Crawl URLs and store for research"""
        try:
            results = []
            for url in urls:
                payload = {"url": url}
                async with self.session.post(f"{self.base_url}/smart_crawl_url", json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        results.append(result)
            return {"crawled_sources": results, "success": True}
        except Exception as e:
            logger.warning(f"Crawling failed: {e}")
            return {"crawled_sources": [], "success": False, "error": str(e)}

class AnalystAgent(BaseAgentEnhanced if BaseAgentEnhanced else object):
    """Comprehensive analyst agent for project analysis and specification generation"""

    def __init__(self, agent_id: str = None, mcp_url: str = None, config: AgentConfig = None):
        # Initialize enhanced base agent if available
        if BaseAgentEnhanced:
            agent_id = agent_id or f"analyst_{int(datetime.now().timestamp())}"
            config = config or AgentConfig(
                preferred_provider="openai",
                model="gpt-4",
                max_tokens=4000,
                temperature=0.7
            )
            super().__init__(agent_id, "Analyst Agent", AgentType.ANALYST, config)

        self.mcp_client = MCPRAGClient(mcp_url)

        # Use API manager from base class if available, otherwise create new one
        if not hasattr(self, 'api_manager') or not self.api_manager:
            try:
                from .api_manager import APIManager
            except ImportError:
                from api_manager import APIManager
            self.api_manager = APIManager()
        
        # Research templates for different domains
        self.research_templates = {
            "web_application": [
                "modern web development frameworks",
                "responsive design best practices",
                "web security standards",
                "performance optimization techniques"
            ],
            "mobile_application": [
                "mobile app development frameworks",
                "mobile UI/UX design patterns",
                "mobile performance optimization",
                "app store guidelines"
            ],
            "api_service": [
                "REST API design principles",
                "API security best practices",
                "microservices architecture",
                "API documentation standards"
            ],
            "data_platform": [
                "data pipeline architectures",
                "data processing frameworks",
                "data storage solutions",
                "data visualization tools"
            ]
        }
    
    async def execute_agent_logic(self, task: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced agent logic implementation for BaseAgentEnhanced integration
        """
        task_type = task.get("type", "analyze_prompt")

        if task_type == "analyze_prompt":
            prompt = task.get("prompt", "")
            project_type = task.get("project_type", "web_application")

            if not prompt:
                return {
                    "success": False,
                    "error": "No prompt provided for analysis",
                    "result": None
                }

            try:
                specification = await self.analyze_prompt(prompt, project_type)
                return {
                    "success": True,
                    "result": specification,
                    "task_type": task_type
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "result": None
                }
        else:
            return {
                "success": False,
                "error": f"Unknown task type: {task_type}",
                "result": None
            }

    async def analyze_prompt(self, prompt: str, project_type: str = "web_application") -> ProjectSpecification:
        """Main analysis method that processes prompt and generates comprehensive specification"""
        logger.info(f"Starting analysis for prompt: {prompt[:100]}...")

        async with self.mcp_client:
            # Step 1: Conduct research
            research_context = await self._conduct_research(prompt, project_type)

            # Step 2: Extract requirements
            requirements = await self._extract_requirements(prompt, research_context)

            # Step 3: Generate user stories
            user_stories = await self._generate_user_stories(prompt, requirements, research_context)

            # Step 4: Define technical architecture
            technical_stack = await self._define_technical_stack(prompt, project_type, research_context)

            # Step 5: Create architecture overview
            architecture = await self._create_architecture_overview(prompt, technical_stack, research_context)

            # Step 6: Identify constraints and risks
            constraints, risks = await self._identify_constraints_and_risks(prompt, research_context)

            # Step 7: Define success metrics
            success_metrics = await self._define_success_metrics(prompt, requirements)

            return ProjectSpecification(
                project_name=self._extract_project_name(prompt),
                description=prompt,
                requirements=requirements,
                user_stories=user_stories,
                technical_stack=technical_stack,
                architecture=architecture,
                constraints=constraints,
                success_metrics=success_metrics,
                risks=risks
            )
    
    async def _conduct_research(self, prompt: str, project_type: str) -> Dict[str, Any]:
        """Conduct comprehensive research using MCP-RAG"""
        research_queries = self.research_templates.get(project_type, self.research_templates["web_application"])
        
        # Add project-specific research queries
        project_queries = self._generate_project_specific_queries(prompt)
        all_queries = research_queries + project_queries
        
        research_results = {}
        available_sources = await self.mcp_client.get_available_sources()
        
        for query in all_queries:
            # Perform RAG query
            rag_result = await self.mcp_client.perform_rag_query(query, match_count=5)
            research_results[query] = rag_result
            
            # Search for code examples if available
            code_result = await self.mcp_client.search_code_examples(query, match_count=3)
            research_results[f"{query}_code"] = code_result
        
        return {
            "queries": all_queries,
            "results": research_results,
            "sources": available_sources,
            "timestamp": datetime.now().isoformat()
        }
    
    def _generate_project_specific_queries(self, prompt: str) -> List[str]:
        """Generate research queries specific to the project prompt"""
        # Extract key terms from prompt for targeted research
        keywords = self._extract_keywords(prompt)
        
        queries = []
        for keyword in keywords:
            queries.extend([
                f"{keyword} implementation best practices",
                f"{keyword} architecture patterns",
                f"{keyword} security considerations",
                f"{keyword} performance optimization"
            ])
        
        return queries[:8]  # Limit to avoid too many queries
    
    def _extract_keywords(self, prompt: str) -> List[str]:
        """Extract key technical terms from prompt"""
        # Simple keyword extraction - could be enhanced with NLP
        tech_keywords = [
            "authentication", "database", "API", "frontend", "backend",
            "mobile", "web", "dashboard", "analytics", "payment",
            "notification", "search", "chat", "real-time", "machine learning"
        ]
        
        found_keywords = []
        prompt_lower = prompt.lower()
        for keyword in tech_keywords:
            if keyword in prompt_lower:
                found_keywords.append(keyword)
        
        return found_keywords[:5]  # Limit to top 5 keywords
    
    def _extract_project_name(self, prompt: str) -> str:
        """Extract or generate project name from prompt"""
        # Simple extraction - look for quoted names or generate from key terms
        words = prompt.split()
        if len(words) > 0:
            # Take first few meaningful words
            meaningful_words = [w for w in words[:5] if len(w) > 3 and w.isalpha()]
            if meaningful_words:
                return " ".join(meaningful_words[:3]).title()
        
        return "Aetherforge Project"

    async def _extract_requirements(self, prompt: str, research_context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and categorize requirements from prompt and research"""
        # Analyze prompt for functional requirements
        functional_requirements = self._extract_functional_requirements(prompt)

        # Extract non-functional requirements from research
        non_functional_requirements = self._extract_non_functional_requirements(research_context)

        # Identify business requirements
        business_requirements = self._extract_business_requirements(prompt)

        return {
            "functional": functional_requirements,
            "non_functional": non_functional_requirements,
            "business": business_requirements,
            "research_insights": self._summarize_research_insights(research_context)
        }

    def _extract_functional_requirements(self, prompt: str) -> List[Dict[str, Any]]:
        """Extract functional requirements from prompt"""
        # Parse prompt for action words and features
        action_words = ["create", "build", "develop", "implement", "add", "include", "support"]
        feature_words = ["login", "register", "dashboard", "profile", "search", "payment", "notification"]

        requirements = []
        prompt_lower = prompt.lower()

        # Basic requirement extraction
        if any(word in prompt_lower for word in ["user", "account", "login"]):
            requirements.append({
                "id": "REQ-001",
                "title": "User Authentication",
                "description": "System shall provide user registration and login functionality",
                "priority": "High",
                "category": "Authentication"
            })

        if any(word in prompt_lower for word in ["dashboard", "admin", "manage"]):
            requirements.append({
                "id": "REQ-002",
                "title": "Dashboard Interface",
                "description": "System shall provide a dashboard for users to manage their data",
                "priority": "High",
                "category": "User Interface"
            })

        if any(word in prompt_lower for word in ["api", "service", "endpoint"]):
            requirements.append({
                "id": "REQ-003",
                "title": "API Services",
                "description": "System shall provide RESTful API endpoints for data access",
                "priority": "High",
                "category": "Backend"
            })

        if any(word in prompt_lower for word in ["database", "data", "store"]):
            requirements.append({
                "id": "REQ-004",
                "title": "Data Storage",
                "description": "System shall provide persistent data storage capabilities",
                "priority": "High",
                "category": "Data"
            })

        return requirements

    def _extract_non_functional_requirements(self, research_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract non-functional requirements based on research"""
        return [
            {
                "id": "NFR-001",
                "title": "Performance",
                "description": "System shall respond to user requests within 2 seconds",
                "category": "Performance",
                "metric": "Response time < 2s"
            },
            {
                "id": "NFR-002",
                "title": "Security",
                "description": "System shall implement industry-standard security practices",
                "category": "Security",
                "metric": "OWASP compliance"
            },
            {
                "id": "NFR-003",
                "title": "Scalability",
                "description": "System shall support concurrent users and data growth",
                "category": "Scalability",
                "metric": "Support 1000+ concurrent users"
            },
            {
                "id": "NFR-004",
                "title": "Usability",
                "description": "System shall provide intuitive user experience",
                "category": "Usability",
                "metric": "User task completion rate > 90%"
            }
        ]

    def _extract_business_requirements(self, prompt: str) -> List[Dict[str, Any]]:
        """Extract business requirements from prompt"""
        return [
            {
                "id": "BR-001",
                "title": "Market Readiness",
                "description": "Solution shall be ready for market deployment",
                "stakeholder": "Business",
                "success_criteria": "Deployable MVP within timeline"
            },
            {
                "id": "BR-002",
                "title": "Cost Effectiveness",
                "description": "Solution shall be cost-effective to develop and maintain",
                "stakeholder": "Business",
                "success_criteria": "Development cost within budget"
            },
            {
                "id": "BR-003",
                "title": "User Adoption",
                "description": "Solution shall drive user engagement and adoption",
                "stakeholder": "Product",
                "success_criteria": "User retention rate > 70%"
            }
        ]

    def _summarize_research_insights(self, research_context: Dict[str, Any]) -> List[str]:
        """Summarize key insights from research"""
        insights = []

        # Extract insights from research results
        for query, result in research_context.get("results", {}).items():
            if "results" in result and result["results"]:
                insights.append(f"Research on '{query}' found {len(result['results'])} relevant sources")

        if research_context.get("sources"):
            insights.append(f"Available knowledge sources: {len(research_context['sources'])} domains")

        return insights

    async def _generate_user_stories(self, prompt: str, requirements: Dict[str, Any],
                                   research_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate comprehensive user stories with acceptance criteria"""
        user_stories = []

        # Generate stories based on functional requirements
        for req in requirements.get("functional", []):
            story = self._create_user_story_from_requirement(req)
            user_stories.append(story)

        # Add additional stories based on prompt analysis
        additional_stories = self._generate_additional_stories(prompt)
        user_stories.extend(additional_stories)

        return user_stories

    def _create_user_story_from_requirement(self, requirement: Dict[str, Any]) -> Dict[str, Any]:
        """Create user story from functional requirement"""
        story_templates = {
            "Authentication": {
                "role": "user",
                "action": "register and login to the system",
                "benefit": "I can access personalized features and secure my data"
            },
            "User Interface": {
                "role": "user",
                "action": "access a dashboard to view and manage my information",
                "benefit": "I can efficiently monitor and control my activities"
            },
            "Backend": {
                "role": "developer",
                "action": "access system data through well-documented APIs",
                "benefit": "I can integrate with the system and build additional features"
            },
            "Data": {
                "role": "user",
                "action": "have my data stored securely and persistently",
                "benefit": "I don't lose my information and can access it anytime"
            }
        }

        template = story_templates.get(requirement.get("category", ""), {
            "role": "user",
            "action": "use the system functionality",
            "benefit": "I can accomplish my goals efficiently"
        })

        return {
            "id": f"US-{requirement.get('id', '001').split('-')[1]}",
            "title": requirement.get("title", "User Story"),
            "role": template["role"],
            "action": template["action"],
            "benefit": template["benefit"],
            "priority": requirement.get("priority", "Medium"),
            "acceptance_criteria": self._generate_acceptance_criteria(requirement),
            "story_points": self._estimate_story_points(requirement),
            "epic": self._determine_epic(requirement)
        }

    def _generate_acceptance_criteria(self, requirement: Dict[str, Any]) -> List[str]:
        """Generate acceptance criteria for requirement"""
        category = requirement.get("category", "")

        criteria_templates = {
            "Authentication": [
                "User can register with valid email and password",
                "User can login with correct credentials",
                "User receives appropriate error messages for invalid inputs",
                "User session is maintained securely",
                "User can logout successfully"
            ],
            "User Interface": [
                "Dashboard loads within 3 seconds",
                "All navigation elements are clearly visible",
                "Interface is responsive on mobile and desktop",
                "User can access all primary functions from dashboard",
                "Data is displayed in an organized, readable format"
            ],
            "Backend": [
                "API endpoints return correct HTTP status codes",
                "API responses include proper error handling",
                "API documentation is complete and accurate",
                "API supports proper authentication and authorization",
                "API performance meets specified response time requirements"
            ],
            "Data": [
                "Data is stored with proper validation",
                "Data integrity is maintained across operations",
                "Data can be retrieved accurately and completely",
                "Data backup and recovery procedures are in place",
                "Data access is properly secured and audited"
            ]
        }

        return criteria_templates.get(category, [
            "Functionality works as described",
            "Error handling is implemented",
            "Performance meets requirements",
            "Security measures are in place"
        ])

    def _estimate_story_points(self, requirement: Dict[str, Any]) -> int:
        """Estimate story points based on requirement complexity"""
        priority = requirement.get("priority", "Medium")
        category = requirement.get("category", "")

        # Base points by category complexity
        base_points = {
            "Authentication": 8,
            "User Interface": 5,
            "Backend": 13,
            "Data": 8
        }

        points = base_points.get(category, 5)

        # Adjust by priority
        if priority == "High":
            points += 2
        elif priority == "Low":
            points -= 2

        return max(1, min(points, 21))  # Fibonacci scale cap

    def _determine_epic(self, requirement: Dict[str, Any]) -> str:
        """Determine epic for requirement"""
        category = requirement.get("category", "")

        epic_mapping = {
            "Authentication": "User Management",
            "User Interface": "User Experience",
            "Backend": "System Infrastructure",
            "Data": "Data Management"
        }

        return epic_mapping.get(category, "Core Features")

    def _generate_additional_stories(self, prompt: str) -> List[Dict[str, Any]]:
        """Generate additional user stories based on prompt analysis"""
        additional_stories = []
        prompt_lower = prompt.lower()

        # Common additional stories based on project type
        if any(word in prompt_lower for word in ["admin", "management", "control"]):
            additional_stories.append({
                "id": "US-005",
                "title": "Admin Management",
                "role": "administrator",
                "action": "manage system settings and user accounts",
                "benefit": "I can maintain system integrity and user access",
                "priority": "Medium",
                "acceptance_criteria": [
                    "Admin can view all user accounts",
                    "Admin can modify user permissions",
                    "Admin can access system configuration",
                    "Admin actions are logged for audit"
                ],
                "story_points": 13,
                "epic": "Administration"
            })

        if any(word in prompt_lower for word in ["report", "analytics", "metrics"]):
            additional_stories.append({
                "id": "US-006",
                "title": "Reporting and Analytics",
                "role": "user",
                "action": "view reports and analytics about my usage",
                "benefit": "I can make informed decisions based on data insights",
                "priority": "Medium",
                "acceptance_criteria": [
                    "User can generate usage reports",
                    "Reports include relevant metrics and visualizations",
                    "Reports can be exported in multiple formats",
                    "Data is accurate and up-to-date"
                ],
                "story_points": 8,
                "epic": "Analytics"
            })

        return additional_stories

    async def _define_technical_stack(self, prompt: str, project_type: str,
                                    research_context: Dict[str, Any]) -> Dict[str, Any]:
        """Define technical stack based on requirements and research"""
        # Base stack recommendations by project type
        base_stacks = {
            "web_application": {
                "frontend": {
                    "framework": "React",
                    "language": "TypeScript",
                    "styling": "Tailwind CSS",
                    "build_tool": "Vite",
                    "testing": "Jest + React Testing Library"
                },
                "backend": {
                    "runtime": "Node.js",
                    "framework": "Express.js",
                    "language": "TypeScript",
                    "testing": "Jest + Supertest"
                },
                "database": {
                    "primary": "PostgreSQL",
                    "orm": "Prisma",
                    "caching": "Redis"
                },
                "deployment": {
                    "containerization": "Docker",
                    "orchestration": "Docker Compose",
                    "cloud": "AWS/Vercel"
                }
            },
            "mobile_application": {
                "frontend": {
                    "framework": "React Native",
                    "language": "TypeScript",
                    "navigation": "React Navigation",
                    "state_management": "Redux Toolkit",
                    "testing": "Jest + Detox"
                },
                "backend": {
                    "runtime": "Node.js",
                    "framework": "Express.js",
                    "language": "TypeScript",
                    "testing": "Jest + Supertest"
                },
                "database": {
                    "primary": "PostgreSQL",
                    "orm": "Prisma",
                    "caching": "Redis"
                },
                "deployment": {
                    "containerization": "Docker",
                    "orchestration": "Docker Compose",
                    "cloud": "AWS/Vercel"
                }
            },
            "api_service": {
                "backend": {
                    "runtime": "Node.js",
                    "framework": "Express.js",
                    "language": "TypeScript",
                    "documentation": "OpenAPI/Swagger",
                    "testing": "Jest + Supertest"
                },
                "database": {
                    "primary": "PostgreSQL",
                    "orm": "Prisma",
                    "caching": "Redis"
                },
                "deployment": {
                    "containerization": "Docker",
                    "orchestration": "Docker Compose",
                    "cloud": "AWS/Vercel"
                }
            }
        }

        # Get base stack and enhance with research insights
        stack = base_stacks.get(project_type, base_stacks["web_application"])

        # Add security considerations
        stack["security"] = {
            "authentication": "JWT",
            "authorization": "RBAC",
            "encryption": "bcrypt",
            "https": "TLS 1.3",
            "headers": "Helmet.js"
        }

        # Add monitoring and logging
        stack["monitoring"] = {
            "logging": "Winston",
            "metrics": "Prometheus",
            "error_tracking": "Sentry",
            "health_checks": "Custom endpoints"
        }

        return stack

    async def _create_architecture_overview(self, prompt: str, technical_stack: Dict[str, Any],
                                          research_context: Dict[str, Any]) -> Dict[str, Any]:
        """Create high-level architecture overview"""
        return {
            "pattern": "Layered Architecture",
            "components": {
                "presentation_layer": {
                    "description": "User interface and user experience components",
                    "technologies": technical_stack.get("frontend", {}),
                    "responsibilities": [
                        "User interaction handling",
                        "Data presentation",
                        "Client-side validation",
                        "Responsive design"
                    ]
                },
                "business_layer": {
                    "description": "Business logic and application services",
                    "technologies": technical_stack.get("backend", {}),
                    "responsibilities": [
                        "Business rule enforcement",
                        "Data processing",
                        "API endpoint management",
                        "Authentication and authorization"
                    ]
                },
                "data_layer": {
                    "description": "Data storage and retrieval services",
                    "technologies": technical_stack.get("database", {}),
                    "responsibilities": [
                        "Data persistence",
                        "Data integrity",
                        "Query optimization",
                        "Backup and recovery"
                    ]
                }
            },
            "integration_patterns": [
                "RESTful API communication",
                "Event-driven architecture for real-time features",
                "Database connection pooling",
                "Caching strategies for performance"
            ],
            "scalability_considerations": [
                "Horizontal scaling capabilities",
                "Load balancing strategies",
                "Database optimization",
                "CDN integration for static assets"
            ]
        }

    async def _identify_constraints_and_risks(self, prompt: str,
                                            research_context: Dict[str, Any]) -> tuple[List[str], List[str]]:
        """Identify project constraints and risks"""
        constraints = [
            "Development timeline must accommodate MVP delivery",
            "Budget constraints may limit third-party service usage",
            "Team size and skill set may impact technology choices",
            "Compliance requirements must be met for data handling",
            "Performance requirements must be maintained under load"
        ]

        risks = [
            {
                "id": "RISK-001",
                "title": "Technology Learning Curve",
                "description": "Team may need time to learn new technologies",
                "probability": "Medium",
                "impact": "Medium",
                "mitigation": "Provide training and documentation"
            },
            {
                "id": "RISK-002",
                "title": "Third-party Dependencies",
                "description": "External services may become unavailable or change",
                "probability": "Low",
                "impact": "High",
                "mitigation": "Implement fallback mechanisms and monitoring"
            },
            {
                "id": "RISK-003",
                "title": "Scalability Challenges",
                "description": "System may not handle expected user load",
                "probability": "Medium",
                "impact": "High",
                "mitigation": "Implement load testing and monitoring"
            },
            {
                "id": "RISK-004",
                "title": "Security Vulnerabilities",
                "description": "System may be vulnerable to security threats",
                "probability": "Medium",
                "impact": "High",
                "mitigation": "Implement security best practices and regular audits"
            }
        ]

        return constraints, risks

    async def _define_success_metrics(self, prompt: str, requirements: Dict[str, Any]) -> List[str]:
        """Define success metrics for the project"""
        return [
            "User adoption rate > 70% within first 3 months",
            "System uptime > 99.5%",
            "Page load times < 2 seconds",
            "User task completion rate > 90%",
            "Customer satisfaction score > 4.0/5.0",
            "Zero critical security vulnerabilities",
            "API response times < 500ms for 95% of requests",
            "Mobile app store rating > 4.0 stars"
        ]

    async def generate_project_documents(self, specification: ProjectSpecification,
                                       output_path: Path) -> List[str]:
        """Generate comprehensive project documentation"""
        output_path.mkdir(parents=True, exist_ok=True)
        docs_path = output_path / "docs"
        docs_path.mkdir(exist_ok=True)

        generated_files = []

        # Generate project brief
        brief_content = self._generate_project_brief(specification)
        brief_file = docs_path / "project_brief.md"
        brief_file.write_text(brief_content, encoding='utf-8')
        generated_files.append("docs/project_brief.md")

        # Generate requirements document
        requirements_content = self._generate_requirements_document(specification)
        req_file = docs_path / "requirements.md"
        req_file.write_text(requirements_content, encoding='utf-8')
        generated_files.append("docs/requirements.md")

        # Generate user stories document
        stories_content = self._generate_user_stories_document(specification)
        stories_file = docs_path / "user_stories.md"
        stories_file.write_text(stories_content, encoding='utf-8')
        generated_files.append("docs/user_stories.md")

        # Generate technical specification
        tech_content = self._generate_technical_specification(specification)
        tech_file = docs_path / "technical_specification.md"
        tech_file.write_text(tech_content, encoding='utf-8')
        generated_files.append("docs/technical_specification.md")

        # Generate architecture document
        arch_content = self._generate_architecture_document(specification)
        arch_file = docs_path / "architecture.md"
        arch_file.write_text(arch_content, encoding='utf-8')
        generated_files.append("docs/architecture.md")

        # Generate project README
        readme_content = self._generate_project_readme(specification)
        readme_file = output_path / "README.md"
        readme_file.write_text(readme_content, encoding='utf-8')
        generated_files.append("README.md")

        return generated_files

    def _generate_project_brief(self, spec: ProjectSpecification) -> str:
        """Generate project brief document"""
        return f"""# Project Brief: {spec.project_name}

Generated: {datetime.now().isoformat()}

## Executive Summary

{spec.description}

## Project Goals

{spec.project_name} aims to deliver a comprehensive solution that meets the following objectives:

{chr(10).join(f"- {metric}" for metric in spec.success_metrics[:5])}

## Core Requirements

### Functional Requirements
{chr(10).join(f"- **{req.get('title', 'Requirement')}**: {req.get('description', '')}"
             for req in spec.requirements.get('functional', []))}

### Non-Functional Requirements
{chr(10).join(f"- **{req.get('title', 'Requirement')}**: {req.get('description', '')}"
             for req in spec.requirements.get('non_functional', []))}

## Technical Stack

### Frontend
- Framework: {spec.technical_stack.get('frontend', {}).get('framework', 'React')}
- Language: {spec.technical_stack.get('frontend', {}).get('language', 'TypeScript')}
- Styling: {spec.technical_stack.get('frontend', {}).get('styling', 'Tailwind CSS')}

### Backend
- Runtime: {spec.technical_stack.get('backend', {}).get('runtime', 'Node.js')}
- Framework: {spec.technical_stack.get('backend', {}).get('framework', 'Express.js')}
- Language: {spec.technical_stack.get('backend', {}).get('language', 'TypeScript')}

### Database
- Primary: {spec.technical_stack.get('database', {}).get('primary', 'PostgreSQL')}
- ORM: {spec.technical_stack.get('database', {}).get('orm', 'Prisma')}

## Project Constraints

{chr(10).join(f"- {constraint}" for constraint in spec.constraints)}

## Success Metrics

{chr(10).join(f"- {metric}" for metric in spec.success_metrics)}

## Next Steps

1. Review and approve this project brief
2. Proceed with detailed requirements analysis
3. Begin system architecture design
4. Start development planning and sprint organization
"""

    def _generate_requirements_document(self, spec: ProjectSpecification) -> str:
        """Generate detailed requirements document"""
        return f"""# Requirements Document: {spec.project_name}

Generated: {datetime.now().isoformat()}

## 1. Introduction

### 1.1 Purpose
This document specifies the functional and non-functional requirements for {spec.project_name}.

### 1.2 Scope
{spec.description}

## 2. Functional Requirements

{chr(10).join(f"""### {req.get('id', 'REQ-XXX')}: {req.get('title', 'Requirement')}
**Priority**: {req.get('priority', 'Medium')}
**Category**: {req.get('category', 'General')}

{req.get('description', '')}
""" for req in spec.requirements.get('functional', []))}

## 3. Non-Functional Requirements

{chr(10).join(f"""### {req.get('id', 'NFR-XXX')}: {req.get('title', 'Requirement')}
**Category**: {req.get('category', 'General')}
**Metric**: {req.get('metric', 'TBD')}

{req.get('description', '')}
""" for req in spec.requirements.get('non_functional', []))}

## 4. Business Requirements

{chr(10).join(f"""### {req.get('id', 'BR-XXX')}: {req.get('title', 'Requirement')}
**Stakeholder**: {req.get('stakeholder', 'Business')}
**Success Criteria**: {req.get('success_criteria', 'TBD')}

{req.get('description', '')}
""" for req in spec.requirements.get('business', []))}

## 5. Constraints and Assumptions

### 5.1 Constraints
{chr(10).join(f"- {constraint}" for constraint in spec.constraints)}

### 5.2 Risks
{chr(10).join(f"""- **{risk.get('title', 'Risk')}** ({risk.get('probability', 'Unknown')} probability, {risk.get('impact', 'Unknown')} impact)
  - {risk.get('description', '')}
  - Mitigation: {risk.get('mitigation', 'TBD')}
""" for risk in spec.risks)}

## 6. Success Criteria

{chr(10).join(f"- {metric}" for metric in spec.success_metrics)}
"""

    def _generate_user_stories_document(self, spec: ProjectSpecification) -> str:
        """Generate user stories document"""
        return f"""# User Stories: {spec.project_name}

Generated: {datetime.now().isoformat()}

## Overview

This document contains all user stories for {spec.project_name}, organized by epic and priority.

## Epics Overview

{chr(10).join(f"- **{epic}**: Stories related to {epic.lower()} functionality"
             for epic in set(story.get('epic', 'Core Features') for story in spec.user_stories))}

## User Stories

{chr(10).join(f"""### {story.get('id', 'US-XXX')}: {story.get('title', 'User Story')}

**Epic**: {story.get('epic', 'Core Features')}
**Priority**: {story.get('priority', 'Medium')}
**Story Points**: {story.get('story_points', 'TBD')}

#### Story
As a **{story.get('role', 'user')}**
I want **{story.get('action', 'to perform an action')}**
So that **{story.get('benefit', 'I can achieve my goal')}**

#### Acceptance Criteria
{chr(10).join(f"- [ ] {criteria}" for criteria in story.get('acceptance_criteria', []))}

---
""" for story in spec.user_stories)}

## Story Summary

- **Total Stories**: {len(spec.user_stories)}
- **Total Story Points**: {sum(story.get('story_points', 0) for story in spec.user_stories)}
- **High Priority**: {len([s for s in spec.user_stories if s.get('priority') == 'High'])}
- **Medium Priority**: {len([s for s in spec.user_stories if s.get('priority') == 'Medium'])}
- **Low Priority**: {len([s for s in spec.user_stories if s.get('priority') == 'Low'])}
"""

    def _generate_technical_specification(self, spec: ProjectSpecification) -> str:
        """Generate technical specification document"""
        return f"""# Technical Specification: {spec.project_name}

Generated: {datetime.now().isoformat()}

## 1. Technology Stack

### 1.1 Frontend Technologies
{chr(10).join(f"- **{key.title()}**: {value}"
             for key, value in spec.technical_stack.get('frontend', {}).items())}

### 1.2 Backend Technologies
{chr(10).join(f"- **{key.title()}**: {value}"
             for key, value in spec.technical_stack.get('backend', {}).items())}

### 1.3 Database Technologies
{chr(10).join(f"- **{key.title()}**: {value}"
             for key, value in spec.technical_stack.get('database', {}).items())}

### 1.4 Security Technologies
{chr(10).join(f"- **{key.title()}**: {value}"
             for key, value in spec.technical_stack.get('security', {}).items())}

### 1.5 Monitoring and Logging
{chr(10).join(f"- **{key.title()}**: {value}"
             for key, value in spec.technical_stack.get('monitoring', {}).items())}

### 1.6 Deployment Technologies
{chr(10).join(f"- **{key.title()}**: {value}"
             for key, value in spec.technical_stack.get('deployment', {}).items())}

## 2. System Architecture

### 2.1 Architecture Pattern
{spec.architecture.get('pattern', 'Layered Architecture')}

### 2.2 System Components

{chr(10).join(f"""#### {component_name.replace('_', ' ').title()}
{component.get('description', '')}

**Technologies**: {', '.join(f"{k}: {v}" for k, v in component.get('technologies', {}).items())}

**Responsibilities**:
{chr(10).join(f"- {resp}" for resp in component.get('responsibilities', []))}
""" for component_name, component in spec.architecture.get('components', {}).items())}

### 2.3 Integration Patterns
{chr(10).join(f"- {pattern}" for pattern in spec.architecture.get('integration_patterns', []))}

### 2.4 Scalability Considerations
{chr(10).join(f"- {consideration}" for consideration in spec.architecture.get('scalability_considerations', []))}

## 3. Development Standards

### 3.1 Code Quality
- TypeScript strict mode enabled
- ESLint configuration for code consistency
- Prettier for code formatting
- Comprehensive unit test coverage (>80%)

### 3.2 Security Standards
- OWASP security guidelines compliance
- Regular security audits and vulnerability scanning
- Secure coding practices enforcement
- Data encryption at rest and in transit

### 3.3 Performance Standards
- Page load times < 2 seconds
- API response times < 500ms
- Database query optimization
- Caching strategies implementation

## 4. Deployment Architecture

### 4.1 Environment Strategy
- **Development**: Local development with hot reload
- **Staging**: Production-like environment for testing
- **Production**: Optimized for performance and reliability

### 4.2 CI/CD Pipeline
- Automated testing on pull requests
- Automated deployment to staging
- Manual approval for production deployment
- Rollback capabilities

### 4.3 Monitoring and Alerting
- Application performance monitoring
- Error tracking and alerting
- Infrastructure monitoring
- User analytics and behavior tracking
"""

    def _generate_architecture_document(self, spec: ProjectSpecification) -> str:
        """Generate architecture document"""
        return f"""# System Architecture: {spec.project_name}

Generated: {datetime.now().isoformat()}

## 1. Architecture Overview

### 1.1 Architecture Pattern
{spec.architecture.get('pattern', 'Layered Architecture')}

### 1.2 System Context
{spec.description}

## 2. Component Architecture

{chr(10).join(f"""### 2.{i+1} {component_name.replace('_', ' ').title()}

**Description**: {component.get('description', '')}

**Key Technologies**:
{chr(10).join(f"- {k.title()}: {v}" for k, v in component.get('technologies', {}).items())}

**Core Responsibilities**:
{chr(10).join(f"- {resp}" for resp in component.get('responsibilities', []))}
""" for i, (component_name, component) in enumerate(spec.architecture.get('components', {}).items()))}

## 3. Data Architecture

### 3.1 Data Storage Strategy
- **Primary Database**: {spec.technical_stack.get('database', {}).get('primary', 'PostgreSQL')}
- **ORM/Query Builder**: {spec.technical_stack.get('database', {}).get('orm', 'Prisma')}
- **Caching Layer**: {spec.technical_stack.get('database', {}).get('caching', 'Redis')}

### 3.2 Data Flow
1. User interactions captured by presentation layer
2. Business logic processes and validates data
3. Data layer persists information securely
4. Caching layer optimizes read performance

## 4. Security Architecture

### 4.1 Authentication & Authorization
- **Authentication Method**: {spec.technical_stack.get('security', {}).get('authentication', 'JWT')}
- **Authorization Model**: {spec.technical_stack.get('security', {}).get('authorization', 'RBAC')}
- **Password Security**: {spec.technical_stack.get('security', {}).get('encryption', 'bcrypt')}

### 4.2 Data Protection
- **Transport Security**: {spec.technical_stack.get('security', {}).get('https', 'TLS 1.3')}
- **Security Headers**: {spec.technical_stack.get('security', {}).get('headers', 'Helmet.js')}
- **Input Validation**: Comprehensive validation at all entry points

## 5. Integration Architecture

### 5.1 Integration Patterns
{chr(10).join(f"- {pattern}" for pattern in spec.architecture.get('integration_patterns', []))}

### 5.2 API Design
- RESTful API endpoints with OpenAPI documentation
- Consistent error handling and response formats
- Rate limiting and throttling mechanisms
- API versioning strategy

## 6. Deployment Architecture

### 6.1 Containerization
- **Container Platform**: {spec.technical_stack.get('deployment', {}).get('containerization', 'Docker')}
- **Orchestration**: {spec.technical_stack.get('deployment', {}).get('orchestration', 'Docker Compose')}

### 6.2 Cloud Infrastructure
- **Cloud Provider**: {spec.technical_stack.get('deployment', {}).get('cloud', 'AWS/Vercel')}
- **Scalability**: Auto-scaling based on demand
- **Load Balancing**: Distributed traffic management

## 7. Monitoring Architecture

### 7.1 Application Monitoring
- **Logging**: {spec.technical_stack.get('monitoring', {}).get('logging', 'Winston')}
- **Metrics**: {spec.technical_stack.get('monitoring', {}).get('metrics', 'Prometheus')}
- **Error Tracking**: {spec.technical_stack.get('monitoring', {}).get('error_tracking', 'Sentry')}

### 7.2 Health Monitoring
- **Health Checks**: {spec.technical_stack.get('monitoring', {}).get('health_checks', 'Custom endpoints')}
- **Uptime Monitoring**: Continuous availability tracking
- **Performance Monitoring**: Response time and throughput metrics
"""

    def _generate_project_readme(self, spec: ProjectSpecification) -> str:
        """Generate project README"""
        return f"""# {spec.project_name}

{spec.description}

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- {spec.technical_stack.get('database', {}).get('primary', 'PostgreSQL')}
- Docker (optional)

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd {spec.project_name.lower().replace(' ', '-')}
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Set up the database
```bash
npm run db:setup
npm run db:migrate
```

5. Start the development server
```bash
npm run dev
```

## 📋 Features

{chr(10).join(f"- {req.get('title', 'Feature')}: {req.get('description', '')}"
             for req in spec.requirements.get('functional', []))}

## 🏗️ Architecture

This project follows a {spec.architecture.get('pattern', 'layered architecture')} with the following components:

{chr(10).join(f"- **{component_name.replace('_', ' ').title()}**: {component.get('description', '')}"
             for component_name, component in spec.architecture.get('components', {}).items())}

## 🛠️ Technology Stack

### Frontend
{chr(10).join(f"- {key.title()}: {value}"
             for key, value in spec.technical_stack.get('frontend', {}).items())}

### Backend
{chr(10).join(f"- {key.title()}: {value}"
             for key, value in spec.technical_stack.get('backend', {}).items())}

### Database
{chr(10).join(f"- {key.title()}: {value}"
             for key, value in spec.technical_stack.get('database', {}).items())}

## 📚 Documentation

- [Project Brief](docs/project_brief.md)
- [Requirements](docs/requirements.md)
- [User Stories](docs/user_stories.md)
- [Technical Specification](docs/technical_specification.md)
- [Architecture](docs/architecture.md)

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run integration tests
npm run test:integration
```

## 🚀 Deployment

### Docker Deployment
```bash
docker-compose up --build
```

### Manual Deployment
```bash
npm run build
npm start
```

## 📊 Success Metrics

{chr(10).join(f"- {metric}" for metric in spec.success_metrics)}

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please refer to the documentation or create an issue.
"""

# Main execution function for standalone usage
async def main():
    """Main function for standalone execution"""
    import sys

    if len(sys.argv) < 2:
        print("Usage: python analyst_agent.py '<project_prompt>' [project_type] [output_path]")
        print("Example: python analyst_agent.py 'Create a task management web app' web_application ./output")
        return

    prompt = sys.argv[1]
    project_type = sys.argv[2] if len(sys.argv) > 2 else "web_application"
    output_path = Path(sys.argv[3]) if len(sys.argv) > 3 else Path("./analyst_output")

    # Initialize analyst agent
    analyst = AnalystAgent()

    try:
        print(f"🔍 Analyzing prompt: {prompt}")
        print(f"📋 Project type: {project_type}")
        print(f"📁 Output path: {output_path}")

        # Analyze prompt and generate specification
        specification = await analyst.analyze_prompt(prompt, project_type)

        print(f"✅ Analysis complete for: {specification.project_name}")
        print(f"📊 Generated {len(specification.user_stories)} user stories")
        print(f"📋 Identified {len(specification.requirements.get('functional', []))} functional requirements")

        # Generate documentation
        generated_files = await analyst.generate_project_documents(specification, output_path)

        print(f"📄 Generated {len(generated_files)} documentation files:")
        for file in generated_files:
            print(f"   - {file}")

        print(f"🎉 Analysis complete! Check {output_path} for all generated documents.")

    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        logger.error(f"Analysis failed: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
