{"name": "pheromind-swarmconfig-ui", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.12.0", "jsoneditor": "^9.10.0", "jsoneditor-react": "^3.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-tabs": "^6.0.1", "tailwindcss": "^3.3.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.23"}}