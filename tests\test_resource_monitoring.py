"""
Tests for resource monitoring functionality in workflow engine
"""

import pytest
import sys
from unittest.mock import Mo<PERSON>, patch, MagicMock
from src.workflow_engine import ResourceManager


class TestResourceMonitoring:
    """Test resource monitoring functionality"""

    def setup_method(self):
        """Set up test fixtures"""
        self.resource_manager = ResourceManager()

    def test_resource_manager_initialization(self):
        """Test ResourceManager initialization"""
        rm = ResourceManager()
        assert hasattr(rm, 'cpu_usage')
        assert hasattr(rm, 'memory_usage')
        assert 0.0 <= rm.cpu_usage <= 1.0
        assert 0.0 <= rm.memory_usage <= 1.0

    def test_update_availability_with_psutil(self):
        """Test update_availability with psutil available"""
        # Create a mock psutil module and inject it into sys.modules
        mock_psutil = Mock()
        mock_psutil.cpu_percent.return_value = 45.5
        mock_memory = Mock()
        mock_memory.percent = 60.2
        mock_psutil.virtual_memory.return_value = mock_memory

        mock_disk = Mock()
        mock_disk.used = 500
        mock_disk.total = 1000
        mock_psutil.disk_usage.return_value = mock_disk

        # Patch sys.modules to include our mock psutil
        with patch.dict('sys.modules', {'psutil': mock_psutil}):
            # Test update
            self.resource_manager.update_availability()

            # Verify values are set correctly
            assert abs(self.resource_manager.cpu_usage - 0.455) < 0.001
            assert abs(self.resource_manager.memory_usage - 0.602) < 0.001
            assert hasattr(self.resource_manager, 'disk_usage')
            assert abs(self.resource_manager.disk_usage - 0.5) < 0.001

    def test_update_availability_psutil_error(self):
        """Test update_availability when psutil raises errors"""
        # Mock psutil to raise errors
        mock_psutil = Mock()
        mock_psutil.cpu_percent.side_effect = OSError("CPU error")
        mock_psutil.virtual_memory.side_effect = OSError("Memory error")
        mock_psutil.disk_usage.side_effect = OSError("Disk error")

        # Patch sys.modules to include our mock psutil
        with patch.dict('sys.modules', {'psutil': mock_psutil}):
            # Should not crash and should keep reasonable values
            self.resource_manager.update_availability()

            # Values should still be valid
            assert 0.0 <= self.resource_manager.cpu_usage <= 1.0
            assert 0.0 <= self.resource_manager.memory_usage <= 1.0

    def test_update_availability_invalid_values(self):
        """Test update_availability with invalid psutil values"""
        # Mock psutil to return invalid values
        mock_psutil = Mock()
        mock_psutil.cpu_percent.return_value = -10  # Invalid negative
        mock_memory = Mock()
        mock_memory.percent = 150  # Invalid > 100
        mock_psutil.virtual_memory.return_value = mock_memory

        # Patch sys.modules to include our mock psutil
        with patch.dict('sys.modules', {'psutil': mock_psutil}):
            self.resource_manager.update_availability()

            # Should keep previous valid values when invalid data is received
            assert 0.0 <= self.resource_manager.cpu_usage <= 1.0
            assert 0.0 <= self.resource_manager.memory_usage <= 1.0

    def test_update_availability_without_psutil(self):
        """Test update_availability when psutil is not available"""
        # Remove psutil from sys.modules if it exists, and prevent import
        original_modules = sys.modules.copy()

        # Remove psutil from modules and create a mock that raises ImportError
        if 'psutil' in sys.modules:
            del sys.modules['psutil']

        def mock_import(name, *args, **kwargs):
            if name == 'psutil':
                raise ImportError("psutil not available")
            # Use original import for other modules
            return original_modules.get(name) or __import__(name, *args, **kwargs)

        with patch('builtins.__import__', side_effect=mock_import):
            self.resource_manager.update_availability()

            # Should use simulation and maintain valid values
            assert 0.0 <= self.resource_manager.cpu_usage <= 1.0
            assert 0.0 <= self.resource_manager.memory_usage <= 1.0

    def test_simulate_resource_monitoring(self):
        """Test simulated resource monitoring"""
        # Run simulation multiple times
        for _ in range(10):
            self.resource_manager._simulate_resource_monitoring()

            # Values should always be valid
            assert 0.0 <= self.resource_manager.cpu_usage <= 1.0
            assert 0.0 <= self.resource_manager.memory_usage <= 1.0

    def test_simulate_resource_monitoring_bounds(self):
        """Test that simulation respects bounds"""
        # Set extreme values
        self.resource_manager.cpu_usage = 0.0
        self.resource_manager.memory_usage = 1.0
        
        # Run simulation many times
        for _ in range(100):
            self.resource_manager._simulate_resource_monitoring()
            
            # Should never go out of bounds
            assert 0.0 <= self.resource_manager.cpu_usage <= 1.0
            assert 0.0 <= self.resource_manager.memory_usage <= 1.0

    def test_update_availability_initialization(self):
        """Test that update_availability initializes missing attributes"""
        # Create resource manager without attributes
        rm = object.__new__(ResourceManager)
        
        # Should not crash and should initialize attributes
        rm.update_availability()
        
        assert hasattr(rm, 'cpu_usage')
        assert hasattr(rm, 'memory_usage')
        assert 0.0 <= rm.cpu_usage <= 1.0
        assert 0.0 <= rm.memory_usage <= 1.0

    def test_update_availability_none_values(self):
        """Test handling of None values from psutil"""
        mock_psutil = Mock()
        mock_psutil.cpu_percent.return_value = None
        mock_memory = Mock()
        mock_memory.percent = None
        mock_psutil.virtual_memory.return_value = mock_memory

        # Patch sys.modules to include our mock psutil
        with patch.dict('sys.modules', {'psutil': mock_psutil}):
            initial_cpu = self.resource_manager.cpu_usage
            initial_memory = self.resource_manager.memory_usage

            self.resource_manager.update_availability()

            # Should keep previous values when None is returned
            assert self.resource_manager.cpu_usage == initial_cpu
            assert self.resource_manager.memory_usage == initial_memory

    def test_update_availability_exception_handling(self):
        """Test general exception handling in update_availability"""
        # Create a mock that raises an exception during import
        original_modules = sys.modules.copy()

        def mock_import(name, *args, **kwargs):
            if name == 'psutil':
                raise RuntimeError("Unexpected error")
            return original_modules.get(name) or __import__(name, *args, **kwargs)

        with patch('builtins.__import__', side_effect=mock_import):
            self.resource_manager.update_availability()

            # Should have fallback values
            assert 0.0 <= self.resource_manager.cpu_usage <= 1.0
            assert 0.0 <= self.resource_manager.memory_usage <= 1.0

    def test_resource_manager_attributes_persistence(self):
        """Test that resource manager maintains state between calls"""
        # Test without psutil to use simulation
        original_modules = sys.modules.copy()

        def mock_import(name, *args, **kwargs):
            if name == 'psutil':
                raise ImportError("psutil not available")
            return original_modules.get(name) or __import__(name, *args, **kwargs)

        with patch('builtins.__import__', side_effect=mock_import):
            # Simulate some updates
            self.resource_manager.update_availability()

            # Should maintain valid state
            assert hasattr(self.resource_manager, 'cpu_usage')
            assert hasattr(self.resource_manager, 'memory_usage')
            assert 0.0 <= self.resource_manager.cpu_usage <= 1.0
            assert 0.0 <= self.resource_manager.memory_usage <= 1.0
