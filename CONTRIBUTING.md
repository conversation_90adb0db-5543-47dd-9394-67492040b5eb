# Contributing to <PERSON>etherforge

Thank you for your interest in contributing to Aetherforge! This guide will help you get started with contributing to our autonomous AI software creation system.

## 🌟 Ways to Contribute

- **Bug Reports**: Help us identify and fix issues
- **Feature Requests**: Suggest new features and improvements
- **Code Contributions**: Submit bug fixes and new features
- **Documentation**: Improve our documentation and guides
- **Testing**: Help test new features and report issues
- **Community Support**: Help other users in discussions and forums

## 🚀 Getting Started

### Prerequisites

- Python 3.9 or higher
- Node.js 16 or higher
- Git
- Docker (for testing)
- VS Code (recommended)

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/aetherforge.git
   cd aetherforge
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Dependencies**
   ```bash
   # Install development dependencies
   pip install -r requirements-dev.txt
   
   # Install pre-commit hooks
   pre-commit install
   
   # Install Node.js dependencies (for VS Code extension)
   npm install
   ```

4. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

5. **Run Tests**
   ```bash
   # Run all tests
   pytest
   
   # Run with coverage
   pytest --cov=src
   
   # Run specific test file
   pytest tests/test_orchestrator.py
   ```

## 📋 Development Workflow

### 1. Create a Branch

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Create bugfix branch
git checkout -b bugfix/issue-number-description
```

### 2. Make Changes

- Follow our [coding standards](#coding-standards)
- Write tests for new functionality
- Update documentation as needed
- Ensure all tests pass

### 3. Commit Changes

We use [Conventional Commits](https://www.conventionalcommits.org/) for commit messages:

```bash
# Feature
git commit -m "feat: add new agent type for mobile development"

# Bug fix
git commit -m "fix: resolve memory leak in workflow engine"

# Documentation
git commit -m "docs: update API documentation with new endpoints"

# Tests
git commit -m "test: add integration tests for pheromone system"
```

### 4. Push and Create PR

```bash
git push origin your-branch-name
```

Then create a Pull Request on GitHub with:
- Clear description of changes
- Link to related issues
- Screenshots/demos if applicable
- Test results and coverage

## 🎯 Coding Standards

### Python Code Style

We follow [PEP 8](https://pep8.org/) with some modifications:

```python
# Use type hints
def create_project(name: str, project_type: ProjectType) -> Dict[str, Any]:
    """Create a new project with the given name and type.
    
    Args:
        name: The project name
        project_type: The type of project to create
        
    Returns:
        Dictionary containing project creation results
        
    Raises:
        ValueError: If name is invalid
        ProjectCreationError: If project creation fails
    """
    pass

# Use dataclasses for data structures
@dataclass
class ProjectConfig:
    name: str
    project_type: ProjectType
    features: List[str] = field(default_factory=list)
    
# Use async/await for I/O operations
async def fetch_data(url: str) -> Dict[str, Any]:
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.json()
```

### Documentation Standards

- All public functions must have docstrings
- Use Google-style docstrings
- Include type hints for all parameters and return values
- Add examples for complex functions

```python
def complex_function(param1: str, param2: Optional[int] = None) -> Tuple[bool, str]:
    """Performs a complex operation with the given parameters.
    
    This function demonstrates the expected documentation format
    for complex operations in Aetherforge.
    
    Args:
        param1: A string parameter that controls the operation
        param2: An optional integer parameter for fine-tuning
        
    Returns:
        A tuple containing:
        - bool: Success status of the operation
        - str: Result message or error description
        
    Raises:
        ValueError: If param1 is empty or invalid
        RuntimeError: If the operation fails due to system issues
        
    Example:
        >>> success, message = complex_function("test", 42)
        >>> print(f"Success: {success}, Message: {message}")
        Success: True, Message: Operation completed successfully
    """
    pass
```

### Testing Standards

- Write tests for all new functionality
- Aim for >90% test coverage
- Use descriptive test names
- Include both positive and negative test cases

```python
import pytest
from unittest.mock import Mock, patch

class TestProjectCreation:
    """Test suite for project creation functionality."""
    
    def test_create_project_success(self):
        """Test successful project creation with valid parameters."""
        # Arrange
        project_name = "TestProject"
        project_type = ProjectType.WEB_APPLICATION
        
        # Act
        result = create_project(project_name, project_type)
        
        # Assert
        assert result["success"] is True
        assert result["project_id"] is not None
        
    def test_create_project_invalid_name(self):
        """Test project creation fails with invalid name."""
        with pytest.raises(ValueError, match="Invalid project name"):
            create_project("", ProjectType.WEB_APPLICATION)
            
    @patch('src.orchestrator.external_api_call')
    def test_create_project_api_failure(self, mock_api):
        """Test project creation handles API failures gracefully."""
        # Arrange
        mock_api.side_effect = ConnectionError("API unavailable")
        
        # Act & Assert
        with pytest.raises(ProjectCreationError):
            create_project("TestProject", ProjectType.WEB_APPLICATION)
```

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Environment Information**
   ```bash
   aetherforge --version
   python --version
   pip list | grep aetherforge
   ```

2. **Steps to Reproduce**
   - Clear, numbered steps
   - Expected vs actual behavior
   - Screenshots if applicable

3. **Error Messages**
   - Full error messages and stack traces
   - Log files if relevant

4. **Minimal Reproduction**
   - Simplest code that reproduces the issue
   - Remove unnecessary complexity

## ✨ Feature Requests

For feature requests, please provide:

1. **Problem Description**
   - What problem does this solve?
   - Who would benefit from this feature?

2. **Proposed Solution**
   - Detailed description of the feature
   - How it would work
   - Alternative approaches considered

3. **Implementation Ideas**
   - Technical approach (if you have ideas)
   - Potential challenges
   - Breaking changes (if any)

## 🔍 Code Review Process

### For Contributors

- Ensure all tests pass
- Update documentation
- Follow coding standards
- Respond to review feedback promptly

### For Reviewers

- Be constructive and respectful
- Focus on code quality and maintainability
- Check for security issues
- Verify test coverage

### Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and pass
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance impact considered
- [ ] Backward compatibility maintained

## 🏷️ Release Process

1. **Version Bumping**
   - Follow [Semantic Versioning](https://semver.org/)
   - Update version in `setup.py` and `__init__.py`

2. **Changelog**
   - Update `CHANGELOG.md` with new features and fixes
   - Follow [Keep a Changelog](https://keepachangelog.com/) format

3. **Testing**
   - Run full test suite
   - Test on multiple Python versions
   - Verify documentation builds

4. **Release**
   - Create release tag
   - Build and publish packages
   - Update documentation

## 🤝 Community Guidelines

- Be respectful and inclusive
- Help others learn and grow
- Share knowledge and experiences
- Follow our [Code of Conduct](CODE_OF_CONDUCT.md)

## 📞 Getting Help

- **Discord**: Join our [community server](https://discord.gg/aetherforge)
- **GitHub Discussions**: Ask questions and share ideas
- **Documentation**: Check our [comprehensive docs](docs/README.md)
- **Email**: Contact <NAME_EMAIL>

## 🎉 Recognition

Contributors are recognized in:
- `CONTRIBUTORS.md` file
- Release notes
- Annual contributor highlights
- Special badges and mentions

Thank you for contributing to Aetherforge! Together, we're building the future of autonomous software development. 🚀
