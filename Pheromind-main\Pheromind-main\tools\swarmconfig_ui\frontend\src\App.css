.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-header {
  background-color: #1a202c;
  color: white;
  padding: 1rem;
}

.app-content {
  flex: 1;
  padding: 1rem;
}

/* JSON Editor styling */
.jsoneditor-container {
  height: 500px;
  width: 100%;
  margin-bottom: 1rem;
}

/* Validation results styling */
.validation-results {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 0.25rem;
}

.validation-success {
  background-color: #d1fae5;
  border: 1px solid #10b981;
  color: #065f46;
}

.validation-error {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  color: #b91c1c;
}

.error-list {
  margin-top: 0.5rem;
  list-style-type: disc;
  padding-left: 1.5rem;
}

.error-path {
  font-family: monospace;
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
}

/* File operations styling */
.file-operations {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.file-input {
  display: none;
}

.file-input-label,
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.file-input-label:hover,
.button:hover {
  background-color: #2563eb;
}

.button-secondary {
  background-color: #6b7280;
}

.button-secondary:hover {
  background-color: #4b5563;
}

/* Tabs styling */
.react-tabs {
  width: 100%;
}

.react-tabs__tab-list {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin: 0 0 1rem;
  padding: 0;
}

.react-tabs__tab {
  list-style: none;
  padding: 0.5rem 1rem;
  cursor: pointer;
  border: 1px solid transparent;
  border-bottom: none;
  margin-bottom: -1px;
}

.react-tabs__tab--selected {
  background: #fff;
  border-color: #e5e7eb;
  border-bottom: 1px solid #fff;
  border-radius: 0.25rem 0.25rem 0 0;
}

.react-tabs__tab-panel {
  display: none;
}

.react-tabs__tab-panel--selected {
  display: block;
}