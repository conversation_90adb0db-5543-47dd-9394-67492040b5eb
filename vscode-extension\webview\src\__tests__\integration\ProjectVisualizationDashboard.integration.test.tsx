import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import ProjectVisualizationDashboard from '@/panels/ProjectVisualizationDashboard';
import { ProjectStructure, QualityMetrics, WorkflowExecution } from '@/types';

// Mock VS Code API
const mockVSCode = {
  postMessage: vi.fn(),
  sendRequest: vi.fn()
};

vi.mock('@/utils/vscode', () => ({
  vscode: mockVSCode,
  useVSCodeMessage: vi.fn((event, callback) => {
    // Mock message handler registration
    return () => {};
  })
}));

// Mock all visualization components
vi.mock('@/components/ProjectStructureVisualizer', () => ({
  default: ({ projectId, onFileSelect }: any) => (
    <div data-testid="project-structure-visualizer">
      <button onClick={() => onFileSelect?.({ id: 'test-file', path: '/test.ts' })}>
        Test File
      </button>
      Project Structure for {projectId}
    </div>
  )
}));

vi.mock('@/components/CodeQualityVisualizer', () => ({
  default: ({ projectId, onIssueSelect }: any) => (
    <div data-testid="code-quality-visualizer">
      <button onClick={() => onIssueSelect?.({ id: 'test-issue', message: 'Test issue' })}>
        Test Issue
      </button>
      Code Quality for {projectId}
    </div>
  )
}));

vi.mock('@/panels/WorkflowVisualizationPanel', () => ({
  default: ({ projectId }: any) => (
    <div data-testid="workflow-visualization-panel">
      Workflow Visualization for {projectId}
    </div>
  )
}));

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  },
  AnimatePresence: ({ children }: any) => children
}));

const mockProjectStructure: ProjectStructure = {
  id: 'test-structure',
  projectId: 'test-project',
  rootPath: '/test',
  files: [],
  components: [],
  dependencies: { nodes: [], edges: [] },
  metrics: {
    totalFiles: 10,
    totalLines: 1000,
    totalComponents: 5,
    averageComplexity: 3.5,
    overallTestCoverage: 85,
    technicalDebtRatio: 0.15,
    maintainabilityScore: 75,
    dependencyCount: 20,
    circularDependencies: 0
  },
  lastAnalyzed: '2023-01-01T00:00:00Z'
};

const mockQualityMetrics: QualityMetrics = {
  overall: 80,
  categories: [],
  trends: [],
  issues: [
    {
      category: 'security',
      severity: 'high',
      count: 2,
      issues: []
    }
  ],
  recommendations: []
};

const mockWorkflowExecutions: WorkflowExecution[] = [
  {
    id: 'workflow-1',
    workflowId: 'wf-1',
    projectId: 'test-project',
    status: 'running',
    progress: 0.6,
    steps: [],
    agents: [],
    pheromones: [],
    startedAt: '2023-01-01T00:00:00Z',
    metrics: {
      totalSteps: 5,
      completedSteps: 3,
      failedSteps: 0,
      averageStepDuration: 120,
      efficiency: 0.85,
      parallelization: 0.4,
      resourceUtilization: 0.7
    },
    workflow: {
      id: 'wf-1',
      name: 'Test Workflow',
      description: 'Test workflow description',
      version: '1.0.0',
      steps: [],
      triggers: [],
      metadata: {},
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }
  }
];

describe('ProjectVisualizationDashboard Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock responses
    mockVSCode.sendRequest.mockImplementation((command) => {
      switch (command) {
        case 'getProjectStructure':
          return Promise.resolve(mockProjectStructure);
        case 'getQualityMetrics':
          return Promise.resolve(mockQualityMetrics);
        case 'getWorkflowExecutions':
          return Promise.resolve(mockWorkflowExecutions);
        default:
          return Promise.resolve({});
      }
    });
  });

  it('renders dashboard with all views', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    expect(screen.getByText('Project Visualization Dashboard')).toBeInTheDocument();
    
    // Wait for data to load
    await waitFor(() => {
      expect(mockVSCode.sendRequest).toHaveBeenCalledWith('getProjectStructure', {
        projectId: 'test-project'
      });
    });
  });

  it('switches between different views', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByText('Overview')).toBeInTheDocument();
    });
    
    // Switch to structure view
    const structureTab = screen.getByText('Structure');
    fireEvent.click(structureTab);
    
    await waitFor(() => {
      expect(screen.getByTestId('project-structure-visualizer')).toBeInTheDocument();
    });
    
    // Switch to quality view
    const qualityTab = screen.getByText('Quality');
    fireEvent.click(qualityTab);
    
    await waitFor(() => {
      expect(screen.getByTestId('code-quality-visualizer')).toBeInTheDocument();
    });
    
    // Switch to workflow view
    const workflowTab = screen.getByText('Workflow');
    fireEvent.click(workflowTab);
    
    await waitFor(() => {
      expect(screen.getByTestId('workflow-visualization-panel')).toBeInTheDocument();
    });
  });

  it('handles file navigation from structure view', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    // Switch to structure view
    const structureTab = screen.getByText('Structure');
    fireEvent.click(structureTab);
    
    await waitFor(() => {
      expect(screen.getByTestId('project-structure-visualizer')).toBeInTheDocument();
    });
    
    // Click on test file
    const testFileButton = screen.getByText('Test File');
    fireEvent.click(testFileButton);
    
    expect(mockVSCode.postMessage).toHaveBeenCalledWith('openFile', {
      path: '/test.ts'
    });
  });

  it('handles issue navigation from quality view', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    // Switch to quality view
    const qualityTab = screen.getByText('Quality');
    fireEvent.click(qualityTab);
    
    await waitFor(() => {
      expect(screen.getByTestId('code-quality-visualizer')).toBeInTheDocument();
    });
    
    // Click on test issue
    const testIssueButton = screen.getByText('Test Issue');
    fireEvent.click(testIssueButton);
    
    // Should handle issue selection
    expect(screen.getByTestId('code-quality-visualizer')).toBeInTheDocument();
  });

  it('displays overview metrics correctly', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      // Check structure metrics
      expect(screen.getByText('10')).toBeInTheDocument(); // Total files
      expect(screen.getByText('5')).toBeInTheDocument(); // Total components
      
      // Check quality metrics
      expect(screen.getByText('80%')).toBeInTheDocument(); // Quality score
      expect(screen.getByText('2')).toBeInTheDocument(); // Issues count
      
      // Check workflow metrics
      expect(screen.getByText('1')).toBeInTheDocument(); // Running workflows
    });
  });

  it('handles global search across views', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search across all views...')).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search across all views...');
    fireEvent.change(searchInput, { target: { value: 'test' } });
    
    // Should not throw errors
    expect(searchInput).toHaveValue('test');
  });

  it('handles global filtering', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('All Categories')).toBeInTheDocument();
    });
    
    const filterSelect = screen.getByDisplayValue('All Categories');
    fireEvent.change(filterSelect, { target: { value: 'files' } });
    
    // Should not throw errors
    expect(filterSelect).toHaveValue('files');
  });

  it('toggles sidebar visibility', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByText('Hide Sidebar')).toBeInTheDocument();
    });
    
    const sidebarToggle = screen.getByText('Hide Sidebar');
    fireEvent.click(sidebarToggle);
    
    expect(screen.getByText('Show Sidebar')).toBeInTheDocument();
  });

  it('toggles fullscreen mode', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByText('Fullscreen')).toBeInTheDocument();
    });
    
    const fullscreenToggle = screen.getByText('Fullscreen');
    fireEvent.click(fullscreenToggle);
    
    expect(screen.getByText('Exit')).toBeInTheDocument();
  });

  it('handles refresh action', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByText('Refresh')).toBeInTheDocument();
    });
    
    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);
    
    // Should call all data loading functions again
    await waitFor(() => {
      expect(mockVSCode.sendRequest).toHaveBeenCalledTimes(6); // 3 initial + 3 refresh
    });
  });

  it('handles export action', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByText('Export')).toBeInTheDocument();
    });
    
    const exportButton = screen.getByText('Export');
    fireEvent.click(exportButton);
    
    expect(mockVSCode.postMessage).toHaveBeenCalledWith('exportProjectVisualization', 
      expect.objectContaining({
        projectId: 'test-project'
      })
    );
  });

  it('handles loading states correctly', () => {
    // Mock loading state
    mockVSCode.sendRequest.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    // Should show loading state
    expect(screen.getByText('Project Visualization Dashboard')).toBeInTheDocument();
  });

  it('handles error states gracefully', async () => {
    mockVSCode.sendRequest.mockRejectedValue(new Error('Network error'));
    
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByText('Error')).toBeInTheDocument();
      expect(screen.getByText('Network error')).toBeInTheDocument();
    });
    
    // Should show retry button
    const retryButton = screen.getByText('Retry');
    expect(retryButton).toBeInTheDocument();
    
    // Test retry functionality
    mockVSCode.sendRequest.mockResolvedValue(mockProjectStructure);
    fireEvent.click(retryButton);
    
    await waitFor(() => {
      expect(mockVSCode.sendRequest).toHaveBeenCalledTimes(4); // 3 failed + 1 retry
    });
  });

  it('calculates health score correctly', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      // Health score should be calculated from quality (80%) + test coverage (85%) + workflow success
      expect(screen.getByText(/\d+%/)).toBeInTheDocument(); // Some percentage for health score
    });
  });

  it('displays quick actions', async () => {
    render(<ProjectVisualizationDashboard projectId="test-project" />);
    
    await waitFor(() => {
      expect(screen.getByText('Analyze Project')).toBeInTheDocument();
      expect(screen.getByText('Quality Check')).toBeInTheDocument();
      expect(screen.getByText('Generate Docs')).toBeInTheDocument();
      expect(screen.getByText('Optimize')).toBeInTheDocument();
    });
    
    // Test quick action
    const analyzeButton = screen.getByText('Analyze Project');
    fireEvent.click(analyzeButton);
    
    expect(mockVSCode.postMessage).toHaveBeenCalledWith('analyzeProject', {});
  });
});
