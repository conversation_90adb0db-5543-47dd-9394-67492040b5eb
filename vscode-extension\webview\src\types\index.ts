// Core types for Aetherforge webview panels

export interface VSCodeAPI {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
}

export interface ProjectConfig {
  name: string;
  description: string;
  type: ProjectType;
  workflow: WorkflowType;
  requirements: string[];
  priority: Priority;
  agentBehavior: AgentBehavior;
  features: ProjectFeatures;
  technologies: Technologies;
  deployment: DeploymentConfig;
  quality: QualityConfig;
}

export type ProjectType = 
  | 'fullstack'
  | 'frontend'
  | 'backend'
  | 'mobile'
  | 'desktop'
  | 'api'
  | 'game'
  | 'library'
  | 'cli'
  | 'microservice'
  | 'data-platform';

export type WorkflowType = 
  | 'default'
  | 'rapid'
  | 'thorough'
  | 'enterprise'
  | 'experimental'
  | 'custom';

export type Priority = 'low' | 'normal' | 'high' | 'urgent';

export type AgentBehavior = 
  | 'conservative'
  | 'balanced'
  | 'aggressive'
  | 'creative'
  | 'production'
  | 'experimental';

export interface ProjectFeatures {
  enableParallelExecution: boolean;
  enableCodeReview: boolean;
  enableTesting: boolean;
  enableDocumentation: boolean;
  enableCICD: boolean;
  enableDocker: boolean;
  enableDeployment: boolean;
  enableAIOptimization: boolean;
  enableSecurityScan: boolean;
  enablePerformanceOptimization: boolean;
  enableMonitoring: boolean;
  enableAnalytics: boolean;
}

export interface Technologies {
  programmingLanguages: string[];
  frameworks: string[];
  databases: string[];
  cloudProviders: string[];
  tools: string[];
}

export interface DeploymentConfig {
  targets: string[];
  environment: 'development' | 'staging' | 'production';
  scalability: 'single' | 'horizontal' | 'vertical' | 'auto';
  monitoring: boolean;
}

export interface QualityConfig {
  codeQualityLevel: 'basic' | 'standard' | 'high' | 'enterprise';
  testCoverageTarget: number;
  documentationLevel: 'minimal' | 'standard' | 'comprehensive';
  securityLevel: 'basic' | 'standard' | 'high' | 'enterprise';
}

export interface Agent {
  id: string;
  name: string;
  role: string;
  status: AgentStatus;
  capabilities: string[];
  currentTask?: string;
  performance: AgentPerformance;
  lastActivity: string;
  avatar?: string;
}

export type AgentStatus = 
  | 'idle'
  | 'busy'
  | 'working'
  | 'paused'
  | 'error'
  | 'offline'
  | 'maintenance';

export interface AgentPerformance {
  tasksCompleted: number;
  successRate: number;
  averageResponseTime: number;
  efficiency: number;
  reliability: number;
}

export interface ChatMessage {
  id: string;
  agentId: string;
  content: string;
  type: 'user' | 'agent' | 'system';
  timestamp: string;
  metadata?: {
    taskId?: string;
    attachments?: string[];
    reactions?: string[];
  };
}

export interface Task {
  id: string;
  title: string;
  description: string;
  agentId: string;
  status: TaskStatus;
  priority: Priority;
  progress: number;
  estimatedDuration: number;
  actualDuration?: number;
  dependencies: string[];
  results?: any;
  createdAt: string;
  updatedAt: string;
}

export type TaskStatus = 
  | 'pending'
  | 'assigned'
  | 'in_progress'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'blocked';

export interface Project {
  id: string;
  name: string;
  slug: string;
  description: string;
  status: ProjectStatus;
  progress: ProjectProgress;
  config: ProjectConfig;
  agents: Agent[];
  tasks: Task[];
  timeline: TimelineEvent[];
  metrics: ProjectMetrics;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export type ProjectStatus = 
  | 'initializing'
  | 'planning'
  | 'in_progress'
  | 'testing'
  | 'deploying'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'paused';

export interface ProjectProgress {
  overall: number;
  phases: PhaseProgress[];
  currentPhase: string;
  estimatedCompletion: string;
}

export interface PhaseProgress {
  name: string;
  progress: number;
  status: 'pending' | 'active' | 'completed' | 'failed';
  startedAt?: string;
  completedAt?: string;
  tasks: number;
  completedTasks: number;
}

export interface TimelineEvent {
  id: string;
  type: 'project_started' | 'phase_completed' | 'task_completed' | 'agent_assigned' | 'error' | 'milestone';
  title: string;
  description: string;
  timestamp: string;
  agentId?: string;
  taskId?: string;
  metadata?: any;
}

export interface ProjectMetrics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  activeAgents: number;
  codeQuality: number;
  testCoverage: number;
  performance: number;
  security: number;
  documentation: number;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  status: WorkflowStatus;
  progress: number;
  executionId?: string;
  projectId?: string;
  startedAt?: string;
  completedAt?: string;
  estimatedDuration: number;
  actualDuration?: number;
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress: number;
  agentId?: string;
  dependencies: string[];
  outputs?: any;
  startedAt?: string;
  completedAt?: string;
}

export type WorkflowStatus = 
  | 'draft'
  | 'ready'
  | 'running'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface Pheromone {
  id: string;
  type: string;
  source: string;
  target?: string;
  data: any;
  strength: number;
  timestamp: string;
  projectId?: string;
  agentId?: string;
  ttl: number;
}

export interface SystemStatus {
  orchestrator: ServiceStatus;
  agents: ServiceStatus;
  database: ServiceStatus;
  messageQueue: ServiceStatus;
  storage: ServiceStatus;
  monitoring: ServiceStatus;
}

export interface ServiceStatus {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  uptime: number;
  responseTime: number;
  errorRate: number;
  lastCheck: string;
  version?: string;
  details?: any;
}

export interface WebviewMessage {
  command: string;
  data?: any;
  requestId?: string;
}

export interface WebviewState {
  loading: boolean;
  error?: string;
  connected: boolean;
  lastUpdate: string;
}

// Project Structure Visualization Types
export interface ProjectFile {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  language?: string;
  lastModified: string;
  children?: ProjectFile[];
  dependencies?: string[];
  exports?: string[];
  imports?: string[];
  complexity?: number;
  testCoverage?: number;
  issues?: CodeIssue[];
}

export interface CodeIssue {
  id: string;
  type: 'error' | 'warning' | 'info' | 'suggestion';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  line?: number;
  column?: number;
  rule?: string;
  category: 'syntax' | 'logic' | 'style' | 'security' | 'performance' | 'maintainability';
}

export interface ArchitectureComponent {
  id: string;
  name: string;
  type: 'service' | 'module' | 'component' | 'database' | 'api' | 'ui';
  description: string;
  technologies: string[];
  dependencies: string[];
  dependents: string[];
  files: string[];
  status: 'planned' | 'in_progress' | 'completed' | 'testing' | 'deployed';
  metrics?: ComponentMetrics;
}

export interface ComponentMetrics {
  linesOfCode: number;
  complexity: number;
  testCoverage: number;
  maintainabilityIndex: number;
  technicalDebt: number;
  performance: number;
  security: number;
}

export interface ProjectStructure {
  id: string;
  projectId: string;
  rootPath: string;
  files: ProjectFile[];
  components: ArchitectureComponent[];
  dependencies: DependencyGraph;
  metrics: ProjectStructureMetrics;
  lastAnalyzed: string;
}

export interface DependencyGraph {
  nodes: DependencyNode[];
  edges: DependencyEdge[];
}

export interface DependencyNode {
  id: string;
  name: string;
  type: 'internal' | 'external' | 'system';
  version?: string;
  size: number;
  importance: number;
}

export interface DependencyEdge {
  source: string;
  target: string;
  type: 'import' | 'require' | 'inherit' | 'compose' | 'use';
  strength: number;
}

export interface ProjectStructureMetrics {
  totalFiles: number;
  totalLines: number;
  totalComponents: number;
  averageComplexity: number;
  overallTestCoverage: number;
  technicalDebtRatio: number;
  maintainabilityScore: number;
  dependencyCount: number;
  circularDependencies: number;
}

// Code Quality Visualization Types
export interface QualityMetrics {
  overall: number;
  categories: QualityCategory[];
  trends: QualityTrend[];
  issues: QualityIssueGroup[];
  recommendations: QualityRecommendation[];
}

export interface QualityCategory {
  name: string;
  score: number;
  weight: number;
  metrics: QualityMetric[];
}

export interface QualityMetric {
  name: string;
  value: number;
  target: number;
  unit: string;
  trend: 'improving' | 'stable' | 'declining';
}

export interface QualityTrend {
  timestamp: string;
  overall: number;
  categories: { [key: string]: number };
}

export interface QualityIssueGroup {
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  count: number;
  issues: CodeIssue[];
}

export interface QualityRecommendation {
  id: string;
  type: 'fix' | 'refactor' | 'optimize' | 'test' | 'document';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  effort: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  files: string[];
  automated: boolean;
}

// Workflow Execution Types
export interface WorkflowExecution {
  id: string;
  workflowId: string;
  projectId: string;
  status: WorkflowStatus;
  progress: number;
  steps: WorkflowStepExecution[];
  agents: WorkflowAgent[];
  pheromones: WorkflowPheromone[];
  startedAt: string;
  completedAt?: string;
  estimatedCompletion?: string;
  metrics: WorkflowMetrics;
}

export interface WorkflowStepExecution {
  id: string;
  stepId: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress: number;
  agentId?: string;
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  outputs?: any;
  logs: WorkflowLog[];
  dependencies: string[];
  dependents: string[];
}

export interface WorkflowAgent {
  id: string;
  name: string;
  role: string;
  status: AgentStatus;
  currentStep?: string;
  assignedSteps: string[];
  completedSteps: string[];
  performance: AgentPerformance;
  position?: { x: number; y: number };
}

export interface WorkflowPheromone {
  id: string;
  type: 'task_start' | 'task_complete' | 'agent_communication' | 'resource_request' | 'error' | 'milestone';
  source: string;
  target?: string;
  data: any;
  strength: number;
  timestamp: string;
  stepId?: string;
  agentId?: string;
}

export interface WorkflowLog {
  id: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: string;
  source: string;
  metadata?: any;
}

export interface WorkflowMetrics {
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  averageStepDuration: number;
  efficiency: number;
  parallelization: number;
  resourceUtilization: number;
}
