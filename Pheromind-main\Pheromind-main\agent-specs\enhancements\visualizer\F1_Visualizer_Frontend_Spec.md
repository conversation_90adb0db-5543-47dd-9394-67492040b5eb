# Feature Specification: F1 - Visualizer Frontend

**Document ID:** `agent-specs/enhancements/visualizer/F1_Visualizer_Frontend_Spec.md`
**Parent Spec:** [`F1_Visualizer_Overall_Spec.md`](./F1_Visualizer_Overall_Spec.md)
**Version:** 1.0
**Date:** 2025-05-15

## 1. Introduction

This document details the frontend requirements for the "Visual Pheromone & Documentation Landscape Tool." The frontend will be a Single Page Application (SPA) built using React with TypeScript and styled with Tailwind CSS. It will connect to a Node.js backend via WebSockets to receive real-time updates of the `.pheromone` file.

## 2. Core Technologies

* **Framework/Library:** React v18+ (using functional components and Hooks)
* **Language:** TypeScript
* **Styling:** Tailwind CSS v3+
* **State Management:** React Context API for simple global state (e.g., WebSocket connection status, theme). Zustand or Redux Toolkit for more complex state like the pheromone data if Context becomes unwieldy. Start with Context, evaluate need for Zustand/Redux.
* **WebSocket Client:** `socket.io-client` or native WebSocket API.
* **Graph Visualization:** `vis-network` (from Vis.js) or `react-flow`.
* **Timeline Visualization:** `vis-timeline` (from Vis.js).
* **Charting:** `Recharts` or `Chart.js` (wrapped for React).
* **Routing:** `react-router-dom` for different views/pages if needed (e.g., separate pages for timeline, graph, docs).
* **Build Tool:** Vite or Create React App.

## 3. UI Components & Views

### 3.1. Overall Layout / Shell (`App.tsx`)

* **Header:**
    * Application Title (e.g., "Pheromind Visualizer").
    * Connection Status Indicator (to backend WebSocket: Connected, Disconnected, Error).
    * Path to `.pheromone` file being watched (displayed, received from backend or configurable).
    * Timestamp of last update received.
* **Navigation (Sidebar or Tabs):**
    * Dashboard
    * Signal Timeline
    * Signal Network
    * Documentation Registry
* **Main Content Area:** Renders the active view.

### 3.2. Dashboard View (`DashboardView.tsx`)

* **Purpose:** Provide a high-level overview and quick stats.
* **Components:**
    * **Signal Count Card:** Displays total number of active signals.
    * **Signal Types Distribution Chart:** Pie or Bar chart showing count of signals per `signalType`. (Use Recharts/Chart.js)
    * **Documentation Registry Count Card:** Displays total number of documents in the registry.
    * **Recent Signals List (Optional):** A small list of the 5 most recent signals (summary).
    * **Pheromone File Info:** Display path and last modified time.

### 3.3. Signal Timeline/Stream View (`SignalTimelineView.tsx`)

* **Purpose:** Display signals chronologically.
* **Layout:**
    * Controls Area: Filters (by `signalType`, `target`, `category`, text search in `message`), Sort (by timestamp, strength).
    * Signal List Area: A scrollable list of `SignalCard` components.
* **`SignalCard.tsx` Component:**
    * Displays: `id`, `signalType`, `target`, `strength` (visual indicator like a bar or intensity), `timestamp_created`, `last_updated_timestamp`.
    * Color-coded border or background based on `signalType` or `category`.
    * Expandable section or button to show a snippet of the `message`.
    * "View Details" button to open `SignalDetailModal`.
* **Real-time Updates:** New signals should be added to the list dynamically. Consider virtualized list for performance with many signals.

### 3.4. Signal Network View (`SignalNetworkView.tsx`)

* **Purpose:** Visualize relationships between signals and targets.
* **Layout:**
    * Controls Area: Options to group nodes (e.g., by `target`, `category`), filter.
    * Graph Area: Rendered using `vis-network` or `react-flow`.
* **Graph Elements:**
    * **Nodes:**
        * Primary nodes: Unique `target` values from signals.
        * Secondary nodes (optional, if not too cluttered): Individual signals, linked to their target.
        * Node appearance: Label with target name, color by category or type of target.
    * **Edges:**
        * Connect signals to their targets, or show sequences if timestamps imply order.
        * Edge properties (color, thickness) could represent signal `strength` or `signalType`.
* **Interactions:**
    * Click on a node (target) to:
        * Highlight connected signals.
        * Filter the `SignalTimelineView` to show only signals related to this target.
        * Display target details in a side panel.
    * Hover over nodes/edges for tooltips with more info.
    * Zoom, pan, drag nodes.

### 3.5. Documentation Registry Explorer View (`DocumentationExplorerView.tsx`)

* **Purpose:** Browse and search documented artifacts.
* **Layout:**
    * Controls Area: Filter by `type`, text search in `description` or `file_path`.
    * Table Area:
        * Columns: `File Path`, `Description`, `Type`, `Last Updated`, `Generated By`.
        * Sortable by each column.
* **Interactions:**
    * Clicking a file path: Ideally, would attempt to trigger an action to open it (this is tricky for local files from a browser; might show a "copy path" button or rely on OS-level file associations if a local helper app were ever considered). For v1, displaying the path clearly is sufficient.

### 3.6. Signal Detail Modal (`SignalDetailModal.tsx`)

* **Purpose:** Display the full JSON content of a selected signal.
* **Content:**
    * Signal ID as title.
    * Pretty-printed, syntax-highlighted JSON representation of the signal object.
    * "Copy JSON" button.
* **Trigger:** Opened from `SignalCard` or by clicking a signal representation in the Network View.

## 4. State Management Details

* **Global State (React Context or Zustand):**
    * `pheromoneData`: `{ signals: [], documentationRegistry: {} }`. This will be the main data object updated by WebSocket.
    * `connectionStatus`: (e.g., 'connected', 'disconnected', 'error').
    * `watchedFilePath`: Path to the `.pheromone` file.
    * `lastUpdateTimestamp`: Timestamp of the last data received.
* **Local Component State:**
    * Filter values, sort orders, UI toggles (e.g., modal visibility).
    * Data derived for specific visualizations (e.g., nodes/edges for graphs).

## 5. WebSocket Integration

* Establish connection to the backend WebSocket server on application load.
* Handle `connect`, `disconnect`, `error` events, updating `connectionStatus`.
* Listen for `pheromone_updated` (or similar) events from the server.
* On receiving data, update the global `pheromoneData` state.
* Implement a mechanism for initial data load (e.g., a specific "request_initial_data" event emitted to server after connection, or an initial HTTP GET).

## 6. Error Handling & User Feedback

* Display clear messages for WebSocket connection errors.
* If `.pheromone` data received is invalid JSON, show an error message.
* Loading indicators for initial data fetch or when processing large updates.
* Empty states for views when no data is available (e.g., "No signals yet," "No documents in registry").

## 7. Build & Development

* Use Vite (recommended for speed) or Create React App.
* Setup ESLint, Prettier, and TypeScript for code quality.
* Develop components in a modular way (e.g., in a `components/` directory).
* Views/pages in a `views/` or `pages/` directory.
