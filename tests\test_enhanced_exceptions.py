"""
Tests for enhanced exception classes in Aetherforge
"""

import pytest
from src.orchestrator import (
    AetherforgeError,
    ProjectCreationError,
    WorkflowExecutionError,
    AgentExecutionError,
    ComponentConnectionError
)
from src.workflow_engine import (
    WorkflowTimeoutError,
    AgentAssignmentError
)


class TestAetherforgeExceptions:
    """Test enhanced exception classes"""

    def test_aetherforge_error_basic(self):
        """Test basic AetherforgeError functionality"""
        error = AetherforgeError("Test error")
        assert str(error) == "Test error"
        assert error.message == "Test error"
        assert error.details == {}
        assert error.error_type == "aetherforge_error"

    def test_aetherforge_error_with_details(self):
        """Test AetherforgeError with details"""
        details = {"code": 500, "context": "test"}
        error = AetherforgeError("Test error", details)
        assert error.details == details

    def test_project_creation_error(self):
        """Test ProjectCreationError"""
        error = ProjectCreationError("Failed to create project", "test-project-123")
        assert "Project Creation Error [test-project-123]" in str(error)
        assert error.project_id == "test-project-123"
        assert error.error_type == "project_creation"

    def test_project_creation_error_without_id(self):
        """Test ProjectCreationError without project ID"""
        error = ProjectCreationError("Failed to create project")
        assert "Project Creation Error:" in str(error)
        assert error.project_id is None

    def test_workflow_execution_error(self):
        """Test WorkflowExecutionError"""
        error = WorkflowExecutionError(
            "Workflow failed", 
            workflow_id="wf-123", 
            step_id="step-456"
        )
        assert "Workflow Execution Error [Workflow: wf-123, Step: step-456]" in str(error)
        assert error.workflow_id == "wf-123"
        assert error.step_id == "step-456"
        assert error.error_type == "workflow_execution"

    def test_workflow_execution_error_partial_info(self):
        """Test WorkflowExecutionError with partial information"""
        error = WorkflowExecutionError("Workflow failed", workflow_id="wf-123")
        assert "Workflow Execution Error [Workflow: wf-123]" in str(error)
        assert error.step_id is None

    def test_agent_execution_error(self):
        """Test AgentExecutionError"""
        error = AgentExecutionError(
            "Agent failed", 
            agent_type="developer", 
            agent_id="dev-001"
        )
        assert "Agent Execution Error [Type: developer, ID: dev-001]" in str(error)
        assert error.agent_type == "developer"
        assert error.agent_id == "dev-001"
        assert error.error_type == "agent_execution"

    def test_component_connection_error(self):
        """Test ComponentConnectionError"""
        error = ComponentConnectionError(
            "Connection failed", 
            component_name="database", 
            endpoint="localhost:5432"
        )
        assert "Component Connection Error [Component: database, Endpoint: localhost:5432]" in str(error)
        assert error.component_name == "database"
        assert error.endpoint == "localhost:5432"
        assert error.error_type == "component_connection"

    def test_workflow_timeout_error(self):
        """Test WorkflowTimeoutError"""
        error = WorkflowTimeoutError(
            "Workflow timed out", 
            workflow_id="wf-123", 
            timeout_duration=300.0
        )
        assert "Timeout: 300.0s" in str(error)
        assert error.timeout_duration == 300.0
        assert error.error_type == "workflow_timeout"

    def test_agent_assignment_error(self):
        """Test AgentAssignmentError"""
        requirements = {"capabilities": ["python", "testing"]}
        available_agents = ["agent1", "agent2"]
        
        error = AgentAssignmentError(
            "No suitable agent found",
            workflow_id="wf-123",
            step_id="step-456",
            agent_requirements=requirements,
            available_agents=available_agents
        )
        
        error_str = str(error)
        assert "Required:" in error_str
        assert "Available: 2 agents" in error_str
        assert error.agent_requirements == requirements
        assert error.available_agents == available_agents
        assert error.error_type == "agent_assignment"

    def test_exception_inheritance(self):
        """Test that all custom exceptions inherit properly"""
        assert issubclass(ProjectCreationError, AetherforgeError)
        assert issubclass(WorkflowExecutionError, AetherforgeError)
        assert issubclass(AgentExecutionError, AetherforgeError)
        assert issubclass(ComponentConnectionError, AetherforgeError)
        assert issubclass(WorkflowTimeoutError, WorkflowExecutionError)
        assert issubclass(AgentAssignmentError, WorkflowExecutionError)

    def test_exception_with_details_inheritance(self):
        """Test that details are properly inherited"""
        details = {"timestamp": "2024-01-01", "user": "test"}
        error = ProjectCreationError("Test", "proj-123", details)
        assert error.details == details

    def test_error_type_consistency(self):
        """Test that error types are set correctly"""
        errors = [
            (ProjectCreationError("test"), "project_creation"),
            (WorkflowExecutionError("test"), "workflow_execution"),
            (AgentExecutionError("test"), "agent_execution"),
            (ComponentConnectionError("test"), "component_connection"),
            (WorkflowTimeoutError("test"), "workflow_timeout"),
            (AgentAssignmentError("test"), "agent_assignment")
        ]
        
        for error, expected_type in errors:
            assert error.error_type == expected_type
