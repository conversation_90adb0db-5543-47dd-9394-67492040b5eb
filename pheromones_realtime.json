{"recent_messages": [{"id": "6665cbcc-c89d-40a8-941f-307e3144cbe2", "type": "project_creation_started", "data": {"project_id": "54582d6e-b984-4631-bd57-f5da5f477bc1", "project_slug": "test1-3", "prompt": "Create a Scientific calculator web page", "project_type": "frontend", "metadata": {"project_id": "54582d6e-b984-4631-bd57-f5da5f477bc1", "slug": "test1-3", "name": "Test1", "description": "Create a Scientific calculator web page", "project_type": "frontend", "priority": "normal", "agent_behavior": "balanced", "created_at": "2025-06-20T20:56:12.398694", "status": "initializing", "configuration": {"enable_parallel_execution": true, "max_iterations": 3, "enable_code_review": true, "enable_testing": true, "code_quality_level": "production", "documentation_level": "comprehensive", "test_coverage_target": 0.8, "enable_ai_optimization": true, "enable_security_scan": true, "enable_performance_optimization": true}, "technical_specs": {"target_platforms": [], "programming_languages": [], "frameworks": [], "databases": [], "deployment_targets": []}, "requirements": {}, "custom_templates": {}, "environment_variables": {}, "deadline": null, "estimated_completion": null, "version": "1.0.0"}, "configuration": {"agent_behavior": "balanced", "parallel_execution": true, "quality_level": "production", "test_coverage_target": 0.8}, "timestamp": "2025-06-20T20:56:12.400695", "orchestrator_version": "1.0.0", "system_info": {"hostname": "unknown", "python_version": "3.12.3"}}, "project_id": "54582d6e-b984-4631-bd57-f5da5f477bc1", "agent_id": null, "trail_id": null, "timestamp": "2025-06-20T20:56:12.400695", "strength": 1.0, "processed": false}, {"id": "a5d0fb2b-ea08-43be-8d7f-01edd2a37f3d", "type": "workflow_start_failed", "data": {"workflow_id": "greenfield-frontend", "error": "Workflow greenfield-frontend not found", "error_type": "ValueError", "timestamp": "2025-06-20T20:56:12.439694", "orchestrator_version": "1.0.0", "system_info": {"hostname": "unknown", "python_version": "3.12.3"}}, "project_id": "54582d6e-b984-4631-bd57-f5da5f477bc1", "agent_id": null, "trail_id": null, "timestamp": "2025-06-20T20:56:12.439694", "strength": 1.0, "processed": false}, {"id": "ddff3bbb-0e2e-4778-b08f-6e71f723dd51", "type": "project_creation_failed", "data": {"project_id": "54582d6e-b984-4631-bd57-f5da5f477bc1", "error_type": "unexpected_error", "error_details": {"error_type": "TypeError", "error_message": "WorkflowExecutionError.__init__() got an unexpected keyword argument 'error_code'", "traceback": "Traceback (most recent call last):\n  File \"E:\\Projects\\TaoForge Main\\TaoForge\\src\\orchestrator.py\", line 2397, in start_enhanced_workflow\n    workflow_execution = await workflow_engine.start_workflow(\n                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Projects\\TaoForge Main\\TaoForge\\src\\workflow_engine.py\", line 6988, in start_workflow\n    raise ValueError(f\"Workflow {workflow_id} not found\")\nValueError: Workflow greenfield-frontend not found\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"E:\\Projects\\TaoForge Main\\TaoForge\\src\\orchestrator.py\", line 997, in create_project\n    workflow_execution = await start_enhanced_workflow(\n                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Projects\\TaoForge Main\\TaoForge\\src\\orchestrator.py\", line 2434, in start_enhanced_workflow\n    raise WorkflowExecutionError(\n          ^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: WorkflowExecutionError.__init__() got an unexpected keyword argument 'error_code'\n", "execution_time": 0.04400014877319336}, "timestamp": "2025-06-20T20:56:12.440695", "orchestrator_version": "1.0.0", "system_info": {"hostname": "unknown", "python_version": "3.12.3"}}, "project_id": "54582d6e-b984-4631-bd57-f5da5f477bc1", "agent_id": null, "trail_id": null, "timestamp": "2025-06-20T20:56:12.440695", "strength": 1.0, "processed": false}], "stats": {"total_messages": 3, "active_projects": 1, "active_subscribers": 0, "messages_per_second": 0, "last_activity": "2025-06-20T20:56:12.440695", "system_start_time": "2025-06-20T20:55:30.726375"}, "last_saved": "2025-06-20T20:57:24.396895"}