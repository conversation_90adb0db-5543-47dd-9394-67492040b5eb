{"name": "aetherforge", "displayName": "Aetherforge", "description": "Autonomous AI Software Creation System", "version": "0.1.0", "engines": {"vscode": "^1.80.0"}, "categories": ["Other"], "activationEvents": ["onCommand:aetherforge.start"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "aetherforge.start", "title": "Aetherforge: Start"}]}, "scripts": {"vscode:prepublish": "npm run esbuild-base -- --minify", "esbuild-base": "esbuild ./src/extension.ts --bundle --outfile=dist/extension.js --external:vscode --format=cjs --platform=node", "esbuild": "npm run esbuild-base -- --sourcemap", "esbuild-watch": "npm run esbuild-base -- --sourcemap --watch", "test-compile": "tsc -p ./"}, "devDependencies": {"@types/node": "^16.11.7", "@types/vscode": "^1.80.0", "esbuild": "^0.18.11", "typescript": "^5.1.6"}}