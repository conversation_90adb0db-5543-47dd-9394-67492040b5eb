# Enhanced API Management System for Aetherforge

## Overview

The Enhanced API Management System provides comprehensive, resilient, and secure API integration for all Aetherforge agents. It supports multiple AI providers with automatic fallback, quota management, rate limiting, and advanced resilience mechanisms.

## Key Features

### 🔧 Multi-Provider Support
- **OpenAI**: GPT-4, GPT-3.5-turbo, and other OpenAI models
- **Anthropic**: Claude-3 Opus, Sonnet, and Haiku models
- **Azure OpenAI**: Enterprise-grade OpenAI models
- **OpenRouter**: Access to multiple AI models through one API
- **Local/Ollama**: Local model support for offline usage

### 🛡️ Security & Key Management
- **AES-256 Encryption**: Secure storage of API keys with PBKDF2-HMAC-SHA256
- **Environment Integration**: Automatic loading from environment variables
- **CLI Management**: Comprehensive command-line interface for key management
- **Permission Control**: Proper file permissions (600) for security
- **Key Validation**: Real-time validation of API keys

### 🔄 Resilience Mechanisms
- **Exponential Backoff**: Intelligent retry logic with jitter
- **Provider Fallback**: Automatic switching between providers
- **Model Fallback**: Fallback to alternative models within providers
- **Cache Fallback**: Use cached responses when providers fail
- **Degraded Mode**: Graceful degradation during outages
- **Circuit Breakers**: Prevent cascading failures

### 📊 Quota & Rate Management
- **Usage Tracking**: Real-time monitoring of API usage
- **Rate Limiting**: Configurable rate limits per provider
- **Quota Warnings**: Proactive alerts at 80% usage
- **Auto-switching**: Automatic provider switching when quotas exceeded
- **Usage Reporting**: Comprehensive usage analytics and projections

### 🎯 Agent Integration
- **Enhanced Base Agent**: Unified base class for all agents
- **Configuration Management**: Per-agent configuration options
- **Error Handling**: Comprehensive error handling and recovery
- **Metrics Tracking**: Performance and usage metrics per agent
- **Status Monitoring**: Real-time agent status and health checks

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Enhanced API Manager                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Provider      │  │   Resilience    │  │    Quota     │ │
│  │   Management    │  │   Manager       │  │   Manager    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Secure Key    │  │   Rate Limiter  │  │   Cache      │ │
│  │   Storage       │  │                 │  │   Manager    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Enhanced Base Agent                       │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Analyst       │  │   Architect     │  │   Developer  │ │
│  │   Agent         │  │   Agent         │  │   Agent      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   QA Agent      │  │   Specialist    │                   │
│  │                 │  │   Agents        │                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────────────────────────────────────────────┘
```

## Quick Start

### 1. Setup API Keys

```bash
# Run the interactive setup wizard
python -m src.api_key_cli setup

# Or add keys individually
python -m src.api_key_cli add openai
python -m src.api_key_cli add anthropic
```

### 2. Basic Usage

```python
from src.api_manager import APIManager
from src.base_agent_enhanced import BaseAgentEnhanced, AgentType, AgentConfig

# Initialize API Manager
api_manager = APIManager()

# Create an enhanced agent
config = AgentConfig(
    preferred_provider="openai",
    model="gpt-4",
    max_tokens=3000,
    enable_resilience=True
)

class MyAgent(BaseAgentEnhanced):
    async def execute_agent_logic(self, task, context):
        messages = [{"role": "user", "content": task["prompt"]}]
        result = await self.generate_text(messages)
        return {"success": True, "result": result}

# Use the agent
agent = MyAgent("my-agent", "My Agent", AgentType.SPECIALIST, config)
result = await agent.execute_task({"type": "generate", "prompt": "Hello, world!"})
```

### 3. CLI Commands

```bash
# List configured providers
python -m src.api_key_cli list

# Test a provider
python -m src.api_key_cli test openai

# Check status
python -m src.api_key_cli status

# Fix permissions
python -m src.api_key_cli fix-permissions
```

## Configuration

### API Manager Configuration

```python
from src.api_manager import APIManager, APIProvider

# Initialize with custom configuration
api_manager = APIManager(config_file="custom_config.json")

# Set quota limits
api_manager.set_quota_limit(APIProvider.OPENAI, "daily_requests", 1000)
api_manager.set_quota_limit(APIProvider.OPENAI, "daily_tokens", 100000)

# Configure rate limiting
api_manager.configure_rate_limiting(APIProvider.OPENAI, requests_per_minute=60)
```

### Agent Configuration

```python
from src.base_agent_enhanced import AgentConfig

config = AgentConfig(
    # API Configuration
    preferred_provider="openai",
    fallback_providers=["anthropic", "openrouter"],
    model="gpt-4",
    max_tokens=3000,
    temperature=0.7,
    
    # Resilience Configuration
    enable_resilience=True,
    max_retries=3,
    retry_delay=1.0,
    enable_fallback=True,
    enable_caching=True,
    
    # Performance Configuration
    timeout_seconds=300,
    enable_streaming=False,
    
    # Quality Configuration
    enable_validation=True,
    enable_logging=True,
    log_level="INFO"
)
```

## Monitoring & Metrics

### Usage Monitoring

```python
# Get quota status
status = api_manager.check_quota_status(APIProvider.OPENAI)
print(f"Usage: {status['usage_percentage']}")
print(f"Warnings: {status['warnings']}")

# Generate usage report
report = await api_manager.generate_usage_report(time_period="daily")
print(f"Total requests: {report['summary']['total_requests']}")
print(f"Total cost: ${report['summary']['total_cost']:.2f}")

# Project future usage
projection = await api_manager.project_usage(APIProvider.OPENAI, days_ahead=7)
print(f"Projected usage: {projection['projected_usage']}")
```

### Agent Metrics

```python
# Get agent status
status = await agent.get_status()
print(f"Success rate: {status['metrics']['success_rate']:.2%}")
print(f"Average execution time: {status['metrics']['average_execution_time']:.2f}s")
print(f"Total tokens used: {status['metrics']['total_tokens_used']}")
```

### Resilience Status

```python
# Get resilience status
if api_manager.resilience_manager:
    status = api_manager.resilience_manager.get_resilience_status()
    print(f"Degraded mode: {status['degraded_mode_active']}")
    print(f"Cache hits: {status['metrics']['cache_hits']}")
    print(f"Fallbacks used: {status['metrics']['fallbacks_used']}")
```

## Best Practices

### 1. Security
- Always use the CLI to manage API keys
- Set proper file permissions (600) for key storage
- Regularly rotate API keys
- Use environment variables in production

### 2. Resilience
- Enable resilience mechanisms for production workloads
- Configure appropriate retry delays and max retries
- Set up multiple provider fallbacks
- Monitor circuit breaker status

### 3. Cost Management
- Set quota limits for all providers
- Monitor usage regularly
- Use smaller models for simple tasks
- Implement response caching

### 4. Performance
- Use streaming for long responses
- Configure appropriate timeouts
- Monitor response times
- Use local models for development

## Troubleshooting

### Common Issues

1. **API Key Validation Fails**
   ```bash
   # Check key format and permissions
   python -m src.api_key_cli test openai
   ```

2. **Quota Exceeded**
   ```bash
   # Check usage and switch providers
   python -m src.api_key_cli list
   ```

3. **High Latency**
   ```python
   # Check provider performance
   status = api_manager.get_provider_status()
   ```

4. **Permission Errors**
   ```bash
   # Fix file permissions
   python -m src.api_key_cli fix-permissions
   ```

### Debugging

Enable debug logging:
```python
import logging
logging.getLogger('api_manager').setLevel(logging.DEBUG)
logging.getLogger('agent').setLevel(logging.DEBUG)
```

## Future Enhancements

- [ ] Support for additional AI providers (Google, Cohere, etc.)
- [ ] Advanced caching strategies (Redis, Memcached)
- [ ] Real-time cost tracking and budgeting
- [ ] A/B testing for different models
- [ ] Advanced analytics and reporting dashboard
- [ ] Integration with monitoring systems (Prometheus, Grafana)

## Contributing

When adding new providers or features:

1. Follow the existing provider interface pattern
2. Add comprehensive tests
3. Update documentation
4. Ensure security best practices
5. Add monitoring and metrics support

## License

This enhanced API management system is part of the Aetherforge project and follows the same licensing terms.
