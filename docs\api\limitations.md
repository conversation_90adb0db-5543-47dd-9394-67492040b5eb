# TaoForge API Limitations and Best Practices

This document outlines the current limitations of the TaoForge API, best practices for usage, and guidelines for optimal performance.

## 📊 Rate Limits and Quotas

### Request Rate Limits

| Tier | Requests/Hour | Concurrent Projects | Burst Limit |
|------|---------------|-------------------|-------------|
| **Free** | 100 | 2 | 10/minute |
| **Pro** | 1,000 | 10 | 50/minute |
| **Enterprise** | 10,000 | 50 | 200/minute |
| **Custom** | Negotiable | Negotiable | Negotiable |

### Project Limits

| Resource | Free | Pro | Enterprise |
|----------|------|-----|------------|
| **Max Project Size** | 100MB | 1GB | 10GB |
| **Max Files per Project** | 500 | 5,000 | 50,000 |
| **Max Project Duration** | 2 hours | 8 hours | 24 hours |
| **Storage Retention** | 7 days | 30 days | 1 year |

### API Key Limits

- **Maximum API keys per account**: 10 (Free), 50 (Pro), Unlimited (Enterprise)
- **Key rotation frequency**: Once per day minimum
- **Scope restrictions**: Available on Pro and Enterprise tiers

## 🚫 Current Limitations

### 1. Project Types

**Supported:**
- Web applications (React, Vue, Angular)
- API services (REST, GraphQL)
- Mobile apps (React Native, Flutter)
- Desktop applications (Electron)
- Data platforms (Python, R)

**Not Yet Supported:**
- Native mobile apps (Swift, Kotlin)
- Game development (Unity, Unreal)
- Embedded systems
- Blockchain applications
- Machine learning models (training)

### 2. Technology Stack Constraints

**Frontend Frameworks:**
- ✅ React, Vue.js, Angular, Svelte
- ❌ Custom frameworks, legacy frameworks

**Backend Technologies:**
- ✅ Node.js, Python (FastAPI, Django), Java (Spring)
- ❌ PHP, Ruby, Go, Rust (coming soon)

**Databases:**
- ✅ PostgreSQL, MySQL, MongoDB, SQLite
- ❌ Oracle, SQL Server, Cassandra

**Cloud Providers:**
- ✅ AWS, Google Cloud, Azure basics
- ❌ Advanced cloud services, multi-cloud setups

### 3. Code Complexity Limits

| Complexity Level | Max Components | Max API Endpoints | Max Database Tables |
|------------------|----------------|-------------------|-------------------|
| **Simple** | 10 | 5 | 3 |
| **Medium** | 50 | 25 | 10 |
| **Complex** | 200 | 100 | 25 |
| **Enterprise** | 1000 | 500 | 100 |

### 4. Language and Localization

**Currently Supported:**
- English (primary)
- Spanish (beta)
- French (beta)

**Limitations:**
- Right-to-left languages not fully supported
- Complex Unicode handling may have issues
- Cultural context understanding is limited

## ⚠️ Known Issues and Workarounds

### 1. Large Project Generation

**Issue:** Projects with >1000 files may timeout or fail.

**Workaround:**
```bash
# Break down into smaller modules
curl -X POST /projects \
  -d '{
    "prompt": "Create user authentication module only",
    "project_name": "AuthModule",
    "project_type": "api",
    "complexity": "simple"
  }'
```

### 2. Real-time Updates

**Issue:** WebSocket connections may drop during long-running projects.

**Workaround:**
```javascript
// Implement reconnection logic
function connectWebSocket() {
  const ws = new WebSocket('ws://localhost:8000/ws');
  
  ws.onclose = () => {
    setTimeout(connectWebSocket, 5000); // Reconnect after 5 seconds
  };
  
  return ws;
}
```

### 3. API Response Times

**Issue:** Complex projects may take 30+ minutes to complete.

**Best Practice:**
```python
import asyncio
from taoforge import TaoForgeClient

async def create_with_timeout():
    client = TaoForgeClient(api_key="your_key")
    
    try:
        # Set reasonable timeout
        project = await asyncio.wait_for(
            client.projects.create_async({
                "prompt": "Complex e-commerce platform",
                "project_type": "fullstack"
            }),
            timeout=3600  # 1 hour timeout
        )
        return project
    except asyncio.TimeoutError:
        print("Project creation timed out")
        return None
```

## 🎯 Best Practices

### 1. Optimal Request Patterns

**✅ Good:**
```python
# Batch operations when possible
projects = []
for config in project_configs:
    project = client.projects.create(config)
    projects.append(project)
    time.sleep(1)  # Respect rate limits
```

**❌ Avoid:**
```python
# Don't spam the API
for i in range(100):
    client.projects.create(config)  # Will hit rate limits
```

### 2. Error Handling

**✅ Comprehensive Error Handling:**
```python
from taoforge import TaoForgeClient, TaoForgeError

client = TaoForgeClient(api_key="your_key")

try:
    project = client.projects.create({
        "prompt": "My application",
        "project_type": "fullstack"
    })
except TaoForgeError as e:
    if e.code == "RATE_LIMITED":
        # Wait and retry
        time.sleep(60)
        project = client.projects.create(config)
    elif e.code == "INVALID_REQUEST":
        # Fix request and retry
        print(f"Invalid request: {e.message}")
    else:
        # Handle other errors
        print(f"Unexpected error: {e}")
```

### 3. Resource Management

**✅ Efficient Resource Usage:**
```python
# Use context managers for automatic cleanup
async with TaoForgeClient(api_key="your_key") as client:
    project = await client.projects.create_async(config)
    
    # Monitor progress efficiently
    async for update in client.projects.stream_updates(project.id):
        if update.status in ["completed", "failed"]:
            break
```

### 4. Caching and Optimization

**✅ Cache Frequently Used Data:**
```python
import functools
import time

@functools.lru_cache(maxsize=100)
def get_project_templates():
    return client.templates.list()

# Cache with TTL
template_cache = {}
cache_ttl = 300  # 5 minutes

def get_cached_templates():
    now = time.time()
    if 'templates' not in template_cache or now - template_cache['timestamp'] > cache_ttl:
        template_cache['templates'] = client.templates.list()
        template_cache['timestamp'] = now
    return template_cache['templates']
```

## 🔧 Performance Optimization

### 1. Request Optimization

**Minimize API Calls:**
```python
# ✅ Good: Single request with all data
project = client.projects.create({
    "prompt": "E-commerce platform",
    "project_type": "fullstack",
    "features": ["auth", "payments", "admin"],
    "tech_preferences": {
        "frontend": "react",
        "backend": "nodejs",
        "database": "postgresql"
    }
})

# ❌ Avoid: Multiple requests for same project
project = client.projects.create(basic_config)
client.projects.add_features(project.id, ["auth", "payments"])
client.projects.set_tech_stack(project.id, tech_stack)
```

### 2. Pagination Best Practices

```python
# ✅ Efficient pagination
def get_all_projects():
    projects = []
    offset = 0
    limit = 50  # Reasonable page size
    
    while True:
        page = client.projects.list(offset=offset, limit=limit)
        projects.extend(page.projects)
        
        if not page.has_more:
            break
            
        offset += limit
        
    return projects
```

### 3. Concurrent Operations

```python
import asyncio
import aiohttp

async def create_multiple_projects(configs):
    async with aiohttp.ClientSession() as session:
        tasks = []
        
        for config in configs:
            task = create_project_async(session, config)
            tasks.append(task)
            
        # Limit concurrency to avoid rate limits
        semaphore = asyncio.Semaphore(5)
        
        async def limited_task(task):
            async with semaphore:
                return await task
                
        results = await asyncio.gather(*[limited_task(task) for task in tasks])
        return results
```

## 🛡️ Security Considerations

### 1. API Key Management

**✅ Secure Practices:**
```python
import os
from taoforge import TaoForgeClient

# Use environment variables
api_key = os.getenv('TAOFORGE_API_KEY')
if not api_key:
    raise ValueError("API key not found in environment")

client = TaoForgeClient(api_key=api_key)
```

**❌ Avoid:**
```python
# Don't hardcode API keys
client = TaoForgeClient(api_key="sk-1234567890abcdef")  # Bad!
```

### 2. Input Validation

**✅ Validate User Input:**
```python
import re

def validate_project_name(name):
    if not name or len(name) < 3 or len(name) > 50:
        raise ValueError("Project name must be 3-50 characters")
    
    if not re.match(r'^[a-zA-Z0-9_-]+$', name):
        raise ValueError("Project name contains invalid characters")
    
    return name

# Use validation before API calls
project_name = validate_project_name(user_input)
project = client.projects.create({
    "project_name": project_name,
    "prompt": sanitize_prompt(user_prompt)
})
```

## 📈 Monitoring and Debugging

### 1. Enable Logging

```python
import logging

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('taoforge')

# Monitor API calls
client = TaoForgeClient(
    api_key="your_key",
    debug=True,
    log_requests=True
)
```

### 2. Track Usage

```python
class UsageTracker:
    def __init__(self):
        self.requests_made = 0
        self.projects_created = 0
        self.errors_encountered = 0
    
    def track_request(self):
        self.requests_made += 1
    
    def track_project(self):
        self.projects_created += 1
    
    def track_error(self):
        self.errors_encountered += 1
    
    def get_stats(self):
        return {
            "requests": self.requests_made,
            "projects": self.projects_created,
            "errors": self.errors_encountered,
            "success_rate": (self.projects_created / self.requests_made) * 100
        }

tracker = UsageTracker()
```

## 🔮 Future Improvements

### Planned Features
- Support for additional programming languages
- Advanced AI model selection
- Custom workflow definitions
- Enhanced real-time collaboration
- Improved error recovery mechanisms

### Timeline
- **Q1 2024**: Go and Rust support
- **Q2 2024**: Advanced cloud integrations
- **Q3 2024**: Custom AI model training
- **Q4 2024**: Enterprise workflow features

---

For the most up-to-date information on limitations and new features, check our [changelog](../CHANGELOG.md) and [roadmap](../ROADMAP.md).
