workflow:
  id: greenfield-fullstack
  name: Greenfield Full-Stack Application Development
  description: >-
    Agent workflow for building full-stack applications from concept to development.
    Supports both comprehensive planning for complex projects and rapid prototyping for simple ones.
  type: greenfield
  project_types:
    - web-app
    - saas
    - enterprise-app
    - prototype
    - mvp

  sequence:
    - agent: analyst
      creates: project-brief.md
      optional_steps:
        - brainstorming_session
        - market_research_prompt
      notes: "Can do brainstorming first, then optional deep research before creating project brief. SAVE OUTPUT: Copy final project-brief.md to your project's docs/ folder."

    - agent: pm
      creates: prd.md
      requires: project-brief.md
      notes: "Creates PRD from project brief using prd-tmpl. SAVE OUTPUT: Copy final prd.md to your project's docs/ folder."

    - agent: ux-expert
      creates: front-end-spec.md
      requires: prd.md
      optional_steps:
        - user_research_prompt
      notes: "Creates UI/UX specification using front-end-spec-tmpl. SAVE OUTPUT: Copy final front-end-spec.md to your project's docs/ folder."

    - agent: ux-expert
      creates: v0_prompt (optional)
      requires: front-end-spec.md
      condition: user_wants_ai_generation
      notes: "OPTIONAL BUT RECOMMENDED: Generate AI UI prompt for tools like v0, Lovable, etc. Use the generate-ai-frontend-prompt task. User can then generate UI in external tool and download project structure."

    - agent: architect
      creates: fullstack-architecture.md
      requires:
        - prd.md
        - front-end-spec.md
      optional_steps:
        - technical_research_prompt
        - review_generated_ui_structure
      notes: "Creates comprehensive architecture using fullstack-architecture-tmpl. If user generated UI with v0/Lovable, can incorporate the project structure into architecture. May suggest changes to PRD stories or new stories. SAVE OUTPUT: Copy final fullstack-architecture.md to your project's docs/ folder."

    - agent: pm
      updates: prd.md (if needed)
      requires: fullstack-architecture.md
      condition: architecture_suggests_prd_changes
      notes: "If architect suggests story changes, update PRD and re-export the complete unredacted prd.md to docs/ folder."

    - agent: po
      validates: all_artifacts
      uses: po-master-checklist
      notes: "Validates all documents for consistency and completeness. May require updates to any document."

    - agent: various
      updates: any_flagged_documents
      condition: po_checklist_issues
      notes: "If PO finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - project_setup_guidance:
      action: guide_project_structure
      condition: user_has_generated_ui
      notes: "If user generated UI with v0/Lovable: For polyrepo setup, place downloaded project in separate frontend repo alongside backend repo. For monorepo, place in apps/web or packages/frontend directory. Review architecture document for specific guidance."

    - development_order_guidance:
      action: guide_development_sequence
      notes: "Based on PRD stories: If stories are frontend-heavy, start with frontend project/directory first. If backend-heavy or API-first, start with backend. For tightly coupled features, follow story sequence in monorepo setup. Reference sharded PRD epics for development order."

    - workflow_end:
      action: move_to_ide
      notes: "All planning artifacts complete. Move to IDE environment to begin development. Explain to the user the IDE Development Workflow next steps: data#bmad-kb:IDE Development Workflow"

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: Greenfield Project] --> B[analyst: project-brief.md]
        B --> C[pm: prd.md]
        C --> D[ux-expert: front-end-spec.md]
        D --> D2{Generate v0 prompt?}
        D2 -->|Yes| D3[ux-expert: create v0 prompt]
        D2 -->|No| E[architect: fullstack-architecture.md]
        D3 --> D4[User: generate UI in v0/Lovable]
        D4 --> E
        E --> F{Architecture suggests PRD changes?}
        F -->|Yes| G[pm: update prd.md]
        F -->|No| H[po: validate all artifacts]
        G --> H
        H --> I{PO finds issues?}
        I -->|Yes| J[Return to relevant agent for fixes]
        I -->|No| K[Move to IDE Environment]
        J --> H

        B -.-> B1[Optional: brainstorming]
        B -.-> B2[Optional: market research]
        D -.-> D1[Optional: user research]
        E -.-> E1[Optional: technical research]

        style K fill:#90EE90
        style D3 fill:#E6E6FA
        style D4 fill:#E6E6FA
        style B fill:#FFE4B5
        style C fill:#FFE4B5
        style D fill:#FFE4B5
        style E fill:#FFE4B5
    ```

  decision_guidance:
    when_to_use:
      - Building production-ready applications
      - Multiple team members will be involved
      - Complex feature requirements
      - Need comprehensive documentation
      - Long-term maintenance expected
      - Enterprise or customer-facing applications

  handoff_prompts:
    analyst_to_pm: "Project brief is complete. Save it as docs/project-brief.md in your project, then create the PRD."
    pm_to_ux: "PRD is ready. Save it as docs/prd.md in your project, then create the UI/UX specification."
    ux_to_architect: "UI/UX spec complete. Save it as docs/front-end-spec.md in your project, then create the fullstack architecture."
    architect_review: "Architecture complete. Save it as docs/fullstack-architecture.md. Do you suggest any changes to the PRD stories or need new stories added?"
    architect_to_pm: "Please update the PRD with the suggested story changes, then re-export the complete prd.md to docs/."
    updated_to_po: "All documents ready in docs/ folder. Please validate all artifacts for consistency."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    complete: "All planning artifacts validated and saved in docs/ folder. Move to IDE environment to begin development."
