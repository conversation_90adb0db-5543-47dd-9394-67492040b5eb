# TaoForge Troubleshooting Guide

This comprehensive guide helps you diagnose and resolve common issues with TaoForge. Use the table of contents to quickly find solutions to specific problems.

## 📋 Table of Contents

- [Quick Diagnostics](#quick-diagnostics)
- [Installation Issues](#installation-issues)
- [API Connection Problems](#api-connection-problems)
- [Project Generation Issues](#project-generation-issues)
- [Performance Problems](#performance-problems)
- [VS Code Extension Issues](#vs-code-extension-issues)
- [Docker-related Issues](#docker-related-issues)
- [Configuration Problems](#configuration-problems)
- [Error Code Reference](#error-code-reference)
- [Getting Support](#getting-support)

## 🔍 Quick Diagnostics

Before diving into specific issues, run these diagnostic commands:

```bash
# Check system status
taoforge status

# Run comprehensive diagnostics
taoforge diagnose

# Check configuration
taoforge config list

# View recent logs
taoforge logs --tail 50

# Test API connectivity
taoforge test-api
```

## 🚨 Installation Issues

### Python Version Compatibility

**Problem:** TaoForge fails to install or run due to Python version issues.

**Solution:**
```bash
# Check Python version
python --version

# TaoForge requires Python 3.9+
# Install correct version using pyenv
curl https://pyenv.run | bash
pyenv install 3.11.0
pyenv global 3.11.0

# Verify installation
python --version
pip --version
```

### Permission Denied Errors

**Problem:** Permission errors during installation on Linux/macOS.

**Solution:**
```bash
# Option 1: Install for current user only
pip install --user taoforge

# Option 2: Use virtual environment (recommended)
python -m venv taoforge-env
source taoforge-env/bin/activate  # Linux/macOS
# or
taoforge-env\Scripts\activate     # Windows
pip install taoforge

# Option 3: Fix pip permissions
python -m pip install --upgrade pip
```

### Package Conflicts

**Problem:** Dependency conflicts during installation.

**Solution:**
```bash
# Create clean virtual environment
python -m venv fresh-env
source fresh-env/bin/activate

# Upgrade pip and setuptools
pip install --upgrade pip setuptools wheel

# Install TaoForge
pip install taoforge

# If conflicts persist, use pip-tools
pip install pip-tools
pip-compile requirements.in
pip-sync requirements.txt
```

## 🌐 API Connection Problems

### OpenAI API Key Issues

**Problem:** "Invalid API key" or "Authentication failed" errors.

**Diagnosis:**
```bash
# Test API key manually
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models

# Check key format (should start with sk-)
echo $OPENAI_API_KEY | head -c 10
```

**Solution:**
```bash
# Set API key correctly
taoforge config set OPENAI_API_KEY sk-your-actual-api-key

# Verify key is set
taoforge config get OPENAI_API_KEY

# Test connection
taoforge test-api
```

### Network Connectivity Issues

**Problem:** Timeouts or connection refused errors.

**Solution:**
```bash
# Check internet connectivity
ping api.openai.com

# Test with curl
curl -I https://api.openai.com/v1/models

# Configure proxy if needed
taoforge config set HTTP_PROXY http://proxy.company.com:8080
taoforge config set HTTPS_PROXY https://proxy.company.com:8080

# Increase timeout
taoforge config set API_TIMEOUT 120
```

### Rate Limiting

**Problem:** "Rate limit exceeded" errors.

**Solution:**
```bash
# Check current usage
taoforge usage

# Configure rate limiting
taoforge config set RATE_LIMIT_DELAY 2
taoforge config set MAX_RETRIES 5

# Use different model tier
taoforge config set DEFAULT_MODEL gpt-3.5-turbo
```

## 🏗️ Project Generation Issues

### Project Creation Fails

**Problem:** Projects fail to generate or create incomplete files.

**Diagnosis:**
```bash
# Check logs for specific errors
taoforge logs --project PROJECT_NAME --level error

# Verify project directory permissions
ls -la ~/taoforge-projects/

# Check disk space
df -h
```

**Solution:**
```bash
# Ensure sufficient disk space (minimum 1GB free)
# Fix directory permissions
chmod 755 ~/taoforge-projects/

# Try with simpler project first
taoforge create "A simple hello world page" --name test --type frontend

# Increase timeout for complex projects
taoforge create "Complex app" --timeout 600
```

### Incomplete File Generation

**Problem:** Some files are missing or incomplete in generated projects.

**Solution:**
```bash
# Enable verbose logging
taoforge config set LOG_LEVEL debug

# Regenerate specific files
taoforge regenerate PROJECT_NAME --files package.json,README.md

# Use different agent model
taoforge config set DEVELOPER_MODEL gpt-4

# Check template integrity
taoforge validate-templates
```

### Code Quality Issues

**Problem:** Generated code has syntax errors or poor quality.

**Solution:**
```bash
# Use higher quality models
taoforge config set DEVELOPER_MODEL gpt-4
taoforge config set ARCHITECT_MODEL gpt-4

# Enable code review
taoforge config set ENABLE_CODE_REVIEW true

# Adjust temperature for more conservative generation
taoforge config set DEVELOPER_TEMPERATURE 0.1

# Use production quality settings
taoforge create "My app" --quality production
```

## ⚡ Performance Problems

### Slow Project Generation

**Problem:** Projects take too long to generate.

**Solution:**
```bash
# Use faster models for development
taoforge config set DEVELOPER_MODEL gpt-3.5-turbo-16k

# Enable parallel processing
taoforge config set PARALLEL_AGENTS true
taoforge config set MAX_WORKERS 4

# Reduce output verbosity
taoforge config set DOCUMENTATION_LEVEL minimal

# Use SSD storage for projects
taoforge config set PROJECTS_DIR /path/to/ssd/projects
```

### High Memory Usage

**Problem:** TaoForge consumes too much memory.

**Solution:**
```bash
# Reduce concurrent projects
taoforge config set MAX_CONCURRENT_PROJECTS 2

# Limit token usage
taoforge config set MAX_TOKENS 4000

# Enable memory optimization
taoforge config set MEMORY_OPTIMIZATION true

# Monitor memory usage
taoforge monitor --memory
```

### CPU Usage Issues

**Problem:** High CPU usage during project generation.

**Solution:**
```bash
# Limit worker processes
taoforge config set MAX_WORKERS 2

# Reduce processing intensity
taoforge config set PROCESSING_INTENSITY low

# Use background processing
taoforge create "My app" --background

# Monitor CPU usage
taoforge monitor --cpu
```

## 🎨 VS Code Extension Issues

### Extension Not Loading

**Problem:** TaoForge extension doesn't appear in VS Code.

**Solution:**
```bash
# Verify extension installation
code --list-extensions | grep taoforge

# Reinstall extension
code --uninstall-extension taoforge.taoforge-vscode
code --install-extension taoforge.taoforge-vscode

# Check VS Code version (requires 1.70+)
code --version

# Reset VS Code settings
code --reset-settings
```

### Connection to Orchestrator Fails

**Problem:** VS Code extension can't connect to TaoForge orchestrator.

**Solution:**
```bash
# Check orchestrator status
taoforge status

# Start orchestrator if not running
taoforge serve

# Configure extension settings in VS Code:
# 1. Open Settings (Ctrl+,)
# 2. Search "TaoForge"
# 3. Set Orchestrator URL: http://localhost:8000

# Test connection from extension
# Command Palette > TaoForge: Test Connection
```

### Command Palette Issues

**Problem:** TaoForge commands don't appear in Command Palette.

**Solution:**
```bash
# Reload VS Code window
# Command Palette > Developer: Reload Window

# Check extension logs
# Help > Toggle Developer Tools > Console

# Reinstall with clean slate
rm -rf ~/.vscode/extensions/taoforge*
code --install-extension taoforge.taoforge-vscode
```

## 🐳 Docker-related Issues

### Docker Container Won't Start

**Problem:** TaoForge Docker container fails to start.

**Diagnosis:**
```bash
# Check Docker status
docker ps -a

# View container logs
docker logs taoforge

# Check Docker daemon
docker info
```

**Solution:**
```bash
# Ensure Docker is running
sudo systemctl start docker  # Linux
# or restart Docker Desktop on Windows/macOS

# Pull latest image
docker pull taoforge/taoforge:latest

# Remove old containers
docker rm -f taoforge

# Start with proper environment
docker run -d \
  --name taoforge \
  -p 8000:8000 \
  -e OPENAI_API_KEY=your_key \
  -v $(pwd)/projects:/app/projects \
  taoforge/taoforge:latest
```

### Port Conflicts

**Problem:** "Port already in use" errors.

**Solution:**
```bash
# Check what's using port 8000
sudo lsof -i :8000
# or
netstat -tulpn | grep 8000

# Kill conflicting process
sudo kill -9 PID_NUMBER

# Use different port
docker run -p 8001:8000 taoforge/taoforge:latest

# Update configuration
taoforge config set ORCHESTRATOR_PORT 8001
```

### Volume Mount Issues

**Problem:** Projects not persisting or permission errors.

**Solution:**
```bash
# Fix volume permissions
sudo chown -R $USER:$USER ./projects

# Use absolute paths
docker run -v /absolute/path/to/projects:/app/projects taoforge/taoforge

# Check SELinux context (Linux)
ls -Z ./projects
sudo setsebool -P container_manage_cgroup true
```

## ⚙️ Configuration Problems

### Configuration File Corruption

**Problem:** Invalid configuration causing startup failures.

**Solution:**
```bash
# Reset configuration to defaults
taoforge reset --config

# Backup and recreate config
cp ~/.taoforge/config.json ~/.taoforge/config.json.backup
taoforge init --force

# Validate configuration
taoforge config validate

# Import specific settings
taoforge config import ~/.taoforge/config.json.backup --selective
```

### Environment Variable Issues

**Problem:** Environment variables not being recognized.

**Solution:**
```bash
# Check current environment
env | grep TAOFORGE
env | grep OPENAI

# Set variables properly
export OPENAI_API_KEY=your_key
echo 'export OPENAI_API_KEY=your_key' >> ~/.bashrc

# Use .env file
echo "OPENAI_API_KEY=your_key" > .env
taoforge config load-env .env

# Verify variables are loaded
taoforge config list
```

## 📊 Error Code Reference

### Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `TF001` | Invalid API key | Check and reset OpenAI API key |
| `TF002` | Network timeout | Check internet connection, increase timeout |
| `TF003` | Insufficient disk space | Free up disk space (minimum 1GB) |
| `TF004` | Permission denied | Fix file/directory permissions |
| `TF005` | Invalid project type | Use supported project type |
| `TF006` | Rate limit exceeded | Wait or upgrade API plan |
| `TF007` | Configuration error | Reset and reconfigure |
| `TF008` | Template not found | Update templates or use default |
| `TF009` | Agent execution failed | Check logs, try different model |
| `TF010` | Port already in use | Change port or kill conflicting process |

### Detailed Error Solutions

#### TF001 - Invalid API Key
```bash
# Verify key format
echo $OPENAI_API_KEY | grep -E '^sk-[a-zA-Z0-9]{48}$'

# Test key validity
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models

# Reset key
taoforge config set OPENAI_API_KEY sk-your-new-key
```

#### TF003 - Insufficient Disk Space
```bash
# Check disk usage
df -h

# Clean up old projects
taoforge cleanup --older-than 30d

# Move projects to larger drive
taoforge config set PROJECTS_DIR /path/to/larger/drive

# Enable compression
taoforge config set COMPRESS_PROJECTS true
```

#### TF009 - Agent Execution Failed
```bash
# Check agent logs
taoforge logs --agent developer --level error

# Try different model
taoforge config set DEVELOPER_MODEL gpt-3.5-turbo

# Reduce complexity
taoforge create "Simple app" --complexity low

# Reset agent state
taoforge reset --agents
```

## 🆘 Getting Support

### Self-Help Resources

1. **Documentation**: Check our [comprehensive docs](../README.md)
2. **FAQ**: Review [frequently asked questions](faq.md)
3. **Community**: Join our [Discord community](https://discord.gg/taoforge)
4. **GitHub Issues**: Search [existing issues](https://github.com/taoforge/taoforge/issues)

### Reporting Issues

When reporting issues, include:

```bash
# Generate diagnostic report
taoforge diagnose --full > diagnostic-report.txt

# Include system information
taoforge --version
python --version
node --version
docker --version

# Provide relevant logs
taoforge logs --tail 100 > recent-logs.txt
```

### Emergency Recovery

If TaoForge is completely broken:

```bash
# Complete reset (WARNING: This removes all configuration)
rm -rf ~/.taoforge
pip uninstall taoforge
pip install taoforge
taoforge init

# Restore from backup if available
taoforge restore --backup ~/.taoforge.backup
```

### Performance Monitoring

```bash
# Monitor system resources
taoforge monitor --real-time

# Generate performance report
taoforge benchmark --report performance-report.json

# Profile specific operations
taoforge profile create "Test project" --output profile.json
```

---

**Need immediate help?** Join our [Discord community](https://discord.gg/taoforge) or create an issue on [GitHub](https://github.com/taoforge/taoforge/issues).
