import * as vscode from 'vscode';
import { spawn, spawnSync, ChildProcess } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import axios from 'axios';

// Main orchestrator for Aetherforge
export async function startAetherforge(context: vscode.ExtensionContext) {
  // Create webview panel for Aetherforge UI
  const panel = vscode.window.createWebviewPanel(
    'aetherforgePanel',
    'Aetherforge',
    vscode.ViewColumn.One,
    {
      enableScripts: true,
      retainContextWhenHidden: true
    }
  );

  // Set up the UI
  panel.webview.html = getWebviewContent();

  // Set up enhanced message handling
  setupWebviewMessageHandling(panel, context);

  // Start real-time monitoring
  startRealTimeMonitoring(panel, context);

  // Setup file system watchers
  setupFileSystemWatchers(context);

  // Show welcome message
  vscode.window.showInformationMessage('Aetherforge is ready! Use the panel to create autonomous software projects.');
}

// Initialize all Aetherforge components
async function initializeComponents(context: vscode.ExtensionContext) {
  const extensionPath = context.extensionPath;
  const componentsPath = path.join(extensionPath, 'components');
  
  // Ensure components directory exists
  if (!fs.existsSync(componentsPath)) {
    fs.mkdirSync(componentsPath, { recursive: true });
  }
  
  // Initialize Docker containers for each component
  await initializeArchon(componentsPath);
  await initializeBMAD(componentsPath);
  await initializePheromind(componentsPath);
  await initializeMCPCrawl(componentsPath);
}

// Initialize Archon component
async function initializeArchon(componentsPath: string): Promise<boolean> {
  try {
    vscode.window.showInformationMessage('Initializing Archon component...');

    const archonPath = path.join(componentsPath, 'Archon');

    // Check if Archon directory exists, if not copy from the existing one
    if (!fs.existsSync(archonPath)) {
      const sourceArchonPath = path.join(vscode.workspace.workspaceFolders![0].uri.fsPath, 'Archon-main', 'Archon-main');
      if (fs.existsSync(sourceArchonPath)) {
        await copyDirectory(sourceArchonPath, archonPath);
        vscode.window.showInformationMessage('Archon component copied successfully');
      } else {
        vscode.window.showErrorMessage('Archon source not found. Please ensure Archon-main directory exists.');
        return false;
      }
    }

    // Check if Docker is available
    if (!await isDockerAvailable()) {
      vscode.window.showWarningMessage('Docker not available. Archon will run in local mode.');
      return await startArchonLocal(archonPath);
    }

    // Start Archon Docker container
    return await startArchonDocker(archonPath);

  } catch (error) {
    vscode.window.showErrorMessage(`Failed to initialize Archon: ${error}`);
    return false;
  }
}

// Initialize BMAD-METHOD component
async function initializeBMAD(componentsPath: string): Promise<boolean> {
  try {
    vscode.window.showInformationMessage('Initializing BMAD-METHOD component...');

    const bmadPath = path.join(componentsPath, 'BMAD-METHOD');

    // Check if BMAD directory exists, if not copy from the existing one
    if (!fs.existsSync(bmadPath)) {
      const sourceBmadPath = path.join(vscode.workspace.workspaceFolders![0].uri.fsPath, 'BMAD-METHOD-main', 'BMAD-METHOD-main');
      if (fs.existsSync(sourceBmadPath)) {
        await copyDirectory(sourceBmadPath, bmadPath);
        vscode.window.showInformationMessage('BMAD-METHOD component copied successfully');
      } else {
        vscode.window.showErrorMessage('BMAD-METHOD source not found. Please ensure BMAD-METHOD-main directory exists.');
        return false;
      }
    }

    // Install BMAD dependencies
    const installResult = await installBMADDependencies(bmadPath);
    if (!installResult) {
      vscode.window.showWarningMessage('Failed to install BMAD dependencies, but continuing...');
    }

    // BMAD-METHOD doesn't require a separate service, it's integrated into the workflow
    vscode.window.showInformationMessage('BMAD-METHOD component initialized successfully');
    return true;

  } catch (error) {
    vscode.window.showErrorMessage(`Failed to initialize BMAD-METHOD: ${error}`);
    return false;
  }
}

// Initialize Pheromind component
async function initializePheromind(componentsPath: string): Promise<boolean> {
  try {
    vscode.window.showInformationMessage('Initializing Pheromind component...');

    const pheromindPath = path.join(componentsPath, 'Pheromind');

    // Check if Pheromind directory exists, if not copy from the existing one
    if (!fs.existsSync(pheromindPath)) {
      const sourcePheromindPath = path.join(vscode.workspace.workspaceFolders![0].uri.fsPath, 'Pheromind-main', 'Pheromind-main');
      if (fs.existsSync(sourcePheromindPath)) {
        await copyDirectory(sourcePheromindPath, pheromindPath);
        vscode.window.showInformationMessage('Pheromind component copied successfully');
      } else {
        vscode.window.showErrorMessage('Pheromind source not found. Please ensure Pheromind-main directory exists.');
        return false;
      }
    }

    // Check if Docker is available for the visualizer backend
    if (await isDockerAvailable()) {
      return await startPheromindDocker(pheromindPath);
    } else {
      // Start the visualizer backend locally if Node.js is available
      return await startPheromindLocal(pheromindPath);
    }

  } catch (error) {
    vscode.window.showErrorMessage(`Failed to initialize Pheromind: ${error}`);
    return false;
  }
}

// Initialize MCP-Crawl4AI-RAG component
async function initializeMCPCrawl(componentsPath: string): Promise<boolean> {
  try {
    vscode.window.showInformationMessage('Initializing MCP-Crawl4AI-RAG component...');

    const mcpPath = path.join(componentsPath, 'mcp-crawl4ai-rag');

    // Check if MCP directory exists, if not copy from the existing one
    if (!fs.existsSync(mcpPath)) {
      const sourceMcpPath = path.join(vscode.workspace.workspaceFolders![0].uri.fsPath, 'mcp-crawl4ai-rag-main', 'mcp-crawl4ai-rag-main');
      if (fs.existsSync(sourceMcpPath)) {
        await copyDirectory(sourceMcpPath, mcpPath);
        vscode.window.showInformationMessage('MCP-Crawl4AI-RAG component copied successfully');
      } else {
        vscode.window.showErrorMessage('MCP-Crawl4AI-RAG source not found. Please ensure mcp-crawl4ai-rag-main directory exists.');
        return false;
      }
    }

    // Check if Docker is available
    if (await isDockerAvailable()) {
      return await startMCPDocker(mcpPath);
    } else {
      // Try to start locally with Python
      return await startMCPLocal(mcpPath);
    }

  } catch (error) {
    vscode.window.showErrorMessage(`Failed to initialize MCP-Crawl4AI-RAG: ${error}`);
    return false;
  }
}

// Handle messages from the webview
function handleWebviewMessage(message: any, webview: vscode.Webview) {
  switch (message.command) {
    case 'createProject':
      createProject(message.prompt, webview);
      break;
  }
}

// Create a new project based on user prompt
async function createProject(
  prompt: string,
  webview: vscode.Webview,
  projectName?: string,
  projectType?: string,
  workflow?: string
) {
  try {
    webview.postMessage({
      command: 'updateStatus',
      message: 'Starting project creation...'
    });

    // Call the orchestrator API to create the project
    const orchestratorUrl = vscode.workspace.getConfiguration('aetherforge').get('orchestratorUrl') || 'http://localhost:8000';

    const requestData: any = {
      prompt: prompt,
      project_type: projectType || 'fullstack'
    };

    if (projectName) {
      requestData.project_name = projectName;
    }

    if (workflow) {
      requestData.workflow = workflow;
    }

    try {
      const response = await axios.post(`${orchestratorUrl}/projects`, requestData, {
        timeout: 300000 // 5 minutes timeout for project creation
      });

      webview.postMessage({
        command: 'projectCreated',
        data: response.data
      });

      vscode.window.showInformationMessage(`Project "${response.data.project_slug}" created successfully!`);

      // Optionally open the project folder
      const openProject = await vscode.window.showInformationMessage(
        'Would you like to open the generated project?',
        'Yes', 'No'
      );

      if (openProject === 'Yes') {
        const projectPath = response.data.project_path || path.join('projects', response.data.project_slug);
        const projectUri = vscode.Uri.file(projectPath);
        await vscode.commands.executeCommand('vscode.openFolder', projectUri, true);
      }

    } catch (apiError: any) {
      if (apiError.code === 'ECONNREFUSED') {
        webview.postMessage({
          command: 'error',
          message: 'Orchestrator service not running. Please start it first or check the URL in settings.'
        });
        vscode.window.showErrorMessage('Aetherforge orchestrator is not running. Please start the service first.');
      } else if (apiError.code === 'ENOTFOUND') {
        webview.postMessage({
          command: 'error',
          message: 'Cannot reach orchestrator. Please check the URL in settings.'
        });
        vscode.window.showErrorMessage('Cannot reach Aetherforge orchestrator. Please check the URL in settings.');
      } else {
        webview.postMessage({
          command: 'error',
          message: `API Error: ${apiError.message}`
        });
        vscode.window.showErrorMessage(`Failed to create project: ${apiError.message}`);
      }
    }

  } catch (error) {
    webview.postMessage({
      command: 'error',
      message: `Error: ${error}`
    });
    vscode.window.showErrorMessage(`Failed to create project: ${error}`);
  }
}

// Helper function to copy directories recursively
async function copyDirectory(source: string, destination: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const copyRecursive = (src: string, dest: string) => {
      if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
      }

      const items = fs.readdirSync(src);

      for (const item of items) {
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);

        if (fs.statSync(srcPath).isDirectory()) {
          copyRecursive(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
      }
    };

    try {
      copyRecursive(source, destination);
      resolve();
    } catch (error) {
      reject(error);
    }
  });
}

// Helper function to check if Docker is available
async function isDockerAvailable(): Promise<boolean> {
  return new Promise((resolve) => {
    const docker = spawnSync('docker', ['--version'], { stdio: 'pipe' });
    resolve(docker.status === 0);
  });
}

// Helper function to check if Node.js is available
async function isNodeAvailable(): Promise<boolean> {
  return new Promise((resolve) => {
    const node = spawnSync('node', ['--version'], { stdio: 'pipe' });
    resolve(node.status === 0);
  });
}

// Helper function to check if Python is available
async function isPythonAvailable(): Promise<boolean> {
  return new Promise((resolve) => {
    const python = spawnSync('python', ['--version'], { stdio: 'pipe' });
    if (python.status === 0) {
      resolve(true);
    } else {
      const python3 = spawnSync('python3', ['--version'], { stdio: 'pipe' });
      resolve(python3.status === 0);
    }
  });
}

// Start Archon with Docker
async function startArchonDocker(archonPath: string): Promise<boolean> {
  return new Promise((resolve) => {
    const dockerCompose = spawn('docker-compose', ['-f', path.join(archonPath, 'docker-compose.yml'), 'up', '-d'], {
      cwd: archonPath,
      stdio: 'pipe'
    });

    dockerCompose.on('close', (code) => {
      if (code === 0) {
        vscode.window.showInformationMessage('Archon Docker container started successfully');
        resolve(true);
      } else {
        vscode.window.showErrorMessage('Failed to start Archon Docker container');
        resolve(false);
      }
    });
  });
}

// Start Archon locally
async function startArchonLocal(archonPath: string): Promise<boolean> {
  if (!await isPythonAvailable()) {
    vscode.window.showErrorMessage('Python not available. Cannot start Archon locally.');
    return false;
  }

  // Install dependencies if requirements.txt exists
  const requirementsPath = path.join(archonPath, 'requirements.txt');
  if (fs.existsSync(requirementsPath)) {
    const pip = spawnSync('pip', ['install', '-r', requirementsPath], { cwd: archonPath });
    if (pip.status !== 0) {
      vscode.window.showWarningMessage('Failed to install Archon dependencies');
    }
  }

  // Start Streamlit UI
  const streamlit = spawn('streamlit', ['run', 'streamlit_ui.py', '--server.port=8501'], {
    cwd: archonPath,
    detached: true,
    stdio: 'ignore'
  });

  streamlit.unref();
  vscode.window.showInformationMessage('Archon started locally on port 8501');
  return true;
}

// Install BMAD dependencies
async function installBMADDependencies(bmadPath: string): Promise<boolean> {
  if (!await isNodeAvailable()) {
    return false;
  }

  return new Promise((resolve) => {
    const npm = spawn('npm', ['install'], {
      cwd: bmadPath,
      stdio: 'pipe'
    });

    npm.on('close', (code) => {
      resolve(code === 0);
    });
  });
}

// Start Pheromind with Docker
async function startPheromindDocker(pheromindPath: string): Promise<boolean> {
  // Pheromind visualizer backend
  const backendPath = path.join(pheromindPath, 'tools', 'visualizer', 'backend');

  if (!fs.existsSync(backendPath)) {
    vscode.window.showWarningMessage('Pheromind visualizer backend not found');
    return false;
  }

  return new Promise((resolve) => {
    const docker = spawn('docker', ['build', '-t', 'pheromind-backend', '.'], {
      cwd: backendPath,
      stdio: 'pipe'
    });

    docker.on('close', (code) => {
      if (code === 0) {
        const run = spawn('docker', ['run', '-d', '-p', '8502:3001', 'pheromind-backend'], {
          stdio: 'pipe'
        });

        run.on('close', (runCode) => {
          if (runCode === 0) {
            vscode.window.showInformationMessage('Pheromind backend started on port 8502');
            resolve(true);
          } else {
            resolve(false);
          }
        });
      } else {
        resolve(false);
      }
    });
  });
}

// Start Pheromind locally
async function startPheromindLocal(pheromindPath: string): Promise<boolean> {
  const backendPath = path.join(pheromindPath, 'tools', 'visualizer', 'backend');

  if (!fs.existsSync(backendPath) || !await isNodeAvailable()) {
    vscode.window.showWarningMessage('Cannot start Pheromind locally - Node.js or backend not available');
    return false;
  }

  // Install dependencies
  const npm = spawnSync('npm', ['install'], { cwd: backendPath });
  if (npm.status !== 0) {
    vscode.window.showWarningMessage('Failed to install Pheromind dependencies');
  }

  // Start the backend
  const backend = spawn('npm', ['start'], {
    cwd: backendPath,
    detached: true,
    stdio: 'ignore'
  });

  backend.unref();
  vscode.window.showInformationMessage('Pheromind backend started locally on port 3001');
  return true;
}

// Start MCP with Docker
async function startMCPDocker(mcpPath: string): Promise<boolean> {
  return new Promise((resolve) => {
    const docker = spawn('docker', ['build', '-t', 'mcp-crawl4ai', '.'], {
      cwd: mcpPath,
      stdio: 'pipe'
    });

    docker.on('close', (code) => {
      if (code === 0) {
        const run = spawn('docker', ['run', '-d', '-p', '8051:8051', 'mcp-crawl4ai'], {
          stdio: 'pipe'
        });

        run.on('close', (runCode) => {
          if (runCode === 0) {
            vscode.window.showInformationMessage('MCP-Crawl4AI started on port 8051');
            resolve(true);
          } else {
            resolve(false);
          }
        });
      } else {
        resolve(false);
      }
    });
  });
}

// Start MCP locally
async function startMCPLocal(mcpPath: string): Promise<boolean> {
  if (!await isPythonAvailable()) {
    vscode.window.showErrorMessage('Python not available. Cannot start MCP-Crawl4AI locally.');
    return false;
  }

  // Install dependencies if requirements.txt exists
  const requirementsPath = path.join(mcpPath, 'requirements.txt');
  if (fs.existsSync(requirementsPath)) {
    const pip = spawnSync('pip', ['install', '-r', requirementsPath], { cwd: mcpPath });
    if (pip.status !== 0) {
      vscode.window.showWarningMessage('Failed to install MCP-Crawl4AI dependencies');
    }
  }

  // Start the MCP server
  const mcpServer = spawn('python', ['src/crawl4ai_mcp.py'], {
    cwd: mcpPath,
    detached: true,
    stdio: 'ignore',
    env: { ...process.env, PORT: '8051' }
  });

  mcpServer.unref();
  vscode.window.showInformationMessage('MCP-Crawl4AI started locally on port 8051');
  return true;
}

// Generate webview HTML content
function getWebviewContent() {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aetherforge</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            padding: 20px;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        h1 {
            color: var(--vscode-textLink-foreground);
            border-bottom: 2px solid var(--vscode-textSeparator-foreground);
            padding-bottom: 15px;
            margin-bottom: 10px;
        }
        .subtitle {
            color: var(--vscode-descriptionForeground);
            font-size: 14px;
            margin-bottom: 20px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--vscode-textSeparator-foreground);
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: var(--vscode-descriptionForeground);
        }
        .tab.active {
            color: var(--vscode-textLink-foreground);
            border-bottom-color: var(--vscode-textLink-foreground);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .input-group {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: var(--vscode-foreground);
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-family: var(--vscode-font-family);
            box-sizing: border-box;
        }
        textarea {
            height: 120px;
            resize: vertical;
            font-family: var(--vscode-editor-font-family);
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 8px 8px 8px 0;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .primary-button {
            background-color: var(--vscode-textLink-foreground);
            color: var(--vscode-editor-background);
        }
        .secondary-button {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
            background-color: var(--vscode-textCodeBlock-background);
            border-left: 4px solid var(--vscode-textLink-foreground);
        }
        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border-left-color: var(--vscode-errorForeground);
        }
        .success {
            color: var(--vscode-terminal-ansiGreen);
            border-left-color: var(--vscode-terminal-ansiGreen);
        }
        .warning {
            color: var(--vscode-terminal-ansiYellow);
            border-left-color: var(--vscode-terminal-ansiYellow);
        }
        .component-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .component-card {
            padding: 15px;
            border: 1px solid var(--vscode-textSeparator-foreground);
            border-radius: 4px;
            background-color: var(--vscode-textCodeBlock-background);
        }
        .component-name {
            font-weight: bold;
            margin-bottom: 8px;
        }
        .component-status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: var(--vscode-terminal-ansiGreen); }
        .status-offline { background-color: var(--vscode-errorForeground); }
        .status-unknown { background-color: var(--vscode-terminal-ansiYellow); }
        .project-list {
            margin: 20px 0;
        }
        .project-item {
            padding: 12px;
            border: 1px solid var(--vscode-textSeparator-foreground);
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: var(--vscode-textCodeBlock-background);
        }
        .project-name {
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        .project-path {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-top: 4px;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--vscode-textSeparator-foreground);
            border-radius: 50%;
            border-top-color: var(--vscode-textLink-foreground);
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .advanced-options {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 Aetherforge</h1>
            <div class="subtitle">Autonomous AI Software Creation System</div>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="switchTab('create')">Create Project</div>
            <div class="tab" onclick="switchTab('status')">System Status</div>
            <div class="tab" onclick="switchTab('projects')">Projects</div>
            <div class="tab" onclick="switchTab('settings')">Settings</div>
        </div>

        <!-- Create Project Tab -->
        <div id="create-tab" class="tab-content active">
            <div class="input-group">
                <label for="prompt">Project Description:</label>
                <textarea id="prompt" placeholder="Describe the software project you want to create in detail. Include features, technologies, and any specific requirements..."></textarea>
            </div>

            <div class="input-group">
                <label for="projectName">Project Name (optional):</label>
                <input type="text" id="projectName" placeholder="MyAwesomeProject">
            </div>

            <div class="input-group">
                <label for="projectType">Project Type:</label>
                <select id="projectType">
                    <option value="fullstack">Full Stack Web Application</option>
                    <option value="frontend">Frontend Application</option>
                    <option value="backend">Backend API Service</option>
                    <option value="mobile">Mobile Application</option>
                    <option value="desktop">Desktop Application</option>
                    <option value="game">Game</option>
                    <option value="api">REST API</option>
                </select>
            </div>

            <div class="advanced-options">
                <h3>Advanced Options</h3>
                <div class="input-group">
                    <label for="workflow">Workflow:</label>
                    <select id="workflow">
                        <option value="">Auto-select (recommended)</option>
                        <option value="greenfield-fullstack">Greenfield Full Stack</option>
                        <option value="greenfield-frontend">Greenfield Frontend</option>
                        <option value="greenfield-service">Greenfield Service</option>
                        <option value="greenfield-mobile">Greenfield Mobile</option>
                    </select>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="enableMCP" checked>
                    <label for="enableMCP">Enable web research and documentation crawling</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="enablePheromind" checked>
                    <label for="enablePheromind">Enable advanced agent coordination</label>
                </div>
            </div>

            <button class="primary-button" onclick="createProject()">
                <span id="create-btn-text">Create Project</span>
                <span id="create-btn-loading" class="loading" style="display: none;"></span>
            </button>
            <button class="secondary-button" onclick="clearForm()">Clear Form</button>
        </div>

        <!-- System Status Tab -->
        <div id="status-tab" class="tab-content">
            <h2>System Status</h2>
            <button onclick="checkSystemStatus()">Refresh Status</button>
            <button onclick="initializeComponents()">Initialize Components</button>

            <div id="component-status" class="component-status">
                <!-- Component status will be populated here -->
            </div>
        </div>

        <!-- Projects Tab -->
        <div id="projects-tab" class="tab-content">
            <h2>Generated Projects</h2>
            <button onclick="refreshProjects()">Refresh Projects</button>
            <button onclick="openProjectsFolder()">Open Projects Folder</button>

            <div id="project-list" class="project-list">
                <!-- Project list will be populated here -->
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings-tab" class="tab-content">
            <h2>Settings</h2>
            <div class="input-group">
                <label for="orchestratorUrl">Orchestrator URL:</label>
                <input type="text" id="orchestratorUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
            </div>
            <div class="input-group">
                <label for="projectsPath">Projects Directory:</label>
                <input type="text" id="projectsPath" value="./projects" placeholder="./projects">
            </div>
            <button onclick="saveSettings()">Save Settings</button>
            <button onclick="testConnection()">Test Connection</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentTab = 'create';

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';

            // Auto-hide after 5 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to selected tab
            event.target.classList.add('active');

            currentTab = tabName;

            // Load data for specific tabs
            if (tabName === 'status') {
                checkSystemStatus();
            } else if (tabName === 'projects') {
                refreshProjects();
            }
        }

        function initializeComponents() {
            showStatus('Initializing Aetherforge components...', 'info');
            vscode.postMessage({
                command: 'initializeComponents'
            });
        }

        function createProject() {
            const prompt = document.getElementById('prompt').value;
            const projectName = document.getElementById('projectName').value;
            const projectType = document.getElementById('projectType').value;
            const workflow = document.getElementById('workflow').value;

            if (!prompt.trim()) {
                showStatus('Please enter a project description', 'error');
                return;
            }

            // Show loading state
            const btnText = document.getElementById('create-btn-text');
            const btnLoading = document.getElementById('create-btn-loading');
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline-block';

            showStatus('Creating project... This may take a few minutes.', 'info');
            vscode.postMessage({
                command: 'createProject',
                prompt: prompt,
                projectName: projectName,
                projectType: projectType,
                workflow: workflow || undefined
            });
        }

        function clearForm() {
            document.getElementById('prompt').value = '';
            document.getElementById('projectName').value = '';
            document.getElementById('projectType').value = 'fullstack';
            document.getElementById('workflow').value = '';
        }

        function checkSystemStatus() {
            showStatus('Checking system status...', 'info');
            vscode.postMessage({
                command: 'checkSystemStatus'
            });
        }

        function refreshProjects() {
            showStatus('Loading projects...', 'info');
            vscode.postMessage({
                command: 'refreshProjects'
            });
        }

        function openProjectsFolder() {
            vscode.postMessage({
                command: 'openProjectsFolder'
            });
        }

        function saveSettings() {
            const settings = {
                orchestratorUrl: document.getElementById('orchestratorUrl').value,
                projectsPath: document.getElementById('projectsPath').value
            };

            vscode.postMessage({
                command: 'saveSettings',
                settings: settings
            });

            showStatus('Settings saved', 'success');
        }

        function testConnection() {
            const url = document.getElementById('orchestratorUrl').value;
            showStatus('Testing connection...', 'info');
            vscode.postMessage({
                command: 'testConnection',
                url: url
            });
        }

        function resetCreateButton() {
            const btnText = document.getElementById('create-btn-text');
            const btnLoading = document.getElementById('create-btn-loading');
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
        }

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'updateStatus':
                    showStatus(message.message, 'info');
                    break;
                case 'error':
                    showStatus(message.message, 'error');
                    resetCreateButton();
                    break;
                case 'projectCreated':
                    showStatus('Project created successfully! Check the projects folder.', 'success');
                    resetCreateButton();
                    clearForm();
                    if (currentTab === 'projects') {
                        refreshProjects();
                    }
                    break;
                case 'systemStatus':
                    updateSystemStatus(message.data);
                    break;
                case 'projectsList':
                    updateProjectsList(message.data);
                    break;
                case 'connectionTest':
                    if (message.success) {
                        showStatus('Connection successful!', 'success');
                    } else {
                        showStatus('Connection failed: ' + message.error, 'error');
                    }
                    break;
            }
        });

        function updateSystemStatus(status) {
            const container = document.getElementById('component-status');
            container.innerHTML = '';

            Object.entries(status.components || {}).forEach(([name, componentStatus]) => {
                const card = document.createElement('div');
                card.className = 'component-card';

                const statusClass = componentStatus === 'running' ? 'status-online' :
                                  componentStatus === 'offline' ? 'status-offline' : 'status-unknown';

                card.innerHTML = \`
                    <div class="component-name">
                        <span class="component-status-indicator \${statusClass}"></span>
                        \${name.charAt(0).toUpperCase() + name.slice(1)}
                    </div>
                    <div>Status: \${componentStatus}</div>
                \`;

                container.appendChild(card);
            });
        }

        function updateProjectsList(projects) {
            const container = document.getElementById('project-list');
            container.innerHTML = '';

            if (projects.length === 0) {
                container.innerHTML = '<div class="project-item">No projects found. Create your first project!</div>';
                return;
            }

            projects.forEach(project => {
                const item = document.createElement('div');
                item.className = 'project-item';
                item.innerHTML = \`
                    <div class="project-name">\${project.name}</div>
                    <div class="project-path">\${project.path}</div>
                    <div style="margin-top: 8px;">
                        <button onclick="openProject('\${project.path}')">Open</button>
                        <button onclick="viewProject('\${project.name}')">View Details</button>
                    </div>
                \`;
                container.appendChild(item);
            });
        }

        function openProject(path) {
            vscode.postMessage({
                command: 'openProject',
                path: path
            });
        }

        function viewProject(name) {
            vscode.postMessage({
                command: 'viewProject',
                name: name
            });
        }

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', () => {
            // Load saved settings
            const savedSettings = vscode.getState();
            if (savedSettings) {
                if (savedSettings.orchestratorUrl) {
                    document.getElementById('orchestratorUrl').value = savedSettings.orchestratorUrl;
                }
                if (savedSettings.projectsPath) {
                    document.getElementById('projectsPath').value = savedSettings.projectsPath;
                }
            }
        });
    </script>
</body>
</html>`;
}

// Enhanced message handling for the webview
function setupWebviewMessageHandling(panel: vscode.WebviewPanel, context: vscode.ExtensionContext) {
  panel.webview.onDidReceiveMessage(
    async message => {
      switch (message.command) {
        case 'createProject':
          await createProject(message.prompt, panel.webview, message.projectName, message.projectType, message.workflow);
          return;
        case 'initializeComponents':
          await initializeAllComponents(panel.webview);
          return;
        case 'checkSystemStatus':
          await checkSystemStatus(panel.webview);
          return;
        case 'refreshProjects':
          await refreshProjects(panel.webview);
          return;
        case 'openProjectsFolder':
          await openProjectsFolder();
          return;
        case 'saveSettings':
          await saveSettings(message.settings, panel.webview);
          return;
        case 'testConnection':
          await testConnection(message.url, panel.webview);
          return;
        case 'openProject':
          await openProject(message.path);
          return;
        case 'viewProject':
          await viewProject(message.name, panel.webview);
          return;
      }
    },
    undefined,
    context.subscriptions
  );
}

// Initialize all components
async function initializeAllComponents(webview: vscode.Webview) {
  try {
    webview.postMessage({
      command: 'updateStatus',
      message: 'Initializing all Aetherforge components...'
    });

    const componentsPath = path.join(os.homedir(), '.aetherforge', 'components');

    // Initialize each component
    const results = await Promise.allSettled([
      initializeArchon(componentsPath),
      initializeBMAD(componentsPath),
      initializePheromind(componentsPath),
      initializeMCPCrawl(componentsPath)
    ]);

    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
    const total = results.length;

    if (successful === total) {
      webview.postMessage({
        command: 'updateStatus',
        message: `All ${total} components initialized successfully!`
      });
      vscode.window.showInformationMessage('Aetherforge components initialized successfully!');
    } else {
      webview.postMessage({
        command: 'updateStatus',
        message: `${successful}/${total} components initialized. Check logs for details.`
      });
      vscode.window.showWarningMessage(`${successful}/${total} components initialized. Some components may not be available.`);
    }

  } catch (error) {
    webview.postMessage({
      command: 'error',
      message: `Failed to initialize components: ${error}`
    });
    vscode.window.showErrorMessage(`Failed to initialize components: ${error}`);
  }
}

// Check system status
async function checkSystemStatus(webview: vscode.Webview) {
  try {
    const orchestratorUrl = 'http://localhost:8000';

    try {
      const response = await axios.get(`${orchestratorUrl}/components/status`);
      webview.postMessage({
        command: 'systemStatus',
        data: response.data
      });
    } catch (apiError: any) {
      // If orchestrator is not running, check individual components
      const componentStatus = {
        components: {
          orchestrator: 'offline',
          archon: 'unknown',
          'mcp-crawl4ai': 'unknown',
          pheromind: 'unknown',
          bmad: 'unknown'
        }
      };

      webview.postMessage({
        command: 'systemStatus',
        data: componentStatus
      });
    }

  } catch (error) {
    webview.postMessage({
      command: 'error',
      message: `Failed to check system status: ${error}`
    });
  }
}

// Refresh projects list
async function refreshProjects(webview: vscode.Webview) {
  try {
    const orchestratorUrl = 'http://localhost:8000';

    try {
      const response = await axios.get(`${orchestratorUrl}/projects`);
      webview.postMessage({
        command: 'projectsList',
        data: response.data
      });
    } catch (apiError: any) {
      // Fallback to local projects directory
      const projectsPath = path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', 'projects');

      if (fs.existsSync(projectsPath)) {
        const projects = fs.readdirSync(projectsPath)
          .filter(name => fs.statSync(path.join(projectsPath, name)).isDirectory())
          .map(name => ({
            name: name,
            path: path.join(projectsPath, name),
            created: new Date().toISOString()
          }));

        webview.postMessage({
          command: 'projectsList',
          data: projects
        });
      } else {
        webview.postMessage({
          command: 'projectsList',
          data: []
        });
      }
    }

  } catch (error) {
    webview.postMessage({
      command: 'error',
      message: `Failed to refresh projects: ${error}`
    });
  }
}

// Open projects folder
async function openProjectsFolder() {
  try {
    const projectsPath = path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', 'projects');

    if (!fs.existsSync(projectsPath)) {
      fs.mkdirSync(projectsPath, { recursive: true });
    }

    const projectsUri = vscode.Uri.file(projectsPath);
    await vscode.commands.executeCommand('vscode.openFolder', projectsUri, true);

  } catch (error) {
    vscode.window.showErrorMessage(`Failed to open projects folder: ${error}`);
  }
}

// Save settings
async function saveSettings(settings: any, webview: vscode.Webview) {
  try {
    // Save settings to VS Code workspace state
    const workspaceState = vscode.workspace.getConfiguration('aetherforge');
    await workspaceState.update('orchestratorUrl', settings.orchestratorUrl, vscode.ConfigurationTarget.Workspace);
    await workspaceState.update('projectsPath', settings.projectsPath, vscode.ConfigurationTarget.Workspace);

    webview.postMessage({
      command: 'updateStatus',
      message: 'Settings saved successfully!'
    });

  } catch (error) {
    webview.postMessage({
      command: 'error',
      message: `Failed to save settings: ${error}`
    });
  }
}

// Test connection to orchestrator
async function testConnection(url: string, webview: vscode.Webview) {
  try {
    const response = await axios.get(`${url}/health`, { timeout: 5000 });

    webview.postMessage({
      command: 'connectionTest',
      success: true,
      data: response.data
    });

  } catch (error: any) {
    webview.postMessage({
      command: 'connectionTest',
      success: false,
      error: error.message
    });
  }
}

// Open a specific project
async function openProject(projectPath: string) {
  try {
    const projectUri = vscode.Uri.file(projectPath);
    await vscode.commands.executeCommand('vscode.openFolder', projectUri, true);

  } catch (error) {
    vscode.window.showErrorMessage(`Failed to open project: ${error}`);
  }
}

// View project details
async function viewProject(projectName: string, webview: vscode.Webview) {
  try {
    const orchestratorUrl = 'http://localhost:8000';

    // Try to get project details from orchestrator
    try {
      const response = await axios.get(`${orchestratorUrl}/projects`);
      const project = response.data.find((p: any) => p.name === projectName);

      if (project) {
        webview.postMessage({
          command: 'updateStatus',
          message: `Project: ${project.name} - Created: ${project.created}`
        });
      } else {
        webview.postMessage({
          command: 'updateStatus',
          message: `Project ${projectName} not found in orchestrator`
        });
      }

    } catch (apiError) {
      webview.postMessage({
        command: 'updateStatus',
        message: `Project ${projectName} - Local project (orchestrator offline)`
      });
    }

  } catch (error) {
    webview.postMessage({
      command: 'error',
      message: `Failed to view project details: ${error}`
    });
  }
}

// Start real-time monitoring of project generation
function startRealTimeMonitoring(panel: vscode.WebviewPanel, context: vscode.ExtensionContext) {
  // Monitor pheromone activity every 5 seconds
  const monitoringInterval = setInterval(async () => {
    try {
      const orchestratorUrl = 'http://localhost:8000';

      // Check for active projects and pheromone activity
      const [projectsResponse, pheromoneResponse] = await Promise.allSettled([
        axios.get(`${orchestratorUrl}/projects`, { timeout: 2000 }),
        axios.get(`${orchestratorUrl}/pheromones/statistics`, { timeout: 2000 })
      ]);

      // Update webview with real-time data
      if (projectsResponse.status === 'fulfilled') {
        const activeProjects = projectsResponse.value.data.filter((p: any) =>
          p.status === 'in_progress' || p.status === 'generating'
        );

        if (activeProjects.length > 0) {
          panel.webview.postMessage({
            command: 'activeProjectsUpdate',
            data: {
              count: activeProjects.length,
              projects: activeProjects
            }
          });
        }
      }

      // Update pheromone statistics
      if (pheromoneResponse.status === 'fulfilled') {
        panel.webview.postMessage({
          command: 'pheromoneUpdate',
          data: pheromoneResponse.value.data
        });
      }

    } catch (error) {
      // Silently handle monitoring errors to avoid spam
      console.log('Monitoring error:', error);
    }
  }, 5000);

  // Store interval for cleanup
  context.subscriptions.push({
    dispose: () => clearInterval(monitoringInterval)
  });
}

// Setup file system watchers for project changes
function setupFileSystemWatchers(context: vscode.ExtensionContext) {
  const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  if (!workspaceRoot) return;

  const projectsPath = path.join(workspaceRoot, 'projects');

  // Watch for new projects being created
  const projectWatcher = vscode.workspace.createFileSystemWatcher(
    new vscode.RelativePattern(projectsPath, '**/README.md')
  );

  projectWatcher.onDidCreate(async (uri) => {
    const projectDir = path.dirname(uri.fsPath);
    const projectName = path.basename(projectDir);

    // Check if this is a new Aetherforge project
    const metadataFile = path.join(projectDir, '.aetherforge.json');
    if (fs.existsSync(metadataFile)) {
      vscode.window.showInformationMessage(
        `✨ New Aetherforge project detected: ${projectName}`,
        'Open Project'
      ).then(selection => {
        if (selection === 'Open Project') {
          vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectDir), true);
        }
      });
    }
  });

  // Watch for project completion (status changes)
  const metadataWatcher = vscode.workspace.createFileSystemWatcher(
    new vscode.RelativePattern(projectsPath, '**/.aetherforge.json')
  );

  metadataWatcher.onDidChange(async (uri) => {
    try {
      const metadata = JSON.parse(fs.readFileSync(uri.fsPath, 'utf-8'));

      if (metadata.status === 'completed' && metadata.completed_at) {
        const projectName = path.basename(path.dirname(uri.fsPath));

        vscode.window.showInformationMessage(
          `🎉 Project ${projectName} completed successfully!`,
          'Open Project', 'View Files'
        ).then(selection => {
          const projectDir = path.dirname(uri.fsPath);

          if (selection === 'Open Project') {
            vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectDir), true);
          } else if (selection === 'View Files') {
            vscode.commands.executeCommand('revealFileInOS', vscode.Uri.file(projectDir));
          }
        });
      }
    } catch (error) {
      console.log('Error reading project metadata:', error);
    }
  });

  context.subscriptions.push(projectWatcher, metadataWatcher);
}
