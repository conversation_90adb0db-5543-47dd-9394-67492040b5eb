{"name": "pheromind-visualizer-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tremor/react": "^3.18.7", "axios": "^1.4.0", "chart.js": "^4.3.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "socket.io-client": "^4.6.1", "tailwindcss": "^3.3.2", "vis-network": "^9.1.6", "vis-timeline": "^7.7.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.23"}, "proxy": "http://localhost:3001"}