FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY src/ ./src/
COPY pheromone_bus_simple.py .
COPY agent_executors.py .

# Create necessary directories
RUN mkdir -p /app/projects /app/data

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Create a non-root user
RUN useradd --create-home --shell /bin/bash aetherforge
RUN chown -R aetherforge:aetherforge /app
USER aetherforge

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port for the API
EXPOSE 8000

# Run the orchestrator with uvicorn
CMD ["uvicorn", "src.orchestrator:app", "--host", "0.0.0.0", "--port", "8000"]