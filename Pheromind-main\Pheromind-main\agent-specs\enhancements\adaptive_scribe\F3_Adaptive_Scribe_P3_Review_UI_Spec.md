# Feature Specification: F3 - Adaptive Scribe - Phase 3: Review UI & Integration

**Document ID:** `agent-specs/enhancements/adaptive_scribe/F3_Adaptive_Scribe_P3_Review_UI_Spec.md`
**Parent Spec:** [`F3_Adaptive_Scribe_Overall_Spec.md`](./F3_Adaptive_Scribe_Overall_Spec.md)
**Version:** 1.0
**Date:** 2025-05-15

## 1. Introduction

This specification details the requirements for the "Suggestion Review UI," which is Phase 3 of the Self-adaptive `interpretationLogic` feature. This UI will allow a human administrator or architect to review, modify, accept, or reject suggestions for `.swarmConfig` rule changes generated by the Phase 2 Analysis & Suggestion Engine.

This UI should ideally be integrated into the "Advanced `.swarmConfig` Tuning & Validation UI" (Feature F2) as a new section/tab.

## 2. Goals

* Provide a clear and intuitive interface for reviewing rule suggestions.
* Allow users to understand the evidence and reasoning behind each suggestion.
* Enable users to accept suggestions, automatically updating the loaded `.swarmConfig`.
* Enable users to edit suggestions before accepting.
* Enable users to reject suggestions.
* Maintain a history or status of reviewed suggestions.

## 3. Integration with `.swarmConfig` Tuning & Validation UI (Feature F2)

* The Review UI will appear as a new tab or section within the existing F2 application.
* It will operate on the `.swarmConfig` data currently loaded in the F2 UI's state.
* Accepted suggestions will directly modify this in-memory `.swarmConfig` object. The user can then save the updated `.swarmConfig` using F2's existing save functionality.

## 4. UI Components & Views

### 4.1. Main Suggestion Review View (`ScribeSuggestionsView.tsx`)

* **File Input:** "Load Suggestions File" button to load the JSON output from the Phase 2 Analysis Engine (e.g., `suggestions.json`).
* **Summary Display:**
    * Timestamp of when suggestions were generated.
    * Source log files and `.swarmConfig` path used by the engine.
* **Suggestion Lists (Separate tabs or expandable sections):**
    * Suggested Document Patterns
    * Suggested Signal Rules
* **Filtering/Sorting (Optional):** Filter suggestions by confidence, status (new, accepted, rejected).

### 4.2. Suggestion Card/Row Component (`SuggestionCard.tsx`)

Used to display each individual suggestion in the lists.

* **Content:**
    * `suggestion_id`.
    * **Proposed Rule Display:**
        * For Document Patterns: Show `pattern`, `docType`, `captureGroups`.
        * For Signal Rules: Show `conditionType`, `value`/`keywords`/`pattern`, and a summary of `generatesSignal`.
        * Clearly indicate if it's a "new" rule or a "modification" to an existing one (diff view for modifications would be ideal).
    * `reasoning` provided by the engine.
    * `confidence` score from the engine.
    * **Evidence Display:** A button or expandable section to show `evidence_summary_snippets`.
* **Actions:**
    * **"Accept" Button:**
        * Adds the new rule to the corresponding section (`documentPatterns` or `signalRules`) in the in-memory `.swarmConfig` object (managed by the F2 UI's global state).
        * Updates the suggestion's status to "accepted" in the UI.
    * **"Edit & Accept" Button:**
        * Opens a modal pre-filled with the suggested rule's details, using the same form components as the F2 structured editors (`DocumentPatternForm.tsx`, `SignalRuleForm.tsx`).
        * User can modify the rule.
        * On saving the modal, the (potentially modified) rule is accepted and added to the in-memory `.swarmConfig`.
    * **"Reject" Button:**
        * Updates the suggestion's status to "rejected" in the UI.
        * (Optional) Allows user to provide a reason for rejection.
* **Status Indicator:** "New", "Accepted", "Rejected", "Edited & Accepted".

### 4.3. Edit Suggestion Modal

* Reuses the existing form components from the F2 `.swarmConfig` Tuning UI (`DocumentPatternForm.tsx` or `SignalRuleForm.tsx`).
* Pre-populated with the details of the suggestion being edited.
* Allows modification of all relevant fields.
* "Save and Accept" button and "Cancel" button.

## 5. Data Flow & State Management

1.  User loads a `suggestions.json` file into the Suggestion Review View.
2.  The UI parses this file and stores the array of suggestions in its local state or a dedicated part of the F2 global state.
3.  Suggestions are rendered using `SuggestionCard.tsx`.
4.  When a user clicks "Accept" on a suggestion:
    * The corresponding rule (from `suggestion.proposed_rule`) is retrieved.
    * The F2 UI's global state holding the main `.swarmConfig` object is updated:
        * The new rule is appended to the `documentPatterns` or `signalRules` array.
        * (If it's a modification, the existing rule needs to be found and replaced - requires unique IDs for existing rules or careful diffing, which is more complex). For v1, focus on adding *new* rules.
    * The `isValid` and `validationErrors` in the F2 global state are re-calculated by re-validating the modified `.swarmConfig`.
    * The status of the suggestion in the Review UI is updated.
5.  If "Edit & Accept" is used, the modal allows changes, and the modified rule is then used to update the global `.swarmConfig` state.
6.  The user can then navigate to other F2 UI sections (e.g., Raw JSON Editor, structured editors) to see the effect of the accepted suggestion on the overall `.swarmConfig`.
7.  Finally, the user saves the modified `.swarmConfig` using F2's "Save" button.

## 6. Non-Functional Requirements

* **Clarity:** Suggestions, evidence, and reasoning must be displayed clearly.
* **Responsiveness:** UI should handle a moderate number of suggestions (e.g., 50-100) smoothly.
* **Safety:** Actions like "Accept" should clearly indicate they are modifying the loaded `.swarmConfig`. The main save operation is still a deliberate user action in the F2 UI.

## 7. Future Considerations

* **Diff View for Modified Rules:** If the suggestion engine proposes changes to *existing* rules, the UI should show a clear "diff" of the changes. This requires a stable way to identify existing rules.
* **Tracking Suggestion History:** Persisting the status (accepted/rejected) of suggestions across sessions, perhaps by allowing the user to save the `suggestions.json` file with updated statuses.
* **Direct Feedback to Suggestion Engine:** Mechanism to provide feedback on why a suggestion was rejected, which could help improve the engine over time.

This phased approach to self-adaptive `interpretationLogic`, culminating in a human-reviewed UI, provides a balance between automation and control, allowing Pheromind to evolve its intelligence responsibly.
