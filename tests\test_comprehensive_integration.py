"""
Comprehensive Integration Tests for TaoForge System
Tests the complete end-to-end functionality across all components
"""

import pytest
import asyncio
import json
import tempfile
import shutil
import time
import requests
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import sys
import os
from typing import Dict, List, Any

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import core components
try:
    from orchestrator import create_app, Orchestrator
    from component_adapters import ComponentManager
    from agent_executors import create_agent_executor
    from pheromone_bus import EnhancedPheromonebus
    from project_generator import ProjectGenerationPipeline
    from config_manager import ConfigurationManager
    from workflow_engine import WorkflowExecutionEngine
except ImportError as e:
    pytest.skip(f"Required modules not available: {e}", allow_module_level=True)


class TestComprehensiveIntegration:
    """Comprehensive integration test suite for TaoForge"""
    
    @pytest.fixture(scope="class")
    def test_environment(self):
        """Setup comprehensive test environment"""
        temp_dir = tempfile.mkdtemp(prefix="taoforge_integration_")
        
        env_config = {
            "temp_dir": temp_dir,
            "projects_dir": Path(temp_dir) / "projects",
            "config_dir": Path(temp_dir) / "config",
            "logs_dir": Path(temp_dir) / "logs",
            "pheromone_file": Path(temp_dir) / "pheromones.json"
        }
        
        # Create directories
        for key, path in env_config.items():
            if key.endswith("_dir"):
                path.mkdir(parents=True, exist_ok=True)
        
        yield env_config
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def orchestrator_app(self, test_environment):
        """Create orchestrator FastAPI app for testing"""
        with patch.dict(os.environ, {
            "PROJECTS_DIR": str(test_environment["projects_dir"]),
            "LOG_LEVEL": "DEBUG",
            "TAOFORGE_ENV": "test"
        }):
            orchestrator = Orchestrator()
            app = create_app(orchestrator)
            yield app, orchestrator
    
    @pytest.fixture
    def mock_external_services(self):
        """Mock all external services for integration testing"""
        mocks = {
            "openai": Mock(),
            "archon": Mock(),
            "mcp": Mock(),
            "pheromind": Mock(),
            "bmad": Mock()
        }
        
        # Configure OpenAI mock
        mocks["openai"].chat.completions.create.return_value = Mock(
            choices=[Mock(message=Mock(content="Mock AI response"))]
        )
        
        # Configure Archon mock
        mocks["archon"].generate_agent_team.return_value = {
            "team_id": "test_team_123",
            "agents": [
                {"role": "analyst", "name": "Test Analyst"},
                {"role": "architect", "name": "Test Architect"},
                {"role": "developer", "name": "Test Developer"},
                {"role": "qa", "name": "Test QA"}
            ]
        }
        
        # Configure MCP mock
        mocks["mcp"].research_technologies.return_value = {
            "project_type": "fullstack",
            "recommended_stack": {
                "frontend": "react",
                "backend": "nodejs",
                "database": "postgresql"
            }
        }
        
        # Configure Pheromind mock
        mocks["pheromind"].initialize_pheromone_bus.return_value = {
            "bus_id": "test_bus_123",
            "status": "initialized"
        }
        
        # Configure BMAD mock
        mocks["bmad"].execute_workflow.return_value = {
            "workflow_id": "test_workflow_123",
            "status": "completed"
        }
        
        return mocks
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_end_to_end_project_generation_web_app(self, test_environment, mock_external_services):
        """Test complete project generation for web application"""
        project_path = test_environment["projects_dir"] / "WebApp"
        
        # Mock agent executors
        with patch('src.agent_executors.create_agent_executor') as mock_create_agent:
            # Setup mock executors for each role
            mock_executors = self._create_mock_executors()
            mock_create_agent.side_effect = lambda role: mock_executors[role]
            
            # Create project generation pipeline
            pipeline = ProjectGenerationPipeline()
            
            # Execute project generation
            result = await pipeline.generate_project(
                prompt="Create a modern web application with user authentication and dashboard",
                project_name="WebApp",
                project_type="frontend",
                project_path=str(project_path)
            )
            
            # Verify results
            assert result["success"] is True
            assert result["project_type"] == "frontend"
            assert project_path.exists()
            
            # Verify project structure
            expected_files = [
                ".taoforge.json",
                "README.md",
                "package.json",
                "src/App.tsx",
                "src/index.tsx",
                "public/index.html"
            ]
            
            for file_path in expected_files:
                assert (project_path / file_path).exists(), f"Missing file: {file_path}"
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_end_to_end_project_generation_mobile_app(self, test_environment, mock_external_services):
        """Test complete project generation for mobile application"""
        project_path = test_environment["projects_dir"] / "MobileApp"
        
        with patch('src.agent_executors.create_agent_executor') as mock_create_agent:
            mock_executors = self._create_mock_executors()
            mock_create_agent.side_effect = lambda role: mock_executors[role]
            
            pipeline = ProjectGenerationPipeline()
            
            result = await pipeline.generate_project(
                prompt="Create a mobile app for task management with offline sync",
                project_name="MobileApp",
                project_type="mobile",
                project_path=str(project_path)
            )
            
            assert result["success"] is True
            assert result["project_type"] == "mobile"
            assert project_path.exists()
            
            # Verify mobile-specific files
            mobile_files = [
                "App.tsx",
                "app.json",
                "package.json",
                "src/screens/HomeScreen.tsx",
                "src/navigation/AppNavigator.tsx"
            ]
            
            for file_path in mobile_files:
                assert (project_path / file_path).exists(), f"Missing mobile file: {file_path}"
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_end_to_end_project_generation_backend_service(self, test_environment, mock_external_services):
        """Test complete project generation for backend service"""
        project_path = test_environment["projects_dir"] / "BackendService"
        
        with patch('src.agent_executors.create_agent_executor') as mock_create_agent:
            mock_executors = self._create_mock_executors()
            mock_create_agent.side_effect = lambda role: mock_executors[role]
            
            pipeline = ProjectGenerationPipeline()
            
            result = await pipeline.generate_project(
                prompt="Create a REST API service for user management with authentication",
                project_name="BackendService",
                project_type="backend",
                project_path=str(project_path)
            )
            
            assert result["success"] is True
            assert result["project_type"] == "backend"
            assert project_path.exists()
            
            # Verify backend-specific files
            backend_files = [
                "main.py",
                "requirements.txt",
                "src/api/routes.py",
                "src/models/user.py",
                "src/auth/jwt_handler.py",
                "tests/test_api.py"
            ]
            
            for file_path in backend_files:
                assert (project_path / file_path).exists(), f"Missing backend file: {file_path}"
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_end_to_end_project_generation_fullstack(self, test_environment, mock_external_services):
        """Test complete project generation for full-stack application"""
        project_path = test_environment["projects_dir"] / "FullStackApp"
        
        with patch('src.agent_executors.create_agent_executor') as mock_create_agent:
            mock_executors = self._create_mock_executors()
            mock_create_agent.side_effect = lambda role: mock_executors[role]
            
            pipeline = ProjectGenerationPipeline()
            
            result = await pipeline.generate_project(
                prompt="Create a full-stack e-commerce platform with React frontend and Node.js backend",
                project_name="FullStackApp",
                project_type="fullstack",
                project_path=str(project_path)
            )
            
            assert result["success"] is True
            assert result["project_type"] == "fullstack"
            assert project_path.exists()
            
            # Verify full-stack structure
            fullstack_structure = [
                "frontend/package.json",
                "frontend/src/App.tsx",
                "backend/package.json",
                "backend/src/server.js",
                "backend/src/routes/api.js",
                "database/schema.sql",
                "docker-compose.yml"
            ]
            
            for file_path in fullstack_structure:
                assert (project_path / file_path).exists(), f"Missing fullstack file: {file_path}"
    
    def _create_mock_executors(self) -> Dict[str, Mock]:
        """Create mock executors for all agent roles"""
        executors = {}
        
        for role in ["analyst", "architect", "developer", "qa"]:
            mock_executor = Mock()
            mock_executor.role = role
            mock_executor.execute = AsyncMock(return_value={
                "success": True,
                "outputs": [f"docs/{role}_output.md"],
                "summary": f"{role.title()} phase completed successfully",
                "files_created": 5 if role == "developer" else 1,
                "duration": 30.0
            })
            executors[role] = mock_executor
        
        return executors

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_orchestrator_agent_coordination(self, test_environment, mock_external_services):
        """Test orchestrator and agent coordination"""
        async with ComponentManager() as cm:
            # Test agent team generation
            team_result = await cm.archon.generate_agent_team(
                "Create a web application",
                "fullstack"
            )

            assert "team_id" in team_result
            assert "agents" in team_result
            assert len(team_result["agents"]) >= 4  # analyst, architect, developer, qa

            # Test agent execution coordination
            for agent_info in team_result["agents"]:
                executor = create_agent_executor(agent_info["role"])
                assert executor is not None
                assert executor.role == agent_info["role"]

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_pheromone_system_integration(self, test_environment):
        """Test pheromone system integration with agents"""
        pheromone_file = test_environment["pheromone_file"]
        pheromone_bus = EnhancedPheromonebus(str(pheromone_file))

        try:
            await pheromone_bus.start_cleanup_task()

            # Test pheromone dropping and retrieval
            project_id = "test_project_integration"

            # Drop various pheromones
            pheromones = [
                ("project_started", {"message": "Project initialization"}),
                ("agent_assigned", {"agent": "analyst", "task": "requirements"}),
                ("phase_completed", {"phase": "analysis", "duration": 300}),
                ("error_occurred", {"error": "test error", "severity": "low"})
            ]

            for signal, payload in pheromones:
                pheromone_id = await pheromone_bus.drop_pheromone(
                    signal, payload, project_id
                )
                assert pheromone_id is not None

            # Test pheromone statistics
            stats = pheromone_bus.get_statistics()
            assert stats["total_pheromones"] >= len(pheromones)
            assert stats["active_projects"] >= 1

            # Test project trails
            trails = pheromone_bus.get_project_trails(project_id)
            assert len(trails) >= len(pheromones)

        finally:
            await pheromone_bus.shutdown()

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_api_resilience_mechanisms(self, orchestrator_app, mock_external_services):
        """Test API resilience and error handling"""
        app, orchestrator = orchestrator_app

        # Test with various failure scenarios
        failure_scenarios = [
            {"type": "timeout", "delay": 5.0},
            {"type": "rate_limit", "status_code": 429},
            {"type": "server_error", "status_code": 500},
            {"type": "network_error", "exception": ConnectionError}
        ]

        for scenario in failure_scenarios:
            with patch('requests.post') as mock_post:
                if scenario["type"] == "timeout":
                    mock_post.side_effect = requests.Timeout("Request timeout")
                elif scenario["type"] == "rate_limit":
                    mock_response = Mock()
                    mock_response.status_code = 429
                    mock_response.json.return_value = {"error": "Rate limit exceeded"}
                    mock_post.return_value = mock_response
                elif scenario["type"] == "server_error":
                    mock_response = Mock()
                    mock_response.status_code = 500
                    mock_response.json.return_value = {"error": "Internal server error"}
                    mock_post.return_value = mock_response
                elif scenario["type"] == "network_error":
                    mock_post.side_effect = ConnectionError("Network unreachable")

                # Test that orchestrator handles failures gracefully
                try:
                    # This should not crash the system
                    result = await orchestrator.handle_api_failure(scenario)
                    assert "error_handled" in result
                except Exception as e:
                    # Should be handled gracefully
                    assert "handled" in str(e).lower() or "retry" in str(e).lower()

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_multi_agent_coordination(self, test_environment, mock_external_services):
        """Test multi-agent coordination and communication"""
        project_path = test_environment["projects_dir"] / "MultiAgentTest"
        pheromone_file = test_environment["pheromone_file"]

        # Setup pheromone bus for agent communication
        pheromone_bus = EnhancedPheromonebus(str(pheromone_file))

        try:
            await pheromone_bus.start_cleanup_task()

            # Create mock agents that communicate via pheromones
            agents = {}
            for role in ["analyst", "architect", "developer", "qa"]:
                agent = create_agent_executor(role)
                agents[role] = agent

            # Simulate agent coordination workflow
            project_id = "multi_agent_test"

            # Phase 1: Analyst starts and signals completion
            await pheromone_bus.drop_pheromone(
                "agent_started",
                {"agent": "analyst", "phase": "requirements_analysis"},
                project_id
            )

            # Simulate analyst work
            await asyncio.sleep(0.1)

            await pheromone_bus.drop_pheromone(
                "phase_completed",
                {"agent": "analyst", "phase": "requirements_analysis", "outputs": ["requirements.md"]},
                project_id
            )

            # Phase 2: Architect receives signal and starts
            await pheromone_bus.drop_pheromone(
                "agent_started",
                {"agent": "architect", "phase": "system_design"},
                project_id
            )

            await pheromone_bus.drop_pheromone(
                "phase_completed",
                {"agent": "architect", "phase": "system_design", "outputs": ["architecture.md"]},
                project_id
            )

            # Phase 3: Developer starts parallel work
            await pheromone_bus.drop_pheromone(
                "agent_started",
                {"agent": "developer", "phase": "implementation"},
                project_id
            )

            await pheromone_bus.drop_pheromone(
                "phase_completed",
                {"agent": "developer", "phase": "implementation", "outputs": ["src/"]},
                project_id
            )

            # Phase 4: QA starts after development
            await pheromone_bus.drop_pheromone(
                "agent_started",
                {"agent": "qa", "phase": "testing"},
                project_id
            )

            await pheromone_bus.drop_pheromone(
                "phase_completed",
                {"agent": "qa", "phase": "testing", "outputs": ["test_plan.md"]},
                project_id
            )

            # Verify coordination worked
            trails = pheromone_bus.get_project_trails(project_id)
            assert len(trails) >= 8  # 4 starts + 4 completions

            # Verify proper sequence
            phase_order = []
            for trail in trails:
                if trail["signal"] == "phase_completed":
                    phase_order.append(trail["payload"]["agent"])

            expected_order = ["analyst", "architect", "developer", "qa"]
            assert phase_order == expected_order

        finally:
            await pheromone_bus.shutdown()

    @pytest.mark.integration
    @pytest.mark.vscode
    def test_vscode_extension_functionality(self, mock_vscode, mock_extension_context):
        """Test VS Code extension functionality and integration"""
        # Import VS Code extension modules
        try:
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'vscode-extension', 'src'))
            from extension import activate, deactivate
            from webview_provider import TaoForgeWebviewProvider
        except ImportError:
            pytest.skip("VS Code extension modules not available")

        # Test extension activation
        activate(mock_extension_context)
        assert len(mock_extension_context.subscriptions) > 0

        # Test webview provider
        provider = TaoForgeWebviewProvider(mock_extension_context.extensionPath)
        assert provider is not None

        # Test webview creation
        with patch.object(mock_vscode.window, 'createWebviewPanel') as mock_create_panel:
            mock_panel = Mock()
            mock_create_panel.return_value = mock_panel

            webview = provider.create_webview(mock_vscode.ViewColumn.One)
            assert webview is not None
            mock_create_panel.assert_called_once()

        # Test extension deactivation
        deactivate()

    @pytest.mark.integration
    @pytest.mark.vscode
    @pytest.mark.asyncio
    async def test_vscode_orchestrator_integration(self, orchestrator_app, mock_vscode):
        """Test VS Code extension integration with orchestrator"""
        app, orchestrator = orchestrator_app

        # Mock VS Code extension API calls to orchestrator
        with patch('requests.post') as mock_post:
            # Mock successful project creation response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "status": "success",
                "project_id": "vscode_test_project",
                "message": "Project creation started"
            }
            mock_post.return_value = mock_response

            # Simulate VS Code extension creating a project
            project_data = {
                "prompt": "Create a React application from VS Code",
                "project_name": "VSCodeProject",
                "project_type": "frontend"
            }

            # Test API call from extension
            response = mock_post("http://localhost:8000/projects", json=project_data)
            assert response.status_code == 200

            result = response.json()
            assert result["status"] == "success"
            assert "project_id" in result

    @pytest.mark.integration
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_performance_under_load(self, test_environment):
        """Test system performance under load"""
        projects_dir = test_environment["projects_dir"]

        # Create multiple concurrent project generation tasks
        concurrent_projects = 5
        project_tasks = []

        with patch('src.agent_executors.create_agent_executor') as mock_create_agent:
            mock_executors = self._create_mock_executors()
            mock_create_agent.side_effect = lambda role: mock_executors[role]

            pipeline = ProjectGenerationPipeline()

            # Start multiple projects concurrently
            for i in range(concurrent_projects):
                project_path = projects_dir / f"LoadTest_{i}"

                task = pipeline.generate_project(
                    prompt=f"Create test project {i}",
                    project_name=f"LoadTest_{i}",
                    project_type="frontend",
                    project_path=str(project_path)
                )
                project_tasks.append(task)

            # Measure execution time
            start_time = time.time()
            results = await asyncio.gather(*project_tasks, return_exceptions=True)
            end_time = time.time()

            # Verify results
            successful_projects = 0
            for result in results:
                if isinstance(result, dict) and result.get("success"):
                    successful_projects += 1

            # Performance assertions
            total_time = end_time - start_time
            assert successful_projects >= concurrent_projects * 0.8  # 80% success rate
            assert total_time < 120  # Should complete within 2 minutes

            # Verify all project directories were created
            created_projects = 0
            for i in range(concurrent_projects):
                project_path = projects_dir / f"LoadTest_{i}"
                if project_path.exists():
                    created_projects += 1

            assert created_projects >= concurrent_projects * 0.8

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_external_system_integration(self, mock_external_services):
        """Test integration with external systems (MCP-RAG, Archon)"""
        async with ComponentManager() as cm:
            # Test Archon integration
            archon_result = await cm.archon.generate_agent_team(
                "Create a machine learning pipeline",
                "data_platform"
            )

            assert "team_id" in archon_result
            assert "agents" in archon_result
            assert len(archon_result["agents"]) > 0

            # Verify agent roles are appropriate for data platform
            agent_roles = [agent["role"] for agent in archon_result["agents"]]
            expected_roles = ["analyst", "architect", "developer", "qa"]
            for role in expected_roles:
                assert role in agent_roles

            # Test MCP-RAG integration
            mcp_result = await cm.mcp.research_technologies(
                "data_platform",
                ["machine learning", "data processing", "model training"]
            )

            assert "project_type" in mcp_result
            assert "recommended_stack" in mcp_result
            assert mcp_result["project_type"] == "data_platform"

            # Test Pheromind integration
            pheromind_result = await cm.pheromind.initialize_pheromone_bus(
                archon_result,
                "/test/project/path",
                "test_ml_project"
            )

            assert "bus_id" in pheromind_result
            assert pheromind_result["status"] == "initialized"
