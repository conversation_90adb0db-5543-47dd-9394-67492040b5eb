import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock VS Code API globally
const mockVSCode = {
  postMessage: vi.fn(),
  sendRequest: vi.fn(),
  getState: vi.fn(),
  setState: vi.fn()
};

// Mock the acquireVsCodeApi function
Object.defineProperty(window, 'acquireVsCodeApi', {
  value: () => mockVSCode,
  writable: true
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
});

// Mock canvas context for chart rendering
HTMLCanvasElement.prototype.getContext = vi.fn().mockImplementation((contextType) => {
  if (contextType === '2d') {
    return {
      fillRect: vi.fn(),
      clearRect: vi.fn(),
      getImageData: vi.fn(() => ({ data: new Array(4) })),
      putImageData: vi.fn(),
      createImageData: vi.fn(() => ({ data: new Array(4) })),
      setTransform: vi.fn(),
      drawImage: vi.fn(),
      save: vi.fn(),
      fillText: vi.fn(),
      restore: vi.fn(),
      beginPath: vi.fn(),
      moveTo: vi.fn(),
      lineTo: vi.fn(),
      closePath: vi.fn(),
      stroke: vi.fn(),
      translate: vi.fn(),
      scale: vi.fn(),
      rotate: vi.fn(),
      arc: vi.fn(),
      fill: vi.fn(),
      measureText: vi.fn(() => ({ width: 0 })),
      transform: vi.fn(),
      rect: vi.fn(),
      clip: vi.fn()
    };
  }
  return null;
});

// Mock HTMLCanvasElement.toBlob
HTMLCanvasElement.prototype.toBlob = vi.fn((callback) => {
  callback(new Blob([''], { type: 'image/png' }));
});

// Mock HTMLCanvasElement.toDataURL
HTMLCanvasElement.prototype.toDataURL = vi.fn(() => 'data:image/png;base64,');

// Mock URL.createObjectURL and revokeObjectURL
global.URL.createObjectURL = vi.fn(() => 'blob:mock-url');
global.URL.revokeObjectURL = vi.fn();

// Mock navigator.clipboard
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: vi.fn().mockResolvedValue(undefined),
    readText: vi.fn().mockResolvedValue('')
  },
  writable: true
});

// Mock navigator.share
Object.defineProperty(navigator, 'share', {
  value: vi.fn().mockResolvedValue(undefined),
  writable: true
});

// Mock window.getComputedStyle
window.getComputedStyle = vi.fn().mockImplementation(() => ({
  getPropertyValue: vi.fn().mockReturnValue(''),
  width: '0px',
  height: '0px'
}));

// Mock scrollTo
window.scrollTo = vi.fn();

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn();

// Mock performance.now
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now())
  },
  writable: true
});

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = vi.fn((...args) => {
  // Only show actual errors, not React warnings
  if (typeof args[0] === 'string' && args[0].includes('Warning:')) {
    return;
  }
  originalConsoleError(...args);
});

console.warn = vi.fn((...args) => {
  // Filter out known warnings
  if (typeof args[0] === 'string' && (
    args[0].includes('componentWillReceiveProps') ||
    args[0].includes('componentWillMount')
  )) {
    return;
  }
  originalConsoleWarn(...args);
});

// Global test utilities
export const createMockFile = (overrides = {}) => ({
  id: 'mock-file-id',
  name: 'mock-file.ts',
  path: '/mock/path/mock-file.ts',
  type: 'file' as const,
  size: 1024,
  language: 'typescript',
  lastModified: '2023-01-01T00:00:00Z',
  complexity: 5,
  testCoverage: 80,
  issues: [],
  ...overrides
});

export const createMockComponent = (overrides = {}) => ({
  id: 'mock-component-id',
  name: 'MockComponent',
  type: 'service' as const,
  description: 'Mock component description',
  technologies: ['TypeScript'],
  dependencies: [],
  dependents: [],
  files: ['/mock/component.ts'],
  status: 'completed' as const,
  metrics: {
    linesOfCode: 100,
    complexity: 5,
    testCoverage: 85,
    maintainabilityIndex: 75,
    technicalDebt: 1,
    performance: 90,
    security: 95
  },
  ...overrides
});

export const createMockIssue = (overrides = {}) => ({
  id: 'mock-issue-id',
  type: 'warning' as const,
  severity: 'medium' as const,
  message: 'Mock issue message',
  line: 10,
  column: 5,
  rule: 'mock-rule',
  category: 'style' as const,
  ...overrides
});

export const createMockWorkflowExecution = (overrides = {}) => ({
  id: 'mock-execution-id',
  workflowId: 'mock-workflow-id',
  projectId: 'mock-project-id',
  status: 'running' as const,
  progress: 0.5,
  steps: [],
  agents: [],
  pheromones: [],
  startedAt: '2023-01-01T00:00:00Z',
  metrics: {
    totalSteps: 5,
    completedSteps: 2,
    failedSteps: 0,
    averageStepDuration: 120,
    efficiency: 0.8,
    parallelization: 0.4,
    resourceUtilization: 0.7
  },
  workflow: {
    id: 'mock-workflow-id',
    name: 'Mock Workflow',
    description: 'Mock workflow description',
    version: '1.0.0',
    steps: [],
    triggers: [],
    metadata: {},
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  ...overrides
});

// Cleanup after each test
afterEach(() => {
  vi.clearAllMocks();
});
