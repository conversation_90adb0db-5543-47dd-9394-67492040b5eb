import { vscode } from '@/utils/vscode';
import { 
  ProjectStructure, 
  QualityMetrics, 
  WorkflowExecution,
  ProjectFile,
  ArchitectureComponent,
  CodeIssue
} from '@/types';

export interface ExportOptions {
  format: 'png' | 'svg' | 'pdf' | 'json' | 'csv' | 'html';
  includeMetadata?: boolean;
  includeTimestamp?: boolean;
  quality?: number; // For image exports (0-1)
  width?: number;
  height?: number;
  theme?: 'light' | 'dark';
}

export interface ReportOptions {
  title?: string;
  description?: string;
  includeStructure?: boolean;
  includeQuality?: boolean;
  includeWorkflows?: boolean;
  includePheromones?: boolean;
  includeRecommendations?: boolean;
  template?: 'executive' | 'technical' | 'detailed';
}

// Export visualization as image
export const exportVisualization = async (
  elementId: string,
  filename: string,
  options: ExportOptions = { format: 'png' }
): Promise<void> => {
  const element = document.getElementById(elementId);
  if (!element) {
    throw new Error(`Element with id "${elementId}" not found`);
  }

  try {
    switch (options.format) {
      case 'png':
      case 'svg':
        await exportAsImage(element, filename, options);
        break;
      case 'pdf':
        await exportAsPDF(element, filename, options);
        break;
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  } catch (error) {
    console.error('Export failed:', error);
    vscode.postMessage('showError', { 
      message: `Failed to export visualization: ${error instanceof Error ? error.message : 'Unknown error'}` 
    });
  }
};

// Export as image (PNG/SVG)
const exportAsImage = async (
  element: HTMLElement,
  filename: string,
  options: ExportOptions
): Promise<void> => {
  const { default: html2canvas } = await import('html2canvas');
  
  const canvas = await html2canvas(element, {
    backgroundColor: options.theme === 'dark' ? '#1f2937' : '#ffffff',
    scale: options.quality || 2,
    width: options.width,
    height: options.height,
    useCORS: true,
    allowTaint: true
  });

  if (options.format === 'svg') {
    // Convert canvas to SVG
    const svgData = canvasToSVG(canvas);
    downloadFile(svgData, filename, 'image/svg+xml');
  } else {
    // Export as PNG
    canvas.toBlob((blob) => {
      if (blob) {
        downloadBlob(blob, filename);
      }
    }, 'image/png', options.quality || 0.9);
  }
};

// Export as PDF
const exportAsPDF = async (
  element: HTMLElement,
  filename: string,
  options: ExportOptions
): Promise<void> => {
  const { default: jsPDF } = await import('jspdf');
  const { default: html2canvas } = await import('html2canvas');
  
  const canvas = await html2canvas(element, {
    backgroundColor: options.theme === 'dark' ? '#1f2937' : '#ffffff',
    scale: 2,
    useCORS: true,
    allowTaint: true
  });

  const imgData = canvas.toDataURL('image/png');
  const pdf = new jsPDF({
    orientation: canvas.width > canvas.height ? 'landscape' : 'portrait',
    unit: 'px',
    format: [canvas.width, canvas.height]
  });

  pdf.addImage(imgData, 'PNG', 0, 0, canvas.width, canvas.height);
  pdf.save(filename);
};

// Convert canvas to SVG
const canvasToSVG = (canvas: HTMLCanvasElement): string => {
  const imgData = canvas.toDataURL('image/png');
  return `
    <svg xmlns="http://www.w3.org/2000/svg" width="${canvas.width}" height="${canvas.height}">
      <image href="${imgData}" width="${canvas.width}" height="${canvas.height}"/>
    </svg>
  `;
};

// Export project data as JSON
export const exportProjectData = (
  projectId: string,
  structure: ProjectStructure | null,
  quality: QualityMetrics | null,
  workflows: WorkflowExecution[],
  options: ExportOptions = { format: 'json' }
): void => {
  const data = {
    projectId,
    exportedAt: new Date().toISOString(),
    structure,
    quality,
    workflows,
    metadata: options.includeMetadata ? {
      exportFormat: options.format,
      exportOptions: options,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    } : undefined
  };

  const filename = `project-${projectId}-${new Date().toISOString().split('T')[0]}.json`;
  
  switch (options.format) {
    case 'json':
      downloadFile(JSON.stringify(data, null, 2), filename, 'application/json');
      break;
    case 'csv':
      exportAsCSV(data, filename);
      break;
    default:
      throw new Error(`Unsupported data export format: ${options.format}`);
  }
};

// Export data as CSV
const exportAsCSV = (data: any, filename: string): void => {
  // Convert project data to CSV format
  const csvData = convertToCSV(data);
  downloadFile(csvData, filename.replace('.json', '.csv'), 'text/csv');
};

// Convert data to CSV format
const convertToCSV = (data: any): string => {
  const rows: string[] = [];
  
  // Add header
  rows.push('Type,Name,Value,Details');
  
  // Add structure data
  if (data.structure) {
    rows.push(`Structure,Total Files,${data.structure.metrics.totalFiles},`);
    rows.push(`Structure,Total Components,${data.structure.metrics.totalComponents},`);
    rows.push(`Structure,Average Complexity,${data.structure.metrics.averageComplexity},`);
    rows.push(`Structure,Test Coverage,${data.structure.metrics.overallTestCoverage}%,`);
  }
  
  // Add quality data
  if (data.quality) {
    rows.push(`Quality,Overall Score,${data.quality.overall}%,`);
    data.quality.issues.forEach((group: any) => {
      rows.push(`Quality,${group.category} Issues,${group.count},${group.severity}`);
    });
  }
  
  // Add workflow data
  data.workflows.forEach((workflow: WorkflowExecution) => {
    rows.push(`Workflow,${workflow.workflow.name},${workflow.status},${Math.round(workflow.progress * 100)}%`);
  });
  
  return rows.join('\n');
};

// Generate comprehensive report
export const generateReport = async (
  projectId: string,
  structure: ProjectStructure | null,
  quality: QualityMetrics | null,
  workflows: WorkflowExecution[],
  options: ReportOptions = {}
): Promise<void> => {
  const reportData = {
    title: options.title || `Project Report - ${projectId}`,
    description: options.description || 'Comprehensive project analysis report',
    generatedAt: new Date().toISOString(),
    projectId,
    structure: options.includeStructure ? structure : null,
    quality: options.includeQuality ? quality : null,
    workflows: options.includeWorkflows ? workflows : null,
    template: options.template || 'technical'
  };

  const html = generateReportHTML(reportData);
  const filename = `report-${projectId}-${new Date().toISOString().split('T')[0]}.html`;
  
  downloadFile(html, filename, 'text/html');
  
  // Also send to VS Code for potential PDF conversion
  vscode.postMessage('generateReport', { reportData, filename });
};

// Generate HTML report
const generateReportHTML = (data: any): string => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.title}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; line-height: 1.6; }
        .header { border-bottom: 2px solid #e5e7eb; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .metric { display: flex; justify-content: space-between; padding: 10px; background: #f9fafb; margin: 5px 0; border-radius: 6px; }
        .chart { width: 100%; height: 200px; background: #f3f4f6; border-radius: 8px; margin: 10px 0; }
        .issue { padding: 8px; margin: 4px 0; border-left: 4px solid #ef4444; background: #fef2f2; }
        .recommendation { padding: 8px; margin: 4px 0; border-left: 4px solid #3b82f6; background: #eff6ff; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; }
        th { background: #f9fafb; font-weight: 600; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${data.title}</h1>
        <p>${data.description}</p>
        <p><strong>Generated:</strong> ${new Date(data.generatedAt).toLocaleString()}</p>
        <p><strong>Project ID:</strong> ${data.projectId}</p>
    </div>
    
    ${data.structure ? generateStructureSection(data.structure) : ''}
    ${data.quality ? generateQualitySection(data.quality) : ''}
    ${data.workflows ? generateWorkflowSection(data.workflows) : ''}
</body>
</html>
  `;
};

const generateStructureSection = (structure: ProjectStructure): string => {
  return `
    <div class="section">
        <h2>Project Structure</h2>
        <div class="metric"><span>Total Files:</span><span>${structure.metrics.totalFiles}</span></div>
        <div class="metric"><span>Total Components:</span><span>${structure.metrics.totalComponents}</span></div>
        <div class="metric"><span>Average Complexity:</span><span>${structure.metrics.averageComplexity.toFixed(2)}</span></div>
        <div class="metric"><span>Test Coverage:</span><span>${structure.metrics.overallTestCoverage.toFixed(1)}%</span></div>
    </div>
  `;
};

const generateQualitySection = (quality: QualityMetrics): string => {
  const issuesHTML = quality.issues.map(group => 
    `<div class="issue"><strong>${group.category}:</strong> ${group.count} ${group.severity} issues</div>`
  ).join('');
  
  return `
    <div class="section">
        <h2>Code Quality</h2>
        <div class="metric"><span>Overall Score:</span><span>${quality.overall.toFixed(1)}%</span></div>
        <h3>Issues</h3>
        ${issuesHTML}
    </div>
  `;
};

const generateWorkflowSection = (workflows: WorkflowExecution[]): string => {
  const workflowsHTML = workflows.map(workflow => 
    `<tr>
        <td>${workflow.workflow.name}</td>
        <td>${workflow.status}</td>
        <td>${Math.round(workflow.progress * 100)}%</td>
        <td>${new Date(workflow.startedAt).toLocaleString()}</td>
    </tr>`
  ).join('');
  
  return `
    <div class="section">
        <h2>Workflows</h2>
        <table>
            <thead>
                <tr><th>Name</th><th>Status</th><th>Progress</th><th>Started</th></tr>
            </thead>
            <tbody>${workflowsHTML}</tbody>
        </table>
    </div>
  `;
};

// Utility functions
const downloadFile = (content: string, filename: string, mimeType: string): void => {
  const blob = new Blob([content], { type: mimeType });
  downloadBlob(blob, filename);
};

const downloadBlob = (blob: Blob, filename: string): void => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// Share functionality
export const shareVisualization = async (
  elementId: string,
  title: string,
  description?: string
): Promise<void> => {
  if (navigator.share) {
    try {
      // Export as image first
      const element = document.getElementById(elementId);
      if (element) {
        const { default: html2canvas } = await import('html2canvas');
        const canvas = await html2canvas(element);
        
        canvas.toBlob(async (blob) => {
          if (blob) {
            const file = new File([blob], `${title}.png`, { type: 'image/png' });
            await navigator.share({
              title,
              text: description,
              files: [file]
            });
          }
        });
      }
    } catch (error) {
      console.error('Sharing failed:', error);
      // Fallback to copying link
      copyShareLink(title, description);
    }
  } else {
    // Fallback for browsers without Web Share API
    copyShareLink(title, description);
  }
};

const copyShareLink = (title: string, description?: string): void => {
  const shareText = `${title}${description ? '\n' + description : ''}`;
  navigator.clipboard.writeText(shareText).then(() => {
    vscode.postMessage('showInfo', { message: 'Share text copied to clipboard' });
  });
};
