/**
 * Tests for enhanced agent communication functionality
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import { AgentCommunicationPanel } from '../agent-communication-panel';

// Mock VS Code API
const mockVSCode = {
  commands: {
    executeCommand: jest.fn()
  },
  workspace: {
    workspaceFolders: [{
      uri: { fsPath: '/test/workspace' },
      name: 'test-workspace'
    }]
  },
  window: {
    activeTextEditor: {
      document: { fileName: '/test/workspace/src/main.ts' }
    }
  }
};

// Mock global vscode object
(global as any).vscode = mockVSCode;

describe('Enhanced Agent Communication', () => {
  let panel: AgentCommunicationPanel;
  let mockWebview: any;

  beforeEach(() => {
    mockWebview = {
      postMessage: jest.fn(),
      onDidReceiveMessage: jest.fn(),
      html: '',
      options: {},
      cspSource: 'test'
    };

    const mockWebviewPanel = {
      webview: mockWebview,
      dispose: jest.fn(),
      onDidDispose: jest.fn(),
      reveal: jest.fn()
    };

    panel = new AgentCommunicationPanel(mockWebviewPanel as any, 'test-context');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Message Validation', () => {
    test('should reject empty agent ID', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      await expect(
        (panel as any).sendMessageToOrchestrator('', message)
      ).rejects.toThrow('Invalid agent ID provided');
    });

    test('should reject invalid agent ID type', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      await expect(
        (panel as any).sendMessageToOrchestrator(123 as any, message)
      ).rejects.toThrow('Invalid agent ID provided');
    });

    test('should reject empty message content', async () => {
      const message = {
        id: 'test-msg',
        content: '',
        type: 'user_message',
        timestamp: new Date()
      };

      await expect(
        (panel as any).sendMessageToOrchestrator('test-agent', message)
      ).rejects.toThrow('Message content cannot be empty');
    });

    test('should reject whitespace-only message content', async () => {
      const message = {
        id: 'test-msg',
        content: '   \n\t  ',
        type: 'user_message',
        timestamp: new Date()
      };

      await expect(
        (panel as any).sendMessageToOrchestrator('test-agent', message)
      ).rejects.toThrow('Message content cannot be empty');
    });

    test('should reject message content that is too long', async () => {
      const message = {
        id: 'test-msg',
        content: 'a'.repeat(10001), // Exceeds 10000 character limit
        type: 'user_message',
        timestamp: new Date()
      };

      await expect(
        (panel as any).sendMessageToOrchestrator('test-agent', message)
      ).rejects.toThrow('Message content too long');
    });

    test('should accept valid message', async () => {
      const message = {
        id: 'test-msg',
        content: 'Valid test message',
        type: 'user_message',
        timestamp: new Date()
      };

      mockVSCode.commands.executeCommand.mockResolvedValue({
        content: 'Response from agent',
        messageId: 'response-msg'
      });

      await expect(
        (panel as any).sendMessageToOrchestrator('test-agent', message)
      ).resolves.toBeDefined();
    });
  });

  describe('Orchestrator Communication', () => {
    test('should handle successful orchestrator response', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      const mockResponse = {
        content: 'Agent response',
        messageId: 'response-123',
        timestamp: new Date().toISOString(),
        type: 'agent_message'
      };

      mockVSCode.commands.executeCommand.mockResolvedValue(mockResponse);

      const result = await (panel as any).sendMessageToOrchestrator('test-agent', message);
      
      expect(result).toEqual(mockResponse);
      expect(mockVSCode.commands.executeCommand).toHaveBeenCalledWith(
        'aetherforge.sendAgentMessage',
        expect.objectContaining({
          agentId: 'test-agent',
          message: 'Test message',
          messageType: 'user_message'
        })
      );
    });

    test('should handle orchestrator timeout', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      // Mock a timeout by never resolving
      mockVSCode.commands.executeCommand.mockImplementation(() => 
        new Promise(() => {}) // Never resolves
      );

      await expect(
        (panel as any).sendMessageToOrchestrator('test-agent', message)
      ).rejects.toThrow('Orchestrator communication timeout');
    }, 15000); // Increase test timeout

    test('should handle orchestrator error', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      mockVSCode.commands.executeCommand.mockRejectedValue(
        new Error('Orchestrator connection failed')
      );

      await expect(
        (panel as any).sendMessageToOrchestrator('test-agent', message)
      ).rejects.toThrow('Orchestrator communication failed');
    });

    test('should handle empty orchestrator response', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      mockVSCode.commands.executeCommand.mockResolvedValue(null);

      await expect(
        (panel as any).sendMessageToOrchestrator('test-agent', message)
      ).rejects.toThrow('Empty response from orchestrator');
    });

    test('should handle invalid orchestrator response format', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      mockVSCode.commands.executeCommand.mockResolvedValue('invalid response');

      await expect(
        (panel as any).sendMessageToOrchestrator('test-agent', message)
      ).rejects.toThrow('Invalid response format from orchestrator');
    });
  });

  describe('Project Context', () => {
    test('should get project context successfully', async () => {
      const context = await (panel as any).getProjectContext();
      
      expect(context).toEqual({
        workspacePath: '/test/workspace',
        workspaceName: 'test-workspace',
        activeFile: '/test/workspace/src/main.ts',
        openFiles: expect.any(Array),
        timestamp: expect.any(String)
      });
    });

    test('should handle missing workspace', async () => {
      const originalWorkspaceFolders = mockVSCode.workspace.workspaceFolders;
      mockVSCode.workspace.workspaceFolders = undefined;

      const context = await (panel as any).getProjectContext();
      
      expect(context).toEqual({
        workspacePath: undefined,
        workspaceName: undefined,
        activeFile: '/test/workspace/src/main.ts',
        openFiles: expect.any(Array),
        timestamp: expect.any(String)
      });

      mockVSCode.workspace.workspaceFolders = originalWorkspaceFolders;
    });
  });

  describe('Fallback Behavior', () => {
    test('should fall back to simulation when orchestrator fails', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      // Mock orchestrator failure
      mockVSCode.commands.executeCommand.mockRejectedValue(
        new Error('Connection failed')
      );

      // Should not throw, should fall back to simulation
      await expect(
        (panel as any).simulateAgentResponse('test-agent', message)
      ).resolves.toBeUndefined();
    });

    test('should generate simulated response', async () => {
      const message = {
        id: 'test-msg',
        content: 'Test message',
        type: 'user_message',
        timestamp: new Date()
      };

      // Initialize agent
      (panel as any).agents.set('test-agent', {
        id: 'test-agent',
        name: 'Test Agent',
        type: 'developer',
        status: 'idle',
        progress: 0,
        lastActivity: new Date()
      });

      await (panel as any).generateSimulatedResponse('test-agent', message);

      // Should have sent message to webview
      expect(mockWebview.postMessage).toHaveBeenCalled();
    });
  });
});
