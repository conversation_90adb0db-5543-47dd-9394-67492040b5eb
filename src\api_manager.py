"""
Enhanced API Manager for Aetherforge
Provides secure API key management, robust API integration with multiple providers,
fallback mechanisms, retry logic, rate limiting, and comprehensive configuration management.
"""

import asyncio
import time
import logging
import os
import hashlib
import base64
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path
from datetime import datetime, timedelta
import aiohttp
import openai
from openai import AsyncOpenAI
import anthropic
from anthropic import AsyncAnthropic
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import traceback
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/api_manager.log', mode='a')
    ]
)

logger = logging.getLogger(__name__)

# Ensure logs directory exists
os.makedirs('logs', exist_ok=True)

class APIProvider(Enum):
    """Supported API providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    AZURE = "azure"
    OPENROUTER = "openrouter"
    LOCAL = "local"
    OLLAMA = "ollama"

class SecureKeyStorage:
    """Secure storage for API keys using encryption"""

    def __init__(self, storage_path: Optional[str] = None):
        self.storage_path = Path(storage_path or os.path.expanduser("~/.aetherforge/keys.enc"))
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        self._key = self._get_or_create_key()
        self._fernet = Fernet(self._key)

    def _get_or_create_key(self) -> bytes:
        """Get or create encryption key"""
        key_file = self.storage_path.parent / "key.key"

        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            password = os.getenv("AETHERFORGE_MASTER_KEY", "aetherforge-default-key").encode()
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))

            # Save key and salt
            with open(key_file, 'wb') as f:
                f.write(key)
            with open(self.storage_path.parent / "salt", 'wb') as f:
                f.write(salt)

            return key

    def store_key(self, provider: str, api_key: str) -> None:
        """Store an encrypted API key"""
        try:
            logger.debug(f"Attempting to store API key for provider: {provider}")

            # Validate inputs
            if not provider or not isinstance(provider, str):
                raise ValueError("Provider must be a non-empty string")
            if not api_key or not isinstance(api_key, str):
                raise ValueError("API key must be a non-empty string")

            # Load existing keys
            keys = self.load_all_keys()

            # Add/update key
            keys[provider] = api_key

            # Ensure directory exists
            self.storage_path.parent.mkdir(parents=True, exist_ok=True)

            # Encrypt and save
            encrypted_data = self._fernet.encrypt(json.dumps(keys).encode())

            # Write to temporary file first, then rename for atomic operation
            temp_path = self.storage_path.with_suffix('.tmp')
            with open(temp_path, 'wb') as f:
                f.write(encrypted_data)

            # Atomic rename
            temp_path.replace(self.storage_path)

            logger.info(f"Successfully stored API key for provider: {provider}")

        except ValueError as e:
            logger.error(f"Invalid input for storing API key: {e}")
            raise
        except PermissionError as e:
            logger.error(f"Permission denied when storing API key for {provider}: {e}")
            raise RuntimeError(f"Cannot write to key storage: {e}")
        except Exception as e:
            logger.error(f"Unexpected error storing API key for {provider}: {e}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            raise RuntimeError(f"Failed to store API key: {e}")

    def load_key(self, provider: str) -> Optional[str]:
        """Load a decrypted API key"""
        try:
            logger.debug(f"Attempting to load API key for provider: {provider}")

            if not provider or not isinstance(provider, str):
                logger.warning(f"Invalid provider name: {provider}")
                return None

            keys = self.load_all_keys()
            key = keys.get(provider)

            if key:
                logger.debug(f"Successfully loaded API key for provider: {provider}")
            else:
                logger.debug(f"No API key found for provider: {provider}")

            return key

        except Exception as e:
            logger.warning(f"Failed to load API key for {provider}: {e}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return None

    def load_all_keys(self) -> Dict[str, str]:
        """Load all decrypted API keys"""
        try:
            if not self.storage_path.exists():
                logger.debug("Key storage file does not exist, returning empty dict")
                return {}

            logger.debug(f"Loading keys from: {self.storage_path}")

            with open(self.storage_path, 'rb') as f:
                encrypted_data = f.read()

            if not encrypted_data:
                logger.warning("Key storage file is empty")
                return {}

            decrypted_data = self._fernet.decrypt(encrypted_data)
            keys = json.loads(decrypted_data.decode())

            logger.debug(f"Successfully loaded {len(keys)} API keys")
            return keys

        except FileNotFoundError:
            logger.debug("Key storage file not found, returning empty dict")
            return {}
        except PermissionError as e:
            logger.error(f"Permission denied reading key storage: {e}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in key storage: {e}")
            return {}
        except Exception as e:
            logger.warning(f"Failed to load API keys: {e}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return {}

    def delete_key(self, provider: str) -> bool:
        """Delete an API key"""
        try:
            keys = self.load_all_keys()
            if provider in keys:
                del keys[provider]

                # Save updated keys
                encrypted_data = self._fernet.encrypt(json.dumps(keys).encode())
                with open(self.storage_path, 'wb') as f:
                    f.write(encrypted_data)

                logger.info(f"Deleted API key for provider: {provider}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete API key for {provider}: {e}")
            return False

    def list_providers(self) -> List[str]:
        """List all providers with stored keys"""
        keys = self.load_all_keys()
        return list(keys.keys())

class APIKeyValidator:
    """Validates API keys for different providers"""

    @staticmethod
    async def validate_openai_key(api_key: str) -> Dict[str, Any]:
        """Validate OpenAI API key"""
        try:
            client = AsyncOpenAI(api_key=api_key)

            # Test with a simple completion
            response = await client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )

            return {
                "valid": True,
                "provider": "openai",
                "model_access": ["gpt-3.5-turbo"],
                "message": "API key is valid"
            }
        except Exception as e:
            error_msg = str(e).lower()
            if "invalid" in error_msg or "unauthorized" in error_msg:
                return {"valid": False, "provider": "openai", "message": "Invalid API key"}
            elif "quota" in error_msg:
                return {"valid": False, "provider": "openai", "message": "API quota exceeded"}
            else:
                return {"valid": False, "provider": "openai", "message": f"Validation failed: {e}"}

    @staticmethod
    async def validate_anthropic_key(api_key: str) -> Dict[str, Any]:
        """Validate Anthropic API key"""
        try:
            client = AsyncAnthropic(api_key=api_key)

            # Test with a simple message
            response = await client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=5,
                messages=[{"role": "user", "content": "Hello"}]
            )

            return {
                "valid": True,
                "provider": "anthropic",
                "model_access": ["claude-3-haiku-20240307"],
                "message": "API key is valid"
            }
        except Exception as e:
            error_msg = str(e).lower()
            if "invalid" in error_msg or "unauthorized" in error_msg:
                return {"valid": False, "provider": "anthropic", "message": "Invalid API key"}
            elif "quota" in error_msg or "credit" in error_msg:
                return {"valid": False, "provider": "anthropic", "message": "API quota exceeded"}
            else:
                return {"valid": False, "provider": "anthropic", "message": f"Validation failed: {e}"}

    @staticmethod
    async def validate_azure_key(api_key: str, base_url: str, api_version: str = "2024-02-01") -> Dict[str, Any]:
        """Validate Azure OpenAI API key"""
        try:
            from openai import AsyncAzureOpenAI

            client = AsyncAzureOpenAI(
                api_key=api_key,
                azure_endpoint=base_url,
                api_version=api_version
            )

            # Test with a simple completion
            response = await client.chat.completions.create(
                model="gpt-35-turbo",  # Common Azure deployment name
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )

            return {
                "valid": True,
                "provider": "azure",
                "model_access": ["gpt-35-turbo", "gpt-4"],
                "message": "Azure API key is valid"
            }
        except Exception as e:
            error_msg = str(e).lower()
            if "invalid" in error_msg or "unauthorized" in error_msg:
                return {"valid": False, "provider": "azure", "message": "Invalid API key or endpoint"}
            elif "quota" in error_msg:
                return {"valid": False, "provider": "azure", "message": "API quota exceeded"}
            elif "deployment" in error_msg:
                return {"valid": False, "provider": "azure", "message": "Deployment not found - check model deployment name"}
            else:
                return {"valid": False, "provider": "azure", "message": f"Validation failed: {e}"}

    @staticmethod
    async def validate_openrouter_key(api_key: str) -> Dict[str, Any]:
        """Validate OpenRouter API key"""
        try:
            from openai import AsyncOpenAI

            # OpenRouter uses OpenAI-compatible API
            client = AsyncOpenAI(
                api_key=api_key,
                base_url="https://openrouter.ai/api/v1"
            )

            # Test with a simple completion using a free model
            response = await client.chat.completions.create(
                model="openai/gpt-3.5-turbo",  # OpenRouter model format
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )

            return {
                "valid": True,
                "provider": "openrouter",
                "model_access": ["openai/gpt-3.5-turbo", "openai/gpt-4", "anthropic/claude-3-sonnet", "meta-llama/llama-2-70b-chat"],
                "message": "OpenRouter API key is valid"
            }
        except Exception as e:
            error_msg = str(e).lower()
            if "invalid" in error_msg or "unauthorized" in error_msg or "401" in error_msg:
                return {"valid": False, "provider": "openrouter", "message": "Invalid API key"}
            elif "quota" in error_msg or "limit" in error_msg:
                return {"valid": False, "provider": "openrouter", "message": "API quota exceeded or rate limited"}
            elif "model" in error_msg:
                return {"valid": False, "provider": "openrouter", "message": "Model not available - check OpenRouter model access"}
            else:
                return {"valid": False, "provider": "openrouter", "message": f"Validation failed: {e}"}

    @staticmethod
    async def validate_local_endpoint(base_url: str) -> Dict[str, Any]:
        """Validate local API endpoint"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        models = [model.get("name", "unknown") for model in data.get("models", [])]
                        return {
                            "valid": True,
                            "provider": "local",
                            "model_access": models,
                            "message": "Local endpoint is accessible"
                        }
                    else:
                        return {"valid": False, "provider": "local", "message": f"Endpoint returned status {response.status}"}
        except Exception as e:
            return {"valid": False, "provider": "local", "message": f"Endpoint validation failed: {e}"}

    @classmethod
    async def validate_key(cls, provider: APIProvider, api_key: str, base_url: Optional[str] = None, api_version: Optional[str] = None) -> Dict[str, Any]:
        """Validate API key for any provider"""
        if provider == APIProvider.OPENAI:
            return await cls.validate_openai_key(api_key)
        elif provider == APIProvider.ANTHROPIC:
            return await cls.validate_anthropic_key(api_key)
        elif provider == APIProvider.AZURE:
            if not base_url:
                return {"valid": False, "provider": "azure", "message": "Azure endpoint URL is required"}
            return await cls.validate_azure_key(api_key, base_url, api_version or "2024-02-01")
        elif provider == APIProvider.OPENROUTER:
            return await cls.validate_openrouter_key(api_key)
        elif provider in [APIProvider.LOCAL, APIProvider.OLLAMA]:
            return await cls.validate_local_endpoint(base_url or "http://localhost:11434")
        else:
            return {"valid": False, "provider": provider.value, "message": "Unsupported provider"}

@dataclass
class APIConfig:
    """Configuration for API providers"""
    provider: APIProvider
    api_key: str
    base_url: Optional[str] = None
    model: str = "gpt-4"
    max_tokens: int = 2000
    temperature: float = 0.7
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit_rpm: int = 60  # requests per minute
    # Azure-specific parameters
    api_version: Optional[str] = None
    deployment_name: Optional[str] = None

@dataclass
class RateLimiter:
    """Rate limiter for API calls"""
    max_requests: int
    time_window: int = 60  # seconds
    requests: List[float] = field(default_factory=list)
    
    def can_make_request(self) -> bool:
        """Check if we can make a request within rate limits"""
        now = time.time()
        # Remove old requests outside the time window
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.time_window]
        return len(self.requests) < self.max_requests
    
    def record_request(self):
        """Record a new request"""
        self.requests.append(time.time())
    
    def time_until_next_request(self) -> float:
        """Get time to wait until next request is allowed"""
        if self.can_make_request():
            return 0.0
        
        now = time.time()
        oldest_request = min(self.requests)
        return self.time_window - (now - oldest_request)

class APIError(Exception):
    """Base API error"""
    def __init__(self, message: str, provider: APIProvider, retryable: bool = True):
        super().__init__(message)
        self.provider = provider
        self.retryable = retryable

class QuotaExceededError(APIError):
    """API quota exceeded error"""
    def __init__(self, provider: APIProvider):
        super().__init__(f"API quota exceeded for {provider.value}", provider, False)

class RateLimitError(APIError):
    """Rate limit exceeded error"""
    def __init__(self, provider: APIProvider, retry_after: float = 60):
        super().__init__(f"Rate limit exceeded for {provider.value}", provider, True)
        self.retry_after = retry_after

class APIManager:
    """Enhanced API manager with secure storage, multi-provider support and robust error handling"""

    def __init__(self, config_file: Optional[str] = None, storage_path: Optional[str] = None):
        self.providers: Dict[APIProvider, APIConfig] = {}
        self.clients: Dict[APIProvider, Any] = {}
        self.rate_limiters: Dict[APIProvider, RateLimiter] = {}
        self.fallback_order: List[APIProvider] = []
        self.secure_storage = SecureKeyStorage(storage_path)
        self.validator = APIKeyValidator()

        # Provider capabilities and features
        self.provider_capabilities: Dict[APIProvider, Dict[str, Any]] = {}
        self.provider_models: Dict[APIProvider, List[str]] = {}
        self.provider_status: Dict[APIProvider, Dict[str, Any]] = {}

        # Usage tracking and quotas
        self.usage_tracker: Dict[APIProvider, Dict[str, Any]] = {}
        self.quota_limits: Dict[APIProvider, Dict[str, Any]] = {}
        self.quota_warnings: Dict[APIProvider, List[float]] = {}  # Warning thresholds

        # Resilience and fallback
        self.retry_configs: Dict[APIProvider, Dict[str, Any]] = {}
        self.cache_storage: Dict[str, Any] = {}  # Simple in-memory cache
        self.degraded_mode: bool = False

        # Performance metrics
        self.performance_metrics: Dict[APIProvider, Dict[str, Any]] = {}

        # Resilience manager
        try:
            from api_resilience_enhanced import APIResilienceManager, RetryConfig, FallbackConfig
            self.resilience_manager = APIResilienceManager(
                retry_config=RetryConfig(max_retries=3, base_delay=1.0),
                fallback_config=FallbackConfig(
                    provider_fallback_order=[p.value for p in APIProvider],
                    enable_cache_fallback=True,
                    enable_degraded_mode=True
                )
            )
        except ImportError:
            logger.warning("Enhanced resilience manager not available, using basic fallback")
            self.resilience_manager = None

        # Load configuration
        self._load_configuration(config_file)
        self._initialize_provider_capabilities()
        self._initialize_clients()
        self._setup_rate_limiters()
        self._setup_usage_tracking()
        self._setup_retry_configs()

    async def set_api_key(self, provider: APIProvider, api_key: str, validate: bool = True) -> Dict[str, Any]:
        """Set and optionally validate an API key"""
        result = {"success": False, "message": ""}

        try:
            # Validate key if requested
            if validate:
                validation_result = await self.validator.validate_key(provider, api_key)
                if not validation_result["valid"]:
                    result["message"] = validation_result["message"]
                    return result
                result["validation"] = validation_result

            # Store key securely
            self.secure_storage.store_key(provider.value, api_key)

            # Update configuration
            if provider in self.providers:
                self.providers[provider].api_key = api_key
            else:
                # Create new config
                self.providers[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    model=self._get_default_model(provider)
                )

            # Reinitialize client
            await self._initialize_client(provider)

            result["success"] = True
            result["message"] = f"API key set successfully for {provider.value}"
            logger.info(f"API key configured for {provider.value}")

        except Exception as e:
            result["message"] = f"Failed to set API key: {e}"
            logger.error(f"Failed to set API key for {provider.value}: {e}")

        return result

    async def test_api_key(self, provider: APIProvider) -> Dict[str, Any]:
        """Test if an API key is working"""
        result = {"success": False, "message": ""}

        try:
            logger.info(f"Testing API key for provider: {provider.value}")

            # Get the API key
            api_key = self.get_api_key(provider)
            if not api_key:
                result["message"] = f"No API key found for {provider.value}"
                logger.warning(f"No API key found for {provider.value}")
                return result

            # Get additional config for Azure
            base_url = None
            api_version = None
            if provider == APIProvider.AZURE:
                config = self.providers.get(provider)
                if config:
                    base_url = config.base_url
                    api_version = config.api_version
                else:
                    base_url = os.getenv("AZURE_OPENAI_ENDPOINT")
                    api_version = os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01")

                if not base_url:
                    result["message"] = "Azure endpoint URL is required"
                    return result

            # Validate the key
            validation_result = await APIKeyValidator.validate_key(
                provider, api_key, base_url, api_version
            )

            if validation_result["valid"]:
                result["success"] = True
                result["message"] = validation_result["message"]
                result["validation"] = validation_result
                logger.info(f"API key test successful for {provider.value}")
            else:
                result["message"] = validation_result["message"]
                logger.warning(f"API key test failed for {provider.value}: {validation_result['message']}")

        except Exception as e:
            result["message"] = f"Error testing API key: {e}"
            logger.error(f"Error testing API key for {provider.value}: {e}")

        return result

    def get_api_key(self, provider: APIProvider) -> Optional[str]:
        """Get API key for a provider"""
        # First check secure storage
        stored_key = self.secure_storage.load_key(provider.value)
        if stored_key:
            return stored_key

        # Fallback to environment variables
        env_var_map = {
            APIProvider.OPENAI: "OPENAI_API_KEY",
            APIProvider.ANTHROPIC: "ANTHROPIC_API_KEY",
            APIProvider.AZURE: "AZURE_OPENAI_API_KEY",
            APIProvider.OPENROUTER: "OPENROUTER_API_KEY",
            APIProvider.LOCAL: "LOCAL_API_KEY",
            APIProvider.OLLAMA: "OLLAMA_API_KEY"
        }

        env_var = env_var_map.get(provider)
        if env_var:
            return os.getenv(env_var)

        return None

    def remove_api_key(self, provider: APIProvider) -> bool:
        """Remove an API key"""
        try:
            # Remove from secure storage
            removed = self.secure_storage.delete_key(provider.value)

            # Remove from active configuration
            if provider in self.providers:
                del self.providers[provider]

            if provider in self.clients:
                del self.clients[provider]

            if provider in self.rate_limiters:
                del self.rate_limiters[provider]

            logger.info(f"Removed API key for {provider.value}")
            return removed
        except Exception as e:
            logger.error(f"Failed to remove API key for {provider.value}: {e}")
            return False

    def list_configured_providers(self) -> List[Dict[str, Any]]:
        """List all configured providers with their status"""
        providers = []

        for provider in APIProvider:
            api_key = self.get_api_key(provider)
            has_key = bool(api_key)
            is_active = provider in self.providers

            providers.append({
                "provider": provider.value,
                "has_key": has_key,
                "is_active": is_active,
                "model": self.providers[provider].model if is_active else self._get_default_model(provider)
            })

        return providers

    def _get_default_model(self, provider: APIProvider) -> str:
        """Get default model for a provider"""
        defaults = {
            APIProvider.OPENAI: "gpt-4",
            APIProvider.ANTHROPIC: "claude-3-sonnet-20240229",
            APIProvider.AZURE: "gpt-35-turbo",
            APIProvider.OPENROUTER: "openai/gpt-3.5-turbo",
            APIProvider.LOCAL: "llama2",
            APIProvider.OLLAMA: "llama2"
        }
        return defaults.get(provider, "unknown")
    
    def _load_configuration(self, config_file: Optional[str] = None) -> None:
        """
        Load API configuration from secure storage, file, or environment variables.

        Args:
            config_file: Optional path to JSON configuration file

        Note:
            Configuration is loaded in the following priority:
            1. Secure storage (encrypted keys)
            2. Configuration file (if provided and exists)
            3. Environment variables
            4. Default values
        """

        # Load stored keys
        stored_keys = self.secure_storage.load_all_keys()

        # Default configurations with secure storage integration
        default_configs = {}

        for provider in APIProvider:
            # Get API key from secure storage first, then environment
            api_key = stored_keys.get(provider.value) or self.get_api_key(provider) or ""

            if provider == APIProvider.OPENAI:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    model=os.getenv("OPENAI_MODEL", "gpt-4"),
                    max_tokens=int(os.getenv("OPENAI_MAX_TOKENS", "2000")),
                    rate_limit_rpm=int(os.getenv("OPENAI_RATE_LIMIT", "60"))
                )
            elif provider == APIProvider.ANTHROPIC:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    model=os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229"),
                    max_tokens=int(os.getenv("ANTHROPIC_MAX_TOKENS", "2000")),
                    rate_limit_rpm=int(os.getenv("ANTHROPIC_RATE_LIMIT", "60"))
                )
            elif provider == APIProvider.AZURE:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    base_url=os.getenv("AZURE_OPENAI_ENDPOINT", ""),
                    model=os.getenv("AZURE_OPENAI_MODEL", "gpt-35-turbo"),
                    max_tokens=int(os.getenv("AZURE_MAX_TOKENS", "2000")),
                    rate_limit_rpm=int(os.getenv("AZURE_RATE_LIMIT", "60")),
                    api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01"),
                    deployment_name=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-35-turbo")
                )
            elif provider == APIProvider.OPENROUTER:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    base_url=os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
                    model=os.getenv("OPENROUTER_MODEL", "openai/gpt-3.5-turbo"),
                    max_tokens=int(os.getenv("OPENROUTER_MAX_TOKENS", "2000")),
                    rate_limit_rpm=int(os.getenv("OPENROUTER_RATE_LIMIT", "60"))
                )
            elif provider == APIProvider.LOCAL:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key or "local",
                    base_url=os.getenv("LOCAL_API_URL", "http://localhost:11434"),
                    model=os.getenv("LOCAL_MODEL", "llama2"),
                    rate_limit_rpm=int(os.getenv("LOCAL_RATE_LIMIT", "120"))
                )
            elif provider == APIProvider.OLLAMA:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key or "ollama",
                    base_url=os.getenv("OLLAMA_API_URL", "http://localhost:11434"),
                    model=os.getenv("OLLAMA_MODEL", "llama2"),
                    rate_limit_rpm=int(os.getenv("OLLAMA_RATE_LIMIT", "120"))
                )
        
        # Load from config file if provided
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    # Update default configs with file configs
                    for provider_name, config_data in file_config.items():
                        provider = APIProvider(provider_name)
                        if provider in default_configs:
                            # Update existing config
                            for key, value in config_data.items():
                                if hasattr(default_configs[provider], key):
                                    setattr(default_configs[provider], key, value)
            except Exception as e:
                logger.warning(f"Failed to load config file {config_file}: {e}")
        
        # Only add providers with valid API keys
        for provider, config in default_configs.items():
            if config.api_key and config.api_key != "":
                self.providers[provider] = config
                logger.info(f"Configured API provider: {provider.value}")
        
        # Set fallback order based on available providers
        self.fallback_order = [
            APIProvider.OPENAI,
            APIProvider.AZURE,
            APIProvider.OPENROUTER,
            APIProvider.ANTHROPIC,
            APIProvider.LOCAL
        ]
        self.fallback_order = [p for p in self.fallback_order if p in self.providers]
        
        if not self.providers:
            logger.warning("No API providers configured with valid keys")

    def _initialize_provider_capabilities(self):
        """Initialize provider capabilities and supported features"""
        self.provider_capabilities = {
            APIProvider.OPENAI: {
                "supports_streaming": True,
                "supports_function_calling": True,
                "supports_vision": True,
                "supports_embeddings": True,
                "max_tokens": 128000,  # GPT-4 Turbo
                "context_window": 128000,
                "supports_json_mode": True,
                "supports_system_messages": True,
                "rate_limit_rpm": 10000,  # Requests per minute
                "rate_limit_tpm": 2000000,  # Tokens per minute
            },
            APIProvider.ANTHROPIC: {
                "supports_streaming": True,
                "supports_function_calling": True,
                "supports_vision": True,
                "supports_embeddings": False,
                "max_tokens": 200000,  # Claude-3
                "context_window": 200000,
                "supports_json_mode": False,
                "supports_system_messages": True,
                "rate_limit_rpm": 5000,
                "rate_limit_tpm": 1000000,
            },
            APIProvider.AZURE: {
                "supports_streaming": True,
                "supports_function_calling": True,
                "supports_vision": True,
                "supports_embeddings": True,
                "max_tokens": 128000,
                "context_window": 128000,
                "supports_json_mode": True,
                "supports_system_messages": True,
                "rate_limit_rpm": 10000,
                "rate_limit_tpm": 2000000,
            },
            APIProvider.OPENROUTER: {
                "supports_streaming": True,
                "supports_function_calling": True,
                "supports_vision": True,
                "supports_embeddings": False,
                "max_tokens": 200000,  # Varies by model
                "context_window": 200000,
                "supports_json_mode": True,
                "supports_system_messages": True,
                "rate_limit_rpm": 200,  # Conservative default
                "rate_limit_tpm": 40000,
            },
            APIProvider.LOCAL: {
                "supports_streaming": True,
                "supports_function_calling": False,
                "supports_vision": False,
                "supports_embeddings": False,
                "max_tokens": 8192,  # Conservative default
                "context_window": 8192,
                "supports_json_mode": False,
                "supports_system_messages": True,
                "rate_limit_rpm": 1000,
                "rate_limit_tpm": 100000,
            },
            APIProvider.OLLAMA: {
                "supports_streaming": True,
                "supports_function_calling": False,
                "supports_vision": False,
                "supports_embeddings": True,
                "max_tokens": 8192,
                "context_window": 8192,
                "supports_json_mode": False,
                "supports_system_messages": True,
                "rate_limit_rpm": 1000,
                "rate_limit_tpm": 100000,
            }
        }

        # Initialize supported models for each provider
        self.provider_models = {
            APIProvider.OPENAI: [
                "gpt-4-turbo-preview", "gpt-4-turbo", "gpt-4", "gpt-4-32k",
                "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct"
            ],
            APIProvider.ANTHROPIC: [
                "claude-3-opus-20240229", "claude-3-sonnet-20240229",
                "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "claude-instant-1.2"
            ],
            APIProvider.AZURE: [
                "gpt-4", "gpt-4-32k", "gpt-35-turbo", "gpt-35-turbo-16k"
            ],
            APIProvider.OPENROUTER: [
                "openai/gpt-4-turbo-preview", "openai/gpt-4", "openai/gpt-3.5-turbo",
                "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku",
                "meta-llama/llama-2-70b-chat", "mistralai/mixtral-8x7b-instruct"
            ],
            APIProvider.LOCAL: [
                "llama2", "codellama", "mistral", "neural-chat", "custom"
            ],
            APIProvider.OLLAMA: [
                "llama2", "llama2:13b", "llama2:70b", "codellama", "mistral",
                "neural-chat", "starling-lm", "dolphin-mixtral"
            ]
        }

        # Initialize provider status
        for provider in APIProvider:
            self.provider_status[provider] = {
                "available": False,
                "last_check": None,
                "error_count": 0,
                "success_count": 0,
                "average_response_time": 0.0,
                "last_error": None
            }

    def _initialize_clients(self):
        """Initialize API clients for each provider"""
        for provider in list(self.providers.keys()):
            try:
                # Try to get running event loop
                loop = asyncio.get_running_loop()
                loop.create_task(self._initialize_client(provider))
            except RuntimeError:
                # No running event loop, skip async initialization
                # Clients will be initialized on first use
                pass

    async def _initialize_client(self, provider: APIProvider):
        """Initialize API client for a specific provider"""
        if provider not in self.providers:
            return

        config = self.providers[provider]
        try:
            if provider == APIProvider.OPENAI:
                self.clients[provider] = AsyncOpenAI(
                    api_key=config.api_key,
                    base_url=config.base_url,
                    timeout=config.timeout
                )
            elif provider == APIProvider.ANTHROPIC:
                self.clients[provider] = AsyncAnthropic(
                    api_key=config.api_key,
                    timeout=config.timeout
                )
            elif provider == APIProvider.AZURE:
                from openai import AsyncAzureOpenAI
                self.clients[provider] = AsyncAzureOpenAI(
                    api_key=config.api_key,
                    azure_endpoint=config.base_url,
                    api_version=config.api_version or "2024-02-01",
                    timeout=config.timeout
                )
            elif provider == APIProvider.OPENROUTER:
                # OpenRouter uses OpenAI-compatible API
                self.clients[provider] = AsyncOpenAI(
                    api_key=config.api_key,
                    base_url=config.base_url or "https://openrouter.ai/api/v1",
                    timeout=config.timeout
                )
            elif provider in [APIProvider.LOCAL, APIProvider.OLLAMA]:
                # For local/Ollama, we'll use aiohttp
                self.clients[provider] = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=config.timeout)
                )

            logger.info(f"Initialized client for {provider.value}")
        except Exception as e:
            logger.error(f"Failed to initialize client for {provider.value}: {e}")
            # Remove provider if client initialization fails
            if provider in self.providers:
                del self.providers[provider]

    def _setup_usage_tracking(self):
        """Setup usage tracking for all providers"""
        for provider in self.providers:
            self.usage_tracker[provider] = {
                "total_requests": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "daily_requests": 0,
                "daily_tokens": 0,
                "daily_cost": 0.0,
                "last_reset": datetime.now().date(),
                "request_history": [],  # Last 100 requests
                "error_history": []     # Last 100 errors
            }

            # Set default quota limits (can be overridden by configuration)
            self.quota_limits[provider] = {
                "daily_requests": 10000,
                "daily_tokens": 1000000,
                "daily_cost": 100.0,
                "monthly_requests": 300000,
                "monthly_tokens": 30000000,
                "monthly_cost": 3000.0
            }

            # Set warning thresholds (80%, 90%, 95%)
            self.quota_warnings[provider] = [0.8, 0.9, 0.95]

    def _setup_retry_configs(self):
        """Setup retry configurations for all providers"""
        for provider in self.providers:
            self.retry_configs[provider] = {
                "max_retries": 3,
                "base_delay": 1.0,  # seconds
                "max_delay": 60.0,  # seconds
                "exponential_base": 2.0,
                "jitter": True,
                "retry_on_status": [429, 500, 502, 503, 504],
                "retry_on_exceptions": [
                    "ConnectionError", "TimeoutError", "RateLimitError"
                ]
            }

            # Provider-specific adjustments
            if provider == APIProvider.OPENROUTER:
                self.retry_configs[provider]["max_retries"] = 5
                self.retry_configs[provider]["base_delay"] = 2.0
            elif provider in [APIProvider.LOCAL, APIProvider.OLLAMA]:
                self.retry_configs[provider]["max_retries"] = 2
                self.retry_configs[provider]["base_delay"] = 0.5

    def get_provider_capabilities(self, provider: APIProvider) -> Dict[str, Any]:
        """Get capabilities for a specific provider"""
        return self.provider_capabilities.get(provider, {})

    def get_supported_models(self, provider: APIProvider) -> List[str]:
        """Get supported models for a specific provider"""
        return self.provider_models.get(provider, [])

    def get_provider_status(self, provider: APIProvider) -> Dict[str, Any]:
        """Get current status for a specific provider"""
        return self.provider_status.get(provider, {})

    def get_usage_stats(self, provider: APIProvider) -> Dict[str, Any]:
        """Get usage statistics for a specific provider"""
        return self.usage_tracker.get(provider, {})

    def check_quota_status(self, provider: APIProvider) -> Dict[str, Any]:
        """Check quota status and return warnings if needed"""
        usage = self.usage_tracker.get(provider, {})
        limits = self.quota_limits.get(provider, {})
        warnings = self.quota_warnings.get(provider, [])

        status = {
            "provider": provider.value,
            "within_limits": True,
            "warnings": [],
            "usage_percentage": {},
            "remaining": {},
            "projected_exhaustion": {},
            "recommendations": []
        }

        # Check daily limits
        for metric in ["daily_requests", "daily_tokens", "daily_cost"]:
            if metric in usage and metric in limits:
                used = usage[metric]
                limit = limits[metric]
                percentage = (used / limit) * 100 if limit > 0 else 0

                status["usage_percentage"][metric] = percentage
                status["remaining"][metric] = max(0, limit - used)

                # Calculate projected exhaustion time
                if used > 0:
                    usage_rate = self._calculate_usage_rate(provider, metric)
                    if usage_rate > 0:
                        remaining = limit - used
                        hours_until_exhaustion = remaining / usage_rate
                        status["projected_exhaustion"][metric] = hours_until_exhaustion

                # Check warning thresholds
                for threshold in warnings:
                    if percentage >= threshold * 100:
                        warning = {
                            "metric": metric,
                            "threshold": threshold * 100,
                            "current": percentage,
                            "message": f"{metric} usage at {percentage:.1f}% of limit"
                        }

                        # Add time-based warning if projection available
                        if metric in status["projected_exhaustion"]:
                            hours = status["projected_exhaustion"][metric]
                            if hours < 24:
                                warning["urgency"] = "high"
                                warning["message"] += f" (exhausted in {hours:.1f} hours)"
                            elif hours < 72:
                                warning["urgency"] = "medium"
                                warning["message"] += f" (exhausted in {hours/24:.1f} days)"

                        status["warnings"].append(warning)

                # Check if over limit
                if used >= limit:
                    status["within_limits"] = False

                # Add recommendations
                if percentage > 90:
                    status["recommendations"].append(f"Consider switching to alternative provider for {metric}")
                elif percentage > 80:
                    status["recommendations"].append(f"Monitor {metric} usage closely")

        return status

    def _calculate_usage_rate(self, provider: APIProvider, metric: str) -> float:
        """Calculate usage rate per hour for a metric"""
        usage = self.usage_tracker.get(provider, {})
        history = usage.get("request_history", [])

        if len(history) < 2:
            return 0.0

        # Calculate rate based on recent history
        recent_history = history[-10:]  # Last 10 requests
        if len(recent_history) < 2:
            return 0.0

        # Get time span and usage for recent requests
        first_time = datetime.fromisoformat(recent_history[0]["timestamp"])
        last_time = datetime.fromisoformat(recent_history[-1]["timestamp"])
        time_span_hours = (last_time - first_time).total_seconds() / 3600

        if time_span_hours <= 0:
            return 0.0

        # Calculate usage in the time span
        if metric == "daily_requests":
            usage_amount = len(recent_history)
        elif metric == "daily_tokens":
            usage_amount = sum(req.get("tokens", 0) for req in recent_history)
        elif metric == "daily_cost":
            # Rough cost estimation (would need actual pricing data)
            usage_amount = sum(req.get("tokens", 0) for req in recent_history) * 0.0001
        else:
            return 0.0

        return usage_amount / time_span_hours

    async def get_quota_recommendations(self, provider: APIProvider) -> Dict[str, Any]:
        """Get recommendations for quota management"""
        status = self.check_quota_status(provider)
        capabilities = self.get_provider_capabilities(provider)

        recommendations = {
            "provider": provider.value,
            "current_status": status,
            "recommendations": [],
            "alternative_providers": [],
            "cost_optimization": []
        }

        # Check if provider switching is recommended
        if not status["within_limits"] or any(w.get("urgency") == "high" for w in status["warnings"]):
            # Find alternative providers
            for alt_provider in self.providers:
                if alt_provider != provider:
                    alt_status = self.check_quota_status(alt_provider)
                    if alt_status["within_limits"]:
                        recommendations["alternative_providers"].append({
                            "provider": alt_provider.value,
                            "available_quota": alt_status["remaining"],
                            "capabilities": self.get_provider_capabilities(alt_provider)
                        })

        # Cost optimization suggestions
        if "daily_cost" in status["usage_percentage"]:
            cost_percentage = status["usage_percentage"]["daily_cost"]
            if cost_percentage > 70:
                recommendations["cost_optimization"].extend([
                    "Consider using smaller models for simple tasks",
                    "Implement response caching to reduce API calls",
                    "Use local models for development and testing"
                ])

        # Performance optimization
        if "daily_requests" in status["usage_percentage"]:
            request_percentage = status["usage_percentage"]["daily_requests"]
            if request_percentage > 80:
                recommendations["recommendations"].extend([
                    "Implement request batching where possible",
                    "Add request deduplication",
                    "Consider increasing rate limits if available"
                ])

        return recommendations

    async def auto_switch_provider(self, current_provider: APIProvider,
                                 required_capabilities: List[str] = None) -> Optional[APIProvider]:
        """Automatically switch to best available provider based on quota and capabilities"""
        required_capabilities = required_capabilities or []

        # Get current provider status
        current_status = self.check_quota_status(current_provider)

        # If current provider is fine, no need to switch
        if current_status["within_limits"] and not any(w.get("urgency") == "high" for w in current_status["warnings"]):
            return current_provider

        # Find best alternative provider
        best_provider = None
        best_score = -1

        for provider in self.providers:
            if provider == current_provider:
                continue

            # Check quota status
            status = self.check_quota_status(provider)
            if not status["within_limits"]:
                continue

            # Check capabilities
            capabilities = self.get_provider_capabilities(provider)
            capability_score = 0

            for required_cap in required_capabilities:
                if capabilities.get(required_cap, False):
                    capability_score += 1

            # Calculate overall score
            quota_score = 100 - max(status["usage_percentage"].values()) if status["usage_percentage"] else 100
            overall_score = (quota_score * 0.7) + (capability_score * 0.3)

            if overall_score > best_score:
                best_score = overall_score
                best_provider = provider

        if best_provider:
            logger.info(f"Auto-switching from {current_provider.value} to {best_provider.value} (score: {best_score:.1f})")

        return best_provider

    async def generate_usage_report(self, provider: Optional[APIProvider] = None,
                                  time_period: str = "daily") -> Dict[str, Any]:
        """Generate comprehensive usage report"""
        providers_to_report = [provider] if provider else list(self.providers.keys())

        report = {
            "report_generated": datetime.now().isoformat(),
            "time_period": time_period,
            "providers": {},
            "summary": {
                "total_requests": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "most_used_provider": None,
                "cost_breakdown": {},
                "efficiency_metrics": {}
            }
        }

        for prov in providers_to_report:
            usage = self.usage_tracker.get(prov, {})
            limits = self.quota_limits.get(prov, {})

            # Get time-period specific data
            if time_period == "daily":
                requests = usage.get("daily_requests", 0)
                tokens = usage.get("daily_tokens", 0)
                cost = usage.get("daily_cost", 0.0)
            else:  # monthly or total
                requests = usage.get("total_requests", 0)
                tokens = usage.get("total_tokens", 0)
                cost = usage.get("total_cost", 0.0)

            provider_report = {
                "requests": requests,
                "tokens": tokens,
                "cost": cost,
                "limits": limits,
                "utilization": {},
                "efficiency": {},
                "trends": {}
            }

            # Calculate utilization percentages
            for metric in ["requests", "tokens", "cost"]:
                limit_key = f"{time_period}_{metric}" if time_period == "daily" else f"monthly_{metric}"
                if limit_key in limits and limits[limit_key] > 0:
                    utilization = (provider_report[metric] / limits[limit_key]) * 100
                    provider_report["utilization"][metric] = utilization

            # Calculate efficiency metrics
            if requests > 0:
                provider_report["efficiency"]["tokens_per_request"] = tokens / requests
                provider_report["efficiency"]["cost_per_request"] = cost / requests

            if tokens > 0:
                provider_report["efficiency"]["cost_per_token"] = cost / tokens

            # Add trend analysis
            provider_report["trends"] = self._calculate_usage_trends(prov)

            report["providers"][prov.value] = provider_report

            # Update summary
            report["summary"]["total_requests"] += requests
            report["summary"]["total_tokens"] += tokens
            report["summary"]["total_cost"] += cost
            report["summary"]["cost_breakdown"][prov.value] = cost

        # Determine most used provider
        if report["summary"]["cost_breakdown"]:
            most_used = max(report["summary"]["cost_breakdown"].items(), key=lambda x: x[1])
            report["summary"]["most_used_provider"] = most_used[0]

        # Calculate overall efficiency
        if report["summary"]["total_requests"] > 0:
            report["summary"]["efficiency_metrics"]["avg_tokens_per_request"] = (
                report["summary"]["total_tokens"] / report["summary"]["total_requests"]
            )
            report["summary"]["efficiency_metrics"]["avg_cost_per_request"] = (
                report["summary"]["total_cost"] / report["summary"]["total_requests"]
            )

        return report

    def _calculate_usage_trends(self, provider: APIProvider) -> Dict[str, Any]:
        """Calculate usage trends for a provider"""
        usage = self.usage_tracker.get(provider, {})
        history = usage.get("request_history", [])

        if len(history) < 5:
            return {"trend": "insufficient_data"}

        # Analyze recent vs older requests
        recent_requests = history[-5:]
        older_requests = history[-10:-5] if len(history) >= 10 else history[:-5]

        if not older_requests:
            return {"trend": "insufficient_data"}

        # Calculate average tokens for recent vs older
        recent_avg_tokens = sum(req.get("tokens", 0) for req in recent_requests) / len(recent_requests)
        older_avg_tokens = sum(req.get("tokens", 0) for req in older_requests) / len(older_requests)

        # Determine trend
        if recent_avg_tokens > older_avg_tokens * 1.1:
            trend = "increasing"
        elif recent_avg_tokens < older_avg_tokens * 0.9:
            trend = "decreasing"
        else:
            trend = "stable"

        return {
            "trend": trend,
            "recent_avg_tokens": recent_avg_tokens,
            "older_avg_tokens": older_avg_tokens,
            "change_percentage": ((recent_avg_tokens - older_avg_tokens) / older_avg_tokens * 100) if older_avg_tokens > 0 else 0
        }

    async def project_usage(self, provider: APIProvider, days_ahead: int = 7) -> Dict[str, Any]:
        """Project future usage based on current trends"""
        usage = self.usage_tracker.get(provider, {})
        limits = self.quota_limits.get(provider, {})

        # Calculate current daily rates
        daily_request_rate = self._calculate_usage_rate(provider, "daily_requests")
        daily_token_rate = self._calculate_usage_rate(provider, "daily_tokens")
        daily_cost_rate = self._calculate_usage_rate(provider, "daily_cost")

        projection = {
            "provider": provider.value,
            "projection_days": days_ahead,
            "current_rates": {
                "requests_per_hour": daily_request_rate,
                "tokens_per_hour": daily_token_rate,
                "cost_per_hour": daily_cost_rate
            },
            "projected_usage": {},
            "projected_limits_reached": {},
            "recommendations": []
        }

        # Project usage for the specified period
        hours_ahead = days_ahead * 24
        projection["projected_usage"] = {
            "requests": daily_request_rate * hours_ahead,
            "tokens": daily_token_rate * hours_ahead,
            "cost": daily_cost_rate * hours_ahead
        }

        # Check when limits would be reached
        for metric in ["daily_requests", "daily_tokens", "daily_cost"]:
            if metric in limits and limits[metric] > 0:
                current_usage = usage.get(metric, 0)
                remaining = limits[metric] - current_usage

                rate_key = metric.replace("daily_", "") + "_per_hour"
                if rate_key in projection["current_rates"] and projection["current_rates"][rate_key] > 0:
                    hours_until_limit = remaining / projection["current_rates"][rate_key]
                    projection["projected_limits_reached"][metric] = {
                        "hours_until_limit": hours_until_limit,
                        "date_reached": (datetime.now() + timedelta(hours=hours_until_limit)).isoformat()
                    }

        # Generate recommendations
        for metric, limit_info in projection["projected_limits_reached"].items():
            hours = limit_info["hours_until_limit"]
            if hours < 24:
                projection["recommendations"].append(f"URGENT: {metric} limit will be reached in {hours:.1f} hours")
            elif hours < 72:
                projection["recommendations"].append(f"WARNING: {metric} limit will be reached in {hours/24:.1f} days")

        return projection

    async def generate_text(self, messages: List[Dict[str, str]],
                          provider: Optional[APIProvider] = None,
                          model: Optional[str] = None,
                          max_tokens: Optional[int] = None,
                          temperature: Optional[float] = None,
                          stream: bool = False,
                          **kwargs) -> Dict[str, Any]:
        """
        Unified text generation interface across all providers with comprehensive resilience

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            provider: Specific provider to use (if None, uses fallback order)
            model: Specific model to use
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            stream: Whether to stream the response
            **kwargs: Additional provider-specific parameters

        Returns:
            Dictionary with response data and metadata
        """
        # Generate cache key for this request
        cache_key = self._generate_cache_key(messages, model, max_tokens, temperature)

        # Use resilience manager if available
        if self.resilience_manager:
            return await self.resilience_manager.execute_with_resilience(
                operation=self._generate_text_with_resilience,
                operation_args=(messages,),
                operation_kwargs={
                    "provider": provider,
                    "model": model,
                    "max_tokens": max_tokens,
                    "temperature": temperature,
                    "stream": stream,
                    **kwargs
                },
                cache_key=cache_key,
                provider=provider.value if provider else None,
                model=model
            )
        else:
            # Fallback to basic implementation
            return await self._generate_text_basic(
                messages, provider, model, max_tokens, temperature, stream, **kwargs
            )

    async def _generate_text_with_resilience(self, messages: List[Dict[str, str]],
                                           provider: Optional[APIProvider] = None,
                                           model: Optional[str] = None,
                                           max_tokens: Optional[int] = None,
                                           temperature: Optional[float] = None,
                                           stream: bool = False,
                                           **kwargs) -> Dict[str, Any]:
        """Generate text with a single provider (used by resilience manager)"""
        # Determine provider to use
        if provider:
            current_provider = provider
        elif self.fallback_order:
            current_provider = self.fallback_order[0]
        else:
            raise Exception("No providers available")

        if current_provider not in self.providers:
            raise Exception(f"Provider {current_provider.value} not configured")

        # Check quota status
        quota_status = self.check_quota_status(current_provider)
        if not quota_status["within_limits"]:
            raise Exception(f"Provider {current_provider.value} over quota")

        # Check rate limits
        rate_limiter = self.rate_limiters.get(current_provider)
        if rate_limiter and not rate_limiter.can_make_request():
            wait_time = rate_limiter.time_until_next_request()
            if wait_time > 0:
                await asyncio.sleep(wait_time)

        # Generate text with the provider
        result = await self._generate_text_with_provider(
            current_provider, messages, model, max_tokens,
            temperature, stream, **kwargs
        )

        # Record successful request
        if rate_limiter:
            rate_limiter.record_request()

        # Update usage tracking
        await self._update_usage_tracking(current_provider, result)

        # Add metadata
        if "metadata" not in result:
            result["metadata"] = {}

        result["metadata"].update({
            "provider": current_provider.value,
            "model": result.get("model", model),
            "tokens_used": result.get("usage", {}).get("total_tokens", 0),
            "resilience_used": True
        })

        return result

    async def _generate_text_basic(self, messages: List[Dict[str, str]],
                                 provider: Optional[APIProvider] = None,
                                 model: Optional[str] = None,
                                 max_tokens: Optional[int] = None,
                                 temperature: Optional[float] = None,
                                 stream: bool = False,
                                 **kwargs) -> Dict[str, Any]:
        """Basic text generation without resilience manager (fallback)"""
        start_time = time.time()

        # Determine providers to try
        providers_to_try = [provider] if provider else self.fallback_order

        last_error = None

        for current_provider in providers_to_try:
            if current_provider not in self.providers:
                continue

            try:
                # Check quota status
                quota_status = self.check_quota_status(current_provider)
                if not quota_status["within_limits"]:
                    logger.warning(f"Provider {current_provider.value} over quota, trying next provider")
                    continue

                # Check rate limits
                rate_limiter = self.rate_limiters.get(current_provider)
                if rate_limiter and not rate_limiter.can_make_request():
                    wait_time = rate_limiter.time_until_next_request()
                    if wait_time > 0:
                        logger.info(f"Rate limited for {current_provider.value}, waiting {wait_time:.2f}s")
                        await asyncio.sleep(wait_time)

                # Generate text with the current provider
                result = await self._generate_text_with_provider(
                    current_provider, messages, model, max_tokens,
                    temperature, stream, **kwargs
                )

                # Record successful request
                if rate_limiter:
                    rate_limiter.record_request()

                # Update usage tracking
                await self._update_usage_tracking(current_provider, result)

                # Update performance metrics
                response_time = time.time() - start_time
                await self._update_performance_metrics(current_provider, response_time, True)

                # Add metadata
                result["metadata"] = {
                    "provider": current_provider.value,
                    "model": result.get("model", model),
                    "response_time": response_time,
                    "tokens_used": result.get("usage", {}).get("total_tokens", 0),
                    "fallback_used": provider != current_provider if provider else False,
                    "resilience_used": False
                }

                return result

            except Exception as e:
                last_error = e
                logger.warning(f"Provider {current_provider.value} failed: {e}")

                # Update performance metrics for failure
                response_time = time.time() - start_time
                await self._update_performance_metrics(current_provider, response_time, False)

                # Check if error is retryable
                if not self._is_retryable_error(e):
                    break

                continue

        # All providers failed
        return {
            "success": False,
            "error": f"All providers failed. Last error: {last_error}",
            "content": "I apologize, but I'm currently unable to process your request due to technical difficulties. Please try again later.",
            "metadata": {
                "degraded_mode": True,
                "providers_tried": [p.value for p in providers_to_try],
                "last_error": str(last_error),
                "resilience_used": False
            }
        }

    async def _generate_text_with_provider(self, provider: APIProvider, messages: List[Dict[str, str]],
                                         model: Optional[str] = None, max_tokens: Optional[int] = None,
                                         temperature: Optional[float] = None, stream: bool = False,
                                         **kwargs) -> Dict[str, Any]:
        """Generate text using a specific provider"""
        config = self.providers[provider]
        client = self.clients[provider]

        # Use config defaults if not specified
        model = model or config.model
        max_tokens = max_tokens or config.max_tokens
        temperature = temperature if temperature is not None else config.temperature

        try:
            if provider == APIProvider.OPENAI:
                return await self._generate_openai(client, messages, model, max_tokens, temperature, stream, **kwargs)
            elif provider == APIProvider.ANTHROPIC:
                return await self._generate_anthropic(client, messages, model, max_tokens, temperature, stream, **kwargs)
            elif provider == APIProvider.AZURE:
                return await self._generate_azure(client, messages, model, max_tokens, temperature, stream, **kwargs)
            elif provider == APIProvider.OPENROUTER:
                return await self._generate_openrouter(client, messages, model, max_tokens, temperature, stream, **kwargs)
            elif provider in [APIProvider.LOCAL, APIProvider.OLLAMA]:
                return await self._generate_local(client, messages, model, max_tokens, temperature, stream, **kwargs)
            else:
                raise ValueError(f"Unsupported provider: {provider}")

        except Exception as e:
            logger.error(f"Text generation failed for {provider.value}: {e}")
            raise

    async def _generate_openai(self, client, messages: List[Dict[str, str]], model: str,
                             max_tokens: int, temperature: float, stream: bool, **kwargs) -> Dict[str, Any]:
        """Generate text using OpenAI API"""
        try:
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream,
                **kwargs
            )

            if stream:
                # Handle streaming response
                content = ""
                async for chunk in response:
                    if chunk.choices[0].delta.content:
                        content += chunk.choices[0].delta.content

                return {
                    "success": True,
                    "content": content,
                    "model": model,
                    "usage": {"total_tokens": len(content.split()) * 1.3}  # Rough estimate
                }
            else:
                return {
                    "success": True,
                    "content": response.choices[0].message.content,
                    "model": response.model,
                    "usage": response.usage.model_dump() if response.usage else {}
                }

        except Exception as e:
            logger.error(f"OpenAI generation failed: {e}")
            raise

    async def _generate_anthropic(self, client, messages: List[Dict[str, str]], model: str,
                                max_tokens: int, temperature: float, stream: bool, **kwargs) -> Dict[str, Any]:
        """Generate text using Anthropic API"""
        try:
            # Convert messages format for Anthropic
            anthropic_messages = []
            system_message = None

            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    anthropic_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            request_params = {
                "model": model,
                "messages": anthropic_messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream
            }

            if system_message:
                request_params["system"] = system_message

            response = await client.messages.create(**request_params)

            if stream:
                # Handle streaming response
                content = ""
                async for chunk in response:
                    if hasattr(chunk, 'delta') and hasattr(chunk.delta, 'text'):
                        content += chunk.delta.text

                return {
                    "success": True,
                    "content": content,
                    "model": model,
                    "usage": {"total_tokens": len(content.split()) * 1.3}  # Rough estimate
                }
            else:
                return {
                    "success": True,
                    "content": response.content[0].text,
                    "model": response.model,
                    "usage": {
                        "input_tokens": response.usage.input_tokens,
                        "output_tokens": response.usage.output_tokens,
                        "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                    }
                }

        except Exception as e:
            logger.error(f"Anthropic generation failed: {e}")
            raise

    async def _generate_azure(self, client, messages: List[Dict[str, str]], model: str,
                            max_tokens: int, temperature: float, stream: bool, **kwargs) -> Dict[str, Any]:
        """Generate text using Azure OpenAI API"""
        try:
            # Azure uses the same interface as OpenAI
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream,
                **kwargs
            )

            if stream:
                content = ""
                async for chunk in response:
                    if chunk.choices[0].delta.content:
                        content += chunk.choices[0].delta.content

                return {
                    "success": True,
                    "content": content,
                    "model": model,
                    "usage": {"total_tokens": len(content.split()) * 1.3}
                }
            else:
                return {
                    "success": True,
                    "content": response.choices[0].message.content,
                    "model": response.model,
                    "usage": response.usage.model_dump() if response.usage else {}
                }

        except Exception as e:
            logger.error(f"Azure generation failed: {e}")
            raise

    async def _generate_openrouter(self, client, messages: List[Dict[str, str]], model: str,
                                 max_tokens: int, temperature: float, stream: bool, **kwargs) -> Dict[str, Any]:
        """Generate text using OpenRouter API"""
        try:
            # OpenRouter uses OpenAI-compatible interface
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream,
                **kwargs
            )

            if stream:
                content = ""
                async for chunk in response:
                    if chunk.choices[0].delta.content:
                        content += chunk.choices[0].delta.content

                return {
                    "success": True,
                    "content": content,
                    "model": model,
                    "usage": {"total_tokens": len(content.split()) * 1.3}
                }
            else:
                return {
                    "success": True,
                    "content": response.choices[0].message.content,
                    "model": response.model,
                    "usage": response.usage.model_dump() if response.usage else {}
                }

        except Exception as e:
            logger.error(f"OpenRouter generation failed: {e}")
            raise

    async def _generate_local(self, client, messages: List[Dict[str, str]], model: str,
                            max_tokens: int, temperature: float, stream: bool, **kwargs) -> Dict[str, Any]:
        """Generate text using Local/Ollama API"""
        try:
            # Convert messages to prompt for local models
            prompt = self._messages_to_prompt(messages)

            # Prepare request for Ollama API
            request_data = {
                "model": model,
                "prompt": prompt,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": temperature
                },
                "stream": stream
            }

            config = self.providers[APIProvider.LOCAL] if APIProvider.LOCAL in self.providers else self.providers[APIProvider.OLLAMA]
            base_url = config.base_url or "http://localhost:11434"

            async with client.post(f"{base_url}/api/generate", json=request_data) as response:
                if response.status != 200:
                    raise Exception(f"Local API returned status {response.status}")

                if stream:
                    content = ""
                    async for line in response.content:
                        if line:
                            data = json.loads(line.decode())
                            if "response" in data:
                                content += data["response"]

                    return {
                        "success": True,
                        "content": content,
                        "model": model,
                        "usage": {"total_tokens": len(content.split()) * 1.3}
                    }
                else:
                    data = await response.json()
                    return {
                        "success": True,
                        "content": data.get("response", ""),
                        "model": model,
                        "usage": {"total_tokens": len(data.get("response", "").split()) * 1.3}
                    }

        except Exception as e:
            logger.error(f"Local generation failed: {e}")
            raise

    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert messages format to a single prompt for local models"""
        prompt_parts = []

        for message in messages:
            role = message["role"]
            content = message["content"]

            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"Human: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")

        prompt_parts.append("Assistant:")
        return "\n\n".join(prompt_parts)

    def _generate_cache_key(self, messages: List[Dict[str, str]], model: Optional[str],
                          max_tokens: Optional[int], temperature: Optional[float]) -> str:
        """Generate cache key for request"""
        import hashlib

        key_data = {
            "messages": messages,
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _is_retryable_error(self, error: Exception) -> bool:
        """Check if an error is retryable"""
        error_str = str(error).lower()

        # Non-retryable errors
        if any(term in error_str for term in ["invalid", "unauthorized", "forbidden", "quota"]):
            return False

        # Retryable errors
        if any(term in error_str for term in ["timeout", "connection", "rate limit", "503", "502", "500"]):
            return True

        return True  # Default to retryable

    async def _update_usage_tracking(self, provider: APIProvider, result: Dict[str, Any]):
        """Update usage tracking for a provider"""
        if provider not in self.usage_tracker:
            return

        usage = self.usage_tracker[provider]
        tokens_used = result.get("usage", {}).get("total_tokens", 0)

        # Update counters
        usage["total_requests"] += 1
        usage["total_tokens"] += tokens_used
        usage["daily_requests"] += 1
        usage["daily_tokens"] += tokens_used

        # Reset daily counters if needed
        today = datetime.now().date()
        if usage["last_reset"] != today:
            usage["daily_requests"] = 1
            usage["daily_tokens"] = tokens_used
            usage["daily_cost"] = 0.0
            usage["last_reset"] = today

        # Add to history (keep last 100)
        usage["request_history"].append({
            "timestamp": datetime.now().isoformat(),
            "tokens": tokens_used,
            "model": result.get("model"),
            "success": result.get("success", True)
        })

        if len(usage["request_history"]) > 100:
            usage["request_history"] = usage["request_history"][-100:]

    async def _update_performance_metrics(self, provider: APIProvider, response_time: float, success: bool):
        """Update performance metrics for a provider"""
        if provider not in self.performance_metrics:
            self.performance_metrics[provider] = {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "average_response_time": 0.0,
                "response_times": []
            }

        metrics = self.performance_metrics[provider]
        metrics["total_requests"] += 1

        if success:
            metrics["successful_requests"] += 1
        else:
            metrics["failed_requests"] += 1

        # Update response times (keep last 100)
        metrics["response_times"].append(response_time)
        if len(metrics["response_times"]) > 100:
            metrics["response_times"] = metrics["response_times"][-100:]

        # Update average
        metrics["average_response_time"] = sum(metrics["response_times"]) / len(metrics["response_times"])

        # Update provider status
        if provider in self.provider_status:
            status = self.provider_status[provider]
            status["last_check"] = datetime.now().isoformat()
            status["average_response_time"] = metrics["average_response_time"]

            if success:
                status["success_count"] += 1
                status["available"] = True
                status["last_error"] = None
            else:
                status["error_count"] += 1
                if status["error_count"] > 5:  # Mark as unavailable after 5 consecutive errors
                    status["available"] = False
    
    def _setup_rate_limiters(self):
        """Setup rate limiters for each provider"""
        for provider, config in self.providers.items():
            self.rate_limiters[provider] = RateLimiter(
                max_requests=config.rate_limit_rpm,
                time_window=60
            )
    
    async def _wait_for_rate_limit(self, provider: APIProvider):
        """Wait if rate limit is exceeded"""
        rate_limiter = self.rate_limiters.get(provider)
        if rate_limiter and not rate_limiter.can_make_request():
            wait_time = rate_limiter.time_until_next_request()
            logger.info(f"Rate limit reached for {provider.value}, waiting {wait_time:.1f}s")
            await asyncio.sleep(wait_time)
    
    async def _make_request_with_retry(
        self, 
        provider: APIProvider, 
        request_func: Callable,
        *args, 
        **kwargs
    ) -> Any:
        """Make API request with exponential backoff retry"""
        config = self.providers[provider]
        
        for attempt in range(config.max_retries + 1):
            try:
                # Check rate limit
                await self._wait_for_rate_limit(provider)
                
                # Record request
                self.rate_limiters[provider].record_request()
                
                # Make request
                result = await request_func(*args, **kwargs)
                return result
                
            except Exception as e:
                error_msg = str(e).lower()
                
                # Check for quota exceeded
                if "quota" in error_msg or "insufficient_quota" in error_msg:
                    raise QuotaExceededError(provider)
                
                # Check for rate limit
                if "rate" in error_msg and "limit" in error_msg:
                    retry_after = 60  # Default retry after 60 seconds
                    raise RateLimitError(provider, retry_after)
                
                # If this is the last attempt, raise the error
                if attempt == config.max_retries:
                    raise APIError(f"API request failed after {config.max_retries} retries: {e}", provider)
                
                # Exponential backoff
                wait_time = config.retry_delay * (2 ** attempt)
                logger.warning(f"API request failed (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                await asyncio.sleep(wait_time)
        
        raise APIError(f"API request failed after all retries", provider)

    async def generate_text(
        self, 
        messages: List[Dict[str, str]], 
        preferred_provider: Optional[APIProvider] = None,
        **kwargs
    ) -> str:
        """Generate text using the best available provider"""
        
        # Determine provider order
        providers_to_try = []
        if preferred_provider and preferred_provider in self.providers:
            providers_to_try.append(preferred_provider)
        
        # Add fallback providers
        for provider in self.fallback_order:
            if provider not in providers_to_try:
                providers_to_try.append(provider)
        
        if not providers_to_try:
            raise APIError("No API providers available", APIProvider.OPENAI, False)
        
        last_error = None
        
        for provider in providers_to_try:
            try:
                logger.debug(f"Attempting text generation with {provider.value}")
                result = await self._generate_with_provider(provider, messages, **kwargs)
                logger.info(f"Successfully generated text with {provider.value}")
                return result
                
            except QuotaExceededError as e:
                logger.warning(f"Quota exceeded for {provider.value}, trying next provider")
                last_error = e
                continue
                
            except RateLimitError as e:
                logger.warning(f"Rate limit exceeded for {provider.value}, trying next provider")
                last_error = e
                continue
                
            except APIError as e:
                if not e.retryable:
                    logger.error(f"Non-retryable error with {provider.value}: {e}")
                    last_error = e
                    continue
                else:
                    logger.warning(f"Retryable error with {provider.value}: {e}")
                    last_error = e
                    continue
        
        # If we get here, all providers failed
        if last_error:
            raise last_error
        else:
            raise APIError("All API providers failed", APIProvider.OPENAI, False)

    async def _generate_with_provider(
        self, 
        provider: APIProvider, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate text with a specific provider"""
        config = self.providers[provider]
        client = self.clients[provider]
        
        # Merge kwargs with config defaults
        generation_params = {
            "max_tokens": kwargs.get("max_tokens", config.max_tokens),
            "temperature": kwargs.get("temperature", config.temperature),
        }
        
        if provider == APIProvider.OPENAI:
            return await self._generate_openai(client, config, messages, generation_params)
        elif provider == APIProvider.ANTHROPIC:
            return await self._generate_anthropic(client, config, messages, generation_params)
        elif provider == APIProvider.AZURE:
            return await self._generate_azure(client, config, messages, generation_params)
        elif provider == APIProvider.OPENROUTER:
            return await self._generate_openrouter(client, config, messages, generation_params)
        elif provider == APIProvider.LOCAL:
            return await self._generate_local(client, config, messages, generation_params)
        else:
            raise APIError(f"Unsupported provider: {provider.value}", provider, False)

    async def _generate_openai(self, client: AsyncOpenAI, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using OpenAI API"""
        async def request_func():
            response = await client.chat.completions.create(
                model=config.model,
                messages=messages,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"]
            )
            return response.choices[0].message.content
        
        return await self._make_request_with_retry(APIProvider.OPENAI, request_func)

    async def _generate_anthropic(self, client: AsyncAnthropic, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using Anthropic API"""
        async def request_func():
            # Convert messages format for Anthropic
            system_message = ""
            user_messages = []
            
            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    user_messages.append(msg)
            
            response = await client.messages.create(
                model=config.model,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"],
                system=system_message,
                messages=user_messages
            )
            return response.content[0].text
        
        return await self._make_request_with_retry(APIProvider.ANTHROPIC, request_func)

    async def _generate_azure(self, client, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using Azure OpenAI API"""
        async def request_func():
            response = await client.chat.completions.create(
                model=config.deployment_name or config.model,
                messages=messages,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"]
            )
            return response.choices[0].message.content

        return await self._make_request_with_retry(APIProvider.AZURE, request_func)

    async def _generate_openrouter(self, client: AsyncOpenAI, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using OpenRouter API"""
        async def request_func():
            response = await client.chat.completions.create(
                model=config.model,
                messages=messages,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"]
            )
            return response.choices[0].message.content

        return await self._make_request_with_retry(APIProvider.OPENROUTER, request_func)

    async def _generate_local(self, client: aiohttp.ClientSession, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using local API (Ollama)"""
        async def request_func():
            # Convert to Ollama format
            prompt = "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
            
            payload = {
                "model": config.model,
                "prompt": prompt,
                "options": {
                    "num_predict": params["max_tokens"],
                    "temperature": params["temperature"]
                }
            }
            
            async with client.post(f"{config.base_url}/api/generate", json=payload) as response:
                if response.status != 200:
                    raise Exception(f"Local API error: {response.status}")
                
                result = await response.json()
                return result.get("response", "")
        
        return await self._make_request_with_retry(APIProvider.LOCAL, request_func)

    async def close(self):
        """Close all API clients"""
        for provider, client in self.clients.items():
            try:
                if hasattr(client, 'close'):
                    await client.close()
            except Exception as e:
                logger.warning(f"Error closing client for {provider.value}: {e}")

    def get_available_providers(self) -> List[APIProvider]:
        """Get list of available providers"""
        return list(self.providers.keys())

    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all providers"""
        status = {}
        for provider, config in self.providers.items():
            rate_limiter = self.rate_limiters[provider]
            status[provider.value] = {
                "configured": True,
                "model": config.model,
                "rate_limit_remaining": config.rate_limit_rpm - len(rate_limiter.requests),
                "can_make_request": rate_limiter.can_make_request()
            }
        return status

# Global API manager instance
_api_manager: Optional[APIManager] = None

def get_api_manager() -> APIManager:
    """Get global API manager instance"""
    global _api_manager
    if _api_manager is None:
        _api_manager = APIManager()
    return _api_manager

async def generate_text(messages: List[Dict[str, str]], **kwargs) -> str:
    """Convenience function for text generation"""
    api_manager = get_api_manager()
    return await api_manager.generate_text(messages, **kwargs)
