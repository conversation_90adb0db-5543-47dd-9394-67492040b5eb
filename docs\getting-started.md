# Getting Started with Aetherforge

Welcome to Aetherforge! This guide will help you get up and running with the autonomous AI software creation system in just a few minutes.

## 🎯 What You'll Learn

By the end of this guide, you'll:
- Have Aetherforge installed and configured
- Understand the basic concepts and workflow
- Create your first AI-generated project
- Know where to go for more advanced features

## 📋 Prerequisites

Before you begin, ensure you have:

- **Python 3.9+** installed on your system
- **Node.js 16+** (for web projects)
- **Git** for version control
- **AI API Key** - Choose from:
  - **OpenAI API Key** (recommended for best performance)
  - **OpenRouter API Key** (access to multiple AI models)
  - **Anthropic API Key** (Claude models)
  - **Azure OpenAI** (enterprise deployment)
- **VS Code** (recommended for the best experience)

## 🚀 Quick Installation

### Option 1: Using pip (Recommended)

```bash
# Install Aetherforge
pip install aetherforge

# Verify installation
aetherforge --version
```

### Option 2: From Source

```bash
# Clone the repository
git clone https://github.com/aetherforge/aetherforge.git
cd aetherforge

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

### Option 3: Using Docker

```bash
# Pull the official image
docker pull aetherforge/aetherforge:latest

# Run Aetherforge
docker run -p 8000:8000 -e OPENAI_API_KEY=your_key_here aetherforge/aetherforge:latest
```

## ⚙️ Initial Configuration

### 1. Set Up Your API Key

Create a `.env` file in your working directory with your preferred AI provider:

```bash
# .env - Choose one or more providers

# OpenAI (recommended for best performance)
OPENAI_API_KEY=your_openai_api_key_here

# OpenRouter (access to multiple AI models)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Anthropic (Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Azure OpenAI (enterprise)
AZURE_OPENAI_API_KEY=your_azure_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/

# System Configuration
AETHERFORGE_LOG_LEVEL=info
AETHERFORGE_PROJECTS_DIR=./projects
```

**Getting API Keys:**
- **OpenAI**: Visit [platform.openai.com/api-keys](https://platform.openai.com/api-keys)
- **OpenRouter**: Visit [openrouter.ai/keys](https://openrouter.ai/keys)
- **Anthropic**: Visit [console.anthropic.com](https://console.anthropic.com/)
- **Azure OpenAI**: Set up through [Azure Portal](https://portal.azure.com/)

### 2. Initialize Aetherforge

```bash
# Initialize configuration
aetherforge init

# Set up API keys interactively
aetherforge keys setup

# This creates:
# - ~/.aetherforge/config.json
# - ~/.aetherforge/workflows/
# - ~/.aetherforge/templates/
# - ~/.aetherforge/keys.enc (encrypted API keys)
```

**Alternative: Manual API Key Setup**
```bash
# Set specific provider keys
aetherforge keys set openai
aetherforge keys set openrouter
aetherforge keys set anthropic

# List configured providers
aetherforge keys list

# Test API connections
aetherforge keys test openai
```

### 3. Verify Setup

```bash
# Check system status
aetherforge status

# Expected output:
# ✅ Aetherforge v1.0.0
# ✅ API Providers: 2 configured (OpenAI, OpenRouter)
# ✅ Orchestrator: Running
# ✅ Pheromone System: Active
# ✅ VS Code Extension: Available

# Check API key status
aetherforge keys list
# Expected output:
# 🔑 API Key Status:
# OPENAI       | ✅ Active        | Model: gpt-4
# OPENROUTER   | ✅ Active        | Model: openai/gpt-3.5-turbo
# ANTHROPIC    | ❌ Not configured | Model: claude-3-sonnet-20240229
```

## 🎨 Install VS Code Extension (Optional but Recommended)

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Aetherforge"
4. Click "Install"
5. Reload VS Code

Or install from command line:
```bash
code --install-extension aetherforge.aetherforge-vscode
```

## 🏗️ Your First Project

Let's create a simple task management application to see Aetherforge in action.

### Method 1: Using the CLI

```bash
# Create a new project
aetherforge create "A simple task management app with user authentication"

# Follow the interactive prompts:
# Project name: TaskMaster
# Project type: fullstack
# Framework preference: React + Node.js
# Database: PostgreSQL
```

### Method 2: Using VS Code Extension

1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "Aetherforge: Create Project"
4. Enter your project description: "A simple task management app with user authentication"
5. Follow the prompts in the sidebar

### Method 3: Using the Web Interface

1. Start the Aetherforge server:
   ```bash
   aetherforge serve
   ```
2. Open http://localhost:8000 in your browser
3. Click "Create New Project"
4. Fill in the project details
5. Click "Generate Project"

## 📊 Understanding the Process

Aetherforge follows the BMAD methodology:

### 1. **Business Analysis** (Analyst Agent)
- Analyzes your requirements
- Creates user stories and acceptance criteria
- Defines functional and non-functional requirements

### 2. **Model Design** (Architect Agent)
- Designs system architecture
- Selects appropriate technologies
- Creates API specifications and database schemas

### 3. **Architecture Implementation** (Developer Agent)
- Generates complete source code
- Creates configuration files
- Sets up project structure

### 4. **Development Validation** (QA Agent)
- Creates test suites
- Validates code quality
- Ensures requirements are met

## 🔍 Monitoring Progress

### Real-time Progress Tracking

```bash
# Watch project creation in real-time
aetherforge watch <project-id>

# View pheromone trail (agent communication)
aetherforge pheromones <project-id>

# Check agent status
aetherforge agents status
```

### VS Code Integration

The VS Code extension provides:
- **Real-time progress panel** showing agent activities
- **Pheromone trail visualization** for debugging
- **Agent chat interface** for direct communication
- **Project structure preview** as it's being created

## 📁 Project Structure

After creation, your project will have this structure:

```
TaskMaster/
├── .aetherforge.json          # Project metadata
├── README.md                  # Generated documentation
├── package.json               # Dependencies and scripts
├── .env.example              # Environment variables template
├── .gitignore                # Git ignore rules
├── docs/                     # Generated documentation
│   ├── requirements.md       # Business requirements
│   ├── architecture.md       # System design
│   ├── api-docs.md          # API documentation
│   └── deployment.md        # Deployment guide
├── client/                   # Frontend application
│   ├── src/
│   ├── public/
│   └── package.json
├── server/                   # Backend application
│   ├── src/
│   ├── tests/
│   └── package.json
├── database/                 # Database schemas
│   ├── migrations/
│   └── seeds/
└── tests/                    # Integration tests
    ├── unit/
    ├── integration/
    └── e2e/
```

## 🚀 Running Your Project

### Development Mode

```bash
cd TaskMaster

# Install dependencies
npm run install:all

# Start development servers
npm run dev

# This starts:
# - Frontend: http://localhost:3000
# - Backend: http://localhost:3001
# - Database: PostgreSQL on port 5432
```

### Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
```

### Using Docker

```bash
# Build and run with Docker Compose
docker-compose up --build

# Access your application at http://localhost:3000
```

## 🎯 Next Steps

Congratulations! You've successfully created your first Aetherforge project. Here's what to explore next:

### 📚 Learn More
- **[Developer Guide](user-guides/developers-guide.md)** - Advanced features and customization
- **[API Documentation](api/README.md)** - Integrate Aetherforge into your workflow
- **[Project Types](examples/project-types/README.md)** - Explore different application types

### 🛠️ Customize Your Workflow
- **[Configuration Guide](configuration/README.md)** - Customize agents and workflows
- **[Templates](guides/templates.md)** - Create reusable project templates
- **[Integrations](examples/integrations/README.md)** - Connect with existing tools

### 🎨 Advanced Features
- **[Team Collaboration](user-guides/team-collaboration.md)** - Work with your team
- **[Custom Agents](technical/custom-agents.md)** - Create specialized agents
- **[Workflow Customization](technical/workflow-engine.md)** - Modify the BMAD process

## 🆘 Need Help?

### Common Issues

**Project creation fails:**
```bash
# Check system status
aetherforge status

# View logs
aetherforge logs --tail 50

# Reset configuration
aetherforge reset --config
```

**VS Code extension not working:**
1. Ensure Aetherforge server is running: `aetherforge serve`
2. Check extension settings: `Ctrl+,` → Search "Aetherforge"
3. Reload VS Code: `Ctrl+Shift+P` → "Developer: Reload Window"

**API key issues:**
```bash
# Test API connection
aetherforge test-api

# Update API key
aetherforge config set OPENAI_API_KEY your_new_key
```

### Getting Support

- **[FAQ](support/faq.md)** - Quick answers to common questions
- **[Troubleshooting Guide](support/troubleshooting.md)** - Detailed problem-solving
- **[Community Forum](support/community.md)** - Connect with other users
- **[GitHub Issues](https://github.com/aetherforge/aetherforge/issues)** - Report bugs

## 🎉 You're Ready!

You now have Aetherforge set up and have created your first AI-generated project. The system is designed to learn from your preferences and improve over time.

**Pro Tip:** The more specific and detailed your project descriptions, the better Aetherforge can understand and implement your vision.

Ready to dive deeper? Check out our [Developer Guide](user-guides/developers-guide.md) for advanced features and customization options.
